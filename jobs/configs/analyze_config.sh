#!/bin/bash

# =============================================================================
# 分析任务参数配置文件
# =============================================================================

# 定义要分析的参数组合
# 格式: "L J2 J1"
PARAM_SETS=(
  "5 0.00 0.10"
)

echo "分析参数配置:"
echo "参数组合:"
for params in "${PARAM_SETS[@]}"; do
    echo "  - $params"
done

# 并行任务最大数量
MAX_ANALYSIS_TASKS=1

echo "最大并行分析任务数: $MAX_ANALYSIS_TASKS"

# ==================== 任务执行函数 ====================
execute_analyze_task() {
    echo "==================== 开始分析任务 ===================="
    
    # 并行任务计数器
    current_tasks=0

    # 遍历所有参数组合并运行分析
    for params in "${PARAM_SETS[@]}"; do
        # 提取参数
        read -r L J2 J1 <<< "$params"

        echo "Starting analysis: L=$L, J2=$J2, J1=$J1 at: $(date)"

        # 获取checkpoint目录
        checkpoint_dir="results/L=$L/J2=$J2/J1=$J1/training/checkpoints"

        # 检查checkpoint目录是否存在
        if [ ! -d "$checkpoint_dir" ]; then
            echo "Warning: Checkpoint directory $checkpoint_dir does not exist, skipping..."
            continue
        fi

        # 获取所有checkpoint文件
        checkpoint_files=($(find "$checkpoint_dir" -name "*.pkl" | sort))

        if [ ${#checkpoint_files[@]} -eq 0 ]; then
            echo "Warning: No checkpoint files found in $checkpoint_dir, skipping..."
            continue
        fi

        echo "Found ${#checkpoint_files[@]} checkpoint files for L=$L, J2=$J2, J1=$J1"

        # 为每个checkpoint运行分析
        for checkpoint_file in "${checkpoint_files[@]}"; do
            # 提取checkpoint名称（不含路径和扩展名）
            checkpoint_name=$(basename "$checkpoint_file" .pkl)

            echo "  Processing checkpoint: $checkpoint_name"

            # 运行分析脚本（后台运行）
            python scripts/analyze.py --L $L --J2 $J2 --J1 $J1 --checkpoint "$checkpoint_name" &

            current_tasks=$((current_tasks + 1))

            # 如果达到最大并行任务数，则等待这批任务全部结束，再继续提交
            if [ $current_tasks -ge $MAX_ANALYSIS_TASKS ]; then
                wait
                current_tasks=0
                echo "Batch of analysis tasks completed at: $(date)"
            fi
        done

        echo "Submitted analysis jobs for L=$L, J2=$J2, J1=$J1 (${#checkpoint_files[@]} checkpoints)"
        echo "---------------------------------------"
    done

    # 等待剩余任务
    wait

    # 整理结果
    echo "Organizing results..."

    # 记录磁盘使用情况
    echo "Disk usage for results:"
    du -sh results/

    echo "==================== 分析任务完成 ===================="
    echo "所有分析任务已完成！"
}
