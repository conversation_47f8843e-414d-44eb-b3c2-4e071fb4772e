#!/bin/bash

# =============================================================================
# 总的PBS作业提交脚本
# 功能：可以选择提交库里所有PBS文件并行到同一个GPU上
# =============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -l, --list              列出所有可用的PBS文件
    -a, --all               提交所有PBS文件到同一个GPU并行
    -s, --select            交互式选择要提交的PBS文件
    -f, --files FILE1,FILE2 指定要提交的PBS文件（逗号分隔）
    -d, --dry-run           仅显示将要执行的命令，不实际提交
    -q, --queue QUEUE       指定队列名称（默认：gpu_h200_pinaki）
    -g, --gpu-count N       指定GPU数量（默认：1）
    -t, --walltime TIME     指定作业时间限制（默认：1440:00:00）
    -p, --project PROJECT   指定项目名称（默认：gs_spms_psengupta）

示例:
    $0 -l                                    # 列出所有PBS文件
    $0 -a                                    # 提交所有PBS文件并行
    $0 -s                                    # 交互式选择PBS文件
    $0 -f train.pbs,chain_of_train.pbs      # 提交指定的PBS文件
    $0 -a -d                                 # 干运行模式，显示命令但不执行
    $0 -a -q gpu_v100 -g 2                  # 使用不同队列和GPU数量

EOF
}

# 获取所有PBS文件
get_pbs_files() {
    find "$SCRIPT_DIR" -name "*.pbs" -type f | sort
}

# 列出所有PBS文件
list_pbs_files() {
    print_info "可用的PBS文件："
    local files=($(get_pbs_files))
    for i in "${!files[@]}"; do
        local file="${files[$i]}"
        local basename=$(basename "$file")
        echo "  $((i+1)). $basename"
        
        # 显示PBS文件的基本信息
        local job_name=$(grep "^#PBS -N" "$file" | cut -d' ' -f3)
        local queue=$(grep "^#PBS -q" "$file" | cut -d' ' -f3)
        local walltime=$(grep "^#PBS -l walltime" "$file" | cut -d'=' -f2)
        
        echo "     作业名: ${job_name:-未指定}"
        echo "     队列: ${queue:-未指定}"
        echo "     时间限制: ${walltime:-未指定}"
        echo ""
    done
}

# 交互式选择PBS文件
interactive_select() {
    local files=($(get_pbs_files))
    
    if [ ${#files[@]} -eq 0 ]; then
        print_error "没有找到PBS文件"
        return 1
    fi
    
    print_info "请选择要提交的PBS文件（可多选，用空格分隔）："
    list_pbs_files
    
    echo -n "请输入选择的编号（例如：1 3 5）: "
    read -r selections
    
    local selected_files=()
    for selection in $selections; do
        if [[ "$selection" =~ ^[0-9]+$ ]] && [ "$selection" -ge 1 ] && [ "$selection" -le ${#files[@]} ]; then
            selected_files+=("${files[$((selection-1))]}")
        else
            print_warning "无效选择: $selection"
        fi
    done
    
    if [ ${#selected_files[@]} -eq 0 ]; then
        print_error "没有选择有效的PBS文件"
        return 1
    fi
    
    echo "${selected_files[@]}"
}

# 创建并行提交的PBS脚本
create_parallel_pbs() {
    local pbs_files=("$@")
    local parallel_script="$SCRIPT_DIR/parallel_submit_$(date +%Y%m%d_%H%M%S).pbs"
    
    cat > "$parallel_script" << EOF
#!/bin/sh

#PBS -q $QUEUE
#PBS -l select=1:ngpus=$GPU_COUNT
#PBS -l walltime=$WALLTIME
#PBS -P $PROJECT
#PBS -N parallel-jobs-$(date +%Y%m%d-%H%M%S)
#PBS -j oe

# 进入工作目录
cd \$PBS_O_WORKDIR || exit \$?

# 记录作业开始时间和节点信息
echo "Parallel jobs start at: \$(date)"
echo "Running on node: \$(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load anaconda2025/2025
module unload cuda/12.2 2>/dev/null || true

# 初始化conda并激活环境
eval "\$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate netket

# 设置GPU设备
export CUDA_VISIBLE_DEVICES=0
echo "Using GPU device: \$CUDA_VISIBLE_DEVICES"

# 验证环境
echo "Python path: \$(which python)"
echo "Python version: \$(python --version)"
echo "Current conda environment: \$CONDA_DEFAULT_ENV"

echo "==================== 开始并行执行PBS作业 ===================="

# 存储后台进程PID
pids=()

EOF

    # 为每个PBS文件添加执行逻辑
    for pbs_file in "${pbs_files[@]}"; do
        local basename=$(basename "$pbs_file" .pbs)
        cat >> "$parallel_script" << EOF

# 执行 $basename
echo "启动作业: $basename at \$(date)"
(
    echo "[$basename] 开始执行 at \$(date)"
    
    # 提取并执行PBS文件中的主要逻辑（跳过PBS指令和模块加载部分）
    # 这里我们需要手动提取每个PBS文件的核心逻辑
    
EOF

        # 根据不同的PBS文件类型添加相应的执行逻辑
        if [[ "$basename" == "train" ]]; then
            cat >> "$parallel_script" << 'EOF'
    # train.pbs 的核心逻辑
    L_VALUES="5"
    J2_VALUES="0.00"
    J1_VALUES="0.04"
    LEARNING_RATE=0.015
    N_SAMPLES=16384
    CHUNK_SIZE=2048
    N_CYCLES=4
    INITIAL_PERIOD=150
    PERIOD_MULT=2.0
    MAX_TEMPERATURE=1.0
    MIN_TEMPERATURE=0.0
    NUM_FEATURES=4
    NUM_LAYERS=4
    DIAG_SHIFT=0.20
    GRAD_CLIP=1.0
    ENABLE_CHECKPOINT=true
    CHECKPOINT_INTERVAL=250
    KEEP_CHECKPOINT_HISTORY=true
    
    CHECKPOINT_ARGS="--enable_checkpoint --save_interval $CHECKPOINT_INTERVAL --keep_history"
    
    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            for J1 in $J1_VALUES; do
                echo "Starting computation L=$L, J2=$J2, J1=$J1 at: $(date)"
                python scripts/train.py $L $J2 $J1 \
                    --learning_rate $LEARNING_RATE \
                    --n_samples $N_SAMPLES \
                    --chunk_size $CHUNK_SIZE \
                    --n_cycles $N_CYCLES \
                    --initial_period $INITIAL_PERIOD \
                    --period_mult $PERIOD_MULT \
                    --max_temperature $MAX_TEMPERATURE \
                    --min_temperature $MIN_TEMPERATURE \
                    --num_features $NUM_FEATURES \
                    --num_layers $NUM_LAYERS \
                    --diag_shift $DIAG_SHIFT \
                    --grad_clip $GRAD_CLIP \
                    $CHECKPOINT_ARGS
                echo "Completed computation L=$L, J2=$J2, J1=$J1 at: $(date)"
            done
        done
    done
EOF
        elif [[ "$basename" == "chain_of_train" ]]; then
            cat >> "$parallel_script" << 'EOF'
    # chain_of_train.pbs 的核心逻辑
    L_VALUES="5"
    J2_VALUES="0.00"
    START_J1="0.09"
    J1_LEFT_BOUND="0.10"
    J1_RIGHT_BOUND="0.10"
    J1_STEP="0.01"
    LEARNING_RATE=0.015
    N_SAMPLES=4096
    CHUNK_SIZE=2048
    N_CYCLES=2
    INITIAL_PERIOD=150
    PERIOD_MULT=2.0
    MAX_TEMPERATURE=1.0
    MIN_TEMPERATURE=0.0
    NUM_FEATURES=4
    NUM_LAYERS=4
    DIAG_SHIFT=0.20
    GRAD_CLIP=1.0
    ENABLE_CHECKPOINT=true
    CHECKPOINT_INTERVAL=50
    KEEP_CHECKPOINT_HISTORY=true
    
    CHECKPOINT_ARGS="--enable_checkpoint --save_interval $CHECKPOINT_INTERVAL --keep_history"
    
    # 这里简化链式微调逻辑，实际执行时会更复杂
    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            echo "Starting chain fine-tuning for L=$L, J2=$J2"
            # 简化版本：只执行一个微调步骤作为示例
            if [ -f "results/L=$L/J2=$J2/J1=$START_J1/training/checkpoints/final_GCNN.pkl" ]; then
                python scripts/train.py $L $J2 $J1_LEFT_BOUND \
                    --learning_rate $LEARNING_RATE \
                    --n_samples $N_SAMPLES \
                    --chunk_size $CHUNK_SIZE \
                    --n_cycles $N_CYCLES \
                    --initial_period $INITIAL_PERIOD \
                    --period_mult $PERIOD_MULT \
                    --max_temperature $MAX_TEMPERATURE \
                    --min_temperature $MIN_TEMPERATURE \
                    --num_features $NUM_FEATURES \
                    --num_layers $NUM_LAYERS \
                    --diag_shift $DIAG_SHIFT \
                    --grad_clip $GRAD_CLIP \
                    --resume_from "results/L=$L/J2=$J2/J1=$START_J1/training/checkpoints/final_GCNN.pkl" \
                    $CHECKPOINT_ARGS
            else
                echo "Warning: Starting checkpoint not found for chain training"
            fi
        done
    done
EOF
        elif [[ "$basename" == "analyze" ]]; then
            cat >> "$parallel_script" << 'EOF'
    # analyze.pbs 的核心逻辑
    PARAM_SETS=("5 0.00 0.10")
    
    for params in "${PARAM_SETS[@]}"; do
        read -r L J2 J1 <<< "$params"
        echo "Starting analysis: L=$L, J2=$J2, J1=$J1 at: $(date)"
        
        checkpoint_dir="results/L=$L/J2=$J2/J1=$J1/training/checkpoints"
        
        if [ -d "$checkpoint_dir" ]; then
            checkpoint_files=($(find "$checkpoint_dir" -name "*.pkl" | sort))
            for checkpoint_file in "${checkpoint_files[@]}"; do
                checkpoint_name=$(basename "$checkpoint_file" .pkl)
                echo "Processing checkpoint: $checkpoint_name"
                python scripts/analyze.py --L $L --J2 $J2 --J1 $J1 --checkpoint "$checkpoint_name"
            done
        else
            echo "Warning: Checkpoint directory $checkpoint_dir does not exist"
        fi
    done
EOF
        fi
        
        cat >> "$parallel_script" << EOF
    
    echo "[$basename] 执行完成 at \$(date)"
) &

# 记录后台进程PID
pids+=(\$!)
echo "[$basename] 后台进程PID: \$!"

EOF
    done

    cat >> "$parallel_script" << EOF

# 等待所有后台进程完成
echo "等待所有并行作业完成..."
for pid in "\${pids[@]}"; do
    wait \$pid
    echo "进程 PID \$pid 已完成"
done

echo "==================== 所有并行作业完成 ===================="
echo "Parallel jobs finished at: \$(date)"
EOF

    echo "$parallel_script"
}

# 提交PBS作业
submit_pbs() {
    local pbs_file="$1"

    if [ "$DRY_RUN" = true ]; then
        print_info "干运行模式 - 将要执行的命令："
        echo "qsub $pbs_file"
        return 0
    fi

    print_info "提交PBS作业: $(basename "$pbs_file")"
    local job_id=$(qsub "$pbs_file")

    if [ $? -eq 0 ]; then
        print_success "作业提交成功，作业ID: $job_id"
        echo "$job_id"
    else
        print_error "作业提交失败: $pbs_file"
        return 1
    fi
}

# 检查作业状态
check_job_status() {
    local job_ids=("$@")

    if [ ${#job_ids[@]} -eq 0 ]; then
        return 0
    fi

    print_info "检查作业状态..."
    for job_id in "${job_ids[@]}"; do
        if command -v qstat >/dev/null 2>&1; then
            local status=$(qstat "$job_id" 2>/dev/null | tail -n +3 | awk '{print $5}')
            if [ -n "$status" ]; then
                echo "作业 $job_id 状态: $status"
            else
                echo "作业 $job_id: 未找到或已完成"
            fi
        else
            print_warning "qstat命令不可用，无法检查作业状态"
            break
        fi
    done
}

# 主函数
main() {
    # 默认参数
    local action=""
    local selected_files=()
    local file_list=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -l|--list)
                action="list"
                shift
                ;;
            -a|--all)
                action="all"
                shift
                ;;
            -s|--select)
                action="select"
                shift
                ;;
            -f|--files)
                action="files"
                file_list="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -q|--queue)
                QUEUE="$2"
                shift 2
                ;;
            -g|--gpu-count)
                GPU_COUNT="$2"
                shift 2
                ;;
            -t|--walltime)
                WALLTIME="$2"
                shift 2
                ;;
            -p|--project)
                PROJECT="$2"
                shift 2
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 如果没有指定动作，显示帮助
    if [ -z "$action" ]; then
        show_help
        exit 0
    fi

    # 执行相应的动作
    case $action in
        list)
            list_pbs_files
            ;;
        all)
            selected_files=($(get_pbs_files))
            if [ ${#selected_files[@]} -eq 0 ]; then
                print_error "没有找到PBS文件"
                exit 1
            fi
            ;;
        select)
            mapfile -t selected_files < <(interactive_select)
            if [ $? -ne 0 ]; then
                exit 1
            fi
            ;;
        files)
            IFS=',' read -ra file_array <<< "$file_list"
            for file in "${file_array[@]}"; do
                local full_path="$SCRIPT_DIR/$file"
                if [ -f "$full_path" ]; then
                    selected_files+=("$full_path")
                else
                    print_warning "文件不存在: $file"
                fi
            done

            if [ ${#selected_files[@]} -eq 0 ]; then
                print_error "没有找到有效的PBS文件"
                exit 1
            fi
            ;;
    esac

    # 如果是列出文件，直接返回
    if [ "$action" = "list" ]; then
        return 0
    fi

    # 显示将要提交的文件
    print_info "将要提交的PBS文件："
    for file in "${selected_files[@]}"; do
        echo "  - $(basename "$file")"
    done

    # 确认提交
    if [ "$DRY_RUN" != true ]; then
        echo -n "确认提交这些作业到同一个GPU并行运行？(y/N): "
        read -r confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            print_info "取消提交"
            exit 0
        fi
    fi

    # 创建并行PBS脚本
    print_info "创建并行提交脚本..."
    local parallel_script=$(create_parallel_pbs "${selected_files[@]}")

    if [ "$DRY_RUN" = true ]; then
        print_info "生成的并行脚本: $parallel_script"
        print_info "脚本内容预览:"
        head -n 50 "$parallel_script"
        echo "..."
        return 0
    fi

    # 提交并行作业
    local job_id=$(submit_pbs "$parallel_script")

    if [ $? -eq 0 ]; then
        print_success "并行作业提交成功！"
        echo "作业ID: $job_id"
        echo "并行脚本: $parallel_script"

        # 等待一会儿再检查状态
        sleep 2
        check_job_status "$job_id"

        print_info "使用以下命令监控作业状态:"
        echo "  qstat $job_id"
        echo "  watch -n 5 'qstat $job_id'"

        print_info "查看作业输出:"
        echo "  tail -f parallel-jobs-*.o*"
    else
        print_error "并行作业提交失败"
        exit 1
    fi
}

# 设置默认值
DRY_RUN=false
QUEUE="gpu_h200_pinaki"
GPU_COUNT="1"
WALLTIME="1440:00:00"
PROJECT="gs_spms_psengupta"

# 检查是否在正确的目录
if [ ! -d "$SCRIPT_DIR" ]; then
    print_error "无法找到jobs目录"
    exit 1
fi

# 运行主函数
main "$@"
