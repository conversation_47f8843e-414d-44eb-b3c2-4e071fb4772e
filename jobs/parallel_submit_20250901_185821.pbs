#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1440:00:00
#PBS -P gs_spms_psengupta
#PBS -N parallel-jobs-20250901-185821
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Parallel jobs start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load anaconda2025/2025
module unload cuda/12.2 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate netket

# 设置GPU设备
export CUDA_VISIBLE_DEVICES=0
echo "Using GPU device: $CUDA_VISIBLE_DEVICES"

# 验证环境
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

echo "==================== 开始并行执行PBS作业 ===================="

# 存储后台进程PID
pids=()


# 执行 train
echo "启动作业: train at $(date)"
(
    echo "[train] 开始执行 at $(date)"
    
    # 提取并执行PBS文件中的主要逻辑（跳过PBS指令和模块加载部分）
    # 这里我们需要手动提取每个PBS文件的核心逻辑
    
    # train.pbs 的核心逻辑
    L_VALUES="5"
    J2_VALUES="0.00"
    J1_VALUES="0.04"
    LEARNING_RATE=0.015
    N_SAMPLES=16384
    CHUNK_SIZE=2048
    N_CYCLES=4
    INITIAL_PERIOD=150
    PERIOD_MULT=2.0
    MAX_TEMPERATURE=1.0
    MIN_TEMPERATURE=0.0
    NUM_FEATURES=4
    NUM_LAYERS=4
    DIAG_SHIFT=0.20
    GRAD_CLIP=1.0
    ENABLE_CHECKPOINT=true
    CHECKPOINT_INTERVAL=250
    KEEP_CHECKPOINT_HISTORY=true
    
    CHECKPOINT_ARGS="--enable_checkpoint --save_interval $CHECKPOINT_INTERVAL --keep_history"
    
    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            for J1 in $J1_VALUES; do
                echo "Starting computation L=$L, J2=$J2, J1=$J1 at: $(date)"
                python scripts/train.py $L $J2 $J1 \
                    --learning_rate $LEARNING_RATE \
                    --n_samples $N_SAMPLES \
                    --chunk_size $CHUNK_SIZE \
                    --n_cycles $N_CYCLES \
                    --initial_period $INITIAL_PERIOD \
                    --period_mult $PERIOD_MULT \
                    --max_temperature $MAX_TEMPERATURE \
                    --min_temperature $MIN_TEMPERATURE \
                    --num_features $NUM_FEATURES \
                    --num_layers $NUM_LAYERS \
                    --diag_shift $DIAG_SHIFT \
                    --grad_clip $GRAD_CLIP \
                    $CHECKPOINT_ARGS
                echo "Completed computation L=$L, J2=$J2, J1=$J1 at: $(date)"
            done
        done
    done
    
    echo "[train] 执行完成 at $(date)"
) &

# 记录后台进程PID
pids+=($!)
echo "[train] 后台进程PID: $!"


# 执行 chain_of_train
echo "启动作业: chain_of_train at $(date)"
(
    echo "[chain_of_train] 开始执行 at $(date)"
    
    # 提取并执行PBS文件中的主要逻辑（跳过PBS指令和模块加载部分）
    # 这里我们需要手动提取每个PBS文件的核心逻辑
    
    # chain_of_train.pbs 的核心逻辑
    L_VALUES="5"
    J2_VALUES="0.00"
    START_J1="0.09"
    J1_LEFT_BOUND="0.10"
    J1_RIGHT_BOUND="0.10"
    J1_STEP="0.01"
    LEARNING_RATE=0.015
    N_SAMPLES=4096
    CHUNK_SIZE=2048
    N_CYCLES=2
    INITIAL_PERIOD=150
    PERIOD_MULT=2.0
    MAX_TEMPERATURE=1.0
    MIN_TEMPERATURE=0.0
    NUM_FEATURES=4
    NUM_LAYERS=4
    DIAG_SHIFT=0.20
    GRAD_CLIP=1.0
    ENABLE_CHECKPOINT=true
    CHECKPOINT_INTERVAL=50
    KEEP_CHECKPOINT_HISTORY=true
    
    CHECKPOINT_ARGS="--enable_checkpoint --save_interval $CHECKPOINT_INTERVAL --keep_history"
    
    # 这里简化链式微调逻辑，实际执行时会更复杂
    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            echo "Starting chain fine-tuning for L=$L, J2=$J2"
            # 简化版本：只执行一个微调步骤作为示例
            if [ -f "results/L=$L/J2=$J2/J1=$START_J1/training/checkpoints/final_GCNN.pkl" ]; then
                python scripts/train.py $L $J2 $J1_LEFT_BOUND \
                    --learning_rate $LEARNING_RATE \
                    --n_samples $N_SAMPLES \
                    --chunk_size $CHUNK_SIZE \
                    --n_cycles $N_CYCLES \
                    --initial_period $INITIAL_PERIOD \
                    --period_mult $PERIOD_MULT \
                    --max_temperature $MAX_TEMPERATURE \
                    --min_temperature $MIN_TEMPERATURE \
                    --num_features $NUM_FEATURES \
                    --num_layers $NUM_LAYERS \
                    --diag_shift $DIAG_SHIFT \
                    --grad_clip $GRAD_CLIP \
                    --resume_from "results/L=$L/J2=$J2/J1=$START_J1/training/checkpoints/final_GCNN.pkl" \
                    $CHECKPOINT_ARGS
            else
                echo "Warning: Starting checkpoint not found for chain training"
            fi
        done
    done
    
    echo "[chain_of_train] 执行完成 at $(date)"
) &

# 记录后台进程PID
pids+=($!)
echo "[chain_of_train] 后台进程PID: $!"


# 等待所有后台进程完成
echo "等待所有并行作业完成..."
for pid in "${pids[@]}"; do
    wait $pid
    echo "进程 PID $pid 已完成"
done

echo "==================== 所有并行作业完成 ===================="
echo "Parallel jobs finished at: $(date)"
