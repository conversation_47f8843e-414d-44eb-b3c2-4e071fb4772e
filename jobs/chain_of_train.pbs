#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1440:00:00
#PBS -P gs_spms_psengupta
#PBS -N ss-gcnn-chain
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load anaconda2025/2025
# 注意：anaconda2025会自动加载cuda/12.2作为依赖
# 如果需要其他CUDA版本，请先卸载cuda/12.2再加载所需版本
module unload cuda/12.2 2>/dev/null || true
# module load cuda/12.8

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate netket

# 设置GPU设备
export CUDA_VISIBLE_DEVICES=0
echo "Using GPU device: $CUDA_VISIBLE_DEVICES"

# 验证环境
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

echo "==================== Shastry-Sutherland GCNN 链式微调 ===================="

# ==================== 系统参数配置 ====================
# 晶格尺寸
L_VALUES="5"

# J2耦合强度
J2_VALUES="0.00"

# ==================== 链式微调参数配置 ====================
# 起始J1值 - 必须已经有训练好的checkpoint
START_J1="0.09"                 # 起始J1值（必须已经训练完成）

# J1扩张边界
J1_LEFT_BOUND="0.10"            # 左边界
J1_RIGHT_BOUND="0.10"           # 右边界

# 扩张步长
J1_STEP="0.01"                  # J1步长

echo "链式微调参数配置:"
echo "Start J1: $START_J1"
echo "J1 bounds: [$J1_LEFT_BOUND, $J1_RIGHT_BOUND]"
echo "J1 step: $J1_STEP"

# ==================== 训练参数配置 ====================
LEARNING_RATE=0.015             # 学习率
N_SAMPLES=4096                  # 样本数量
CHUNK_SIZE=2048                 # 批处理大小

# 退火参数（微调用较少迭代次数）
N_CYCLES=2                      # 退火周期数
INITIAL_PERIOD=150              # 初始周期长度
PERIOD_MULT=2.0                 # 周期倍数
MAX_TEMPERATURE=1.0             # 最大温度
MIN_TEMPERATURE=0.0             # 最小温度

# 模型参数
NUM_FEATURES=4                  # 特征维度
NUM_LAYERS=4                    # 编码器层数

# 其他参数
DIAG_SHIFT=0.20                 # 对角线位移
GRAD_CLIP=1.0                   # 梯度裁剪

echo "微调训练参数配置:"
echo "Learning rate: $LEARNING_RATE"
echo "Samples: $N_SAMPLES"
echo "Annealing cycles: $N_CYCLES"
echo "Initial period: $INITIAL_PERIOD"
echo "Period multiplier: $PERIOD_MULT"
echo "Temperature range: $MIN_TEMPERATURE - $MAX_TEMPERATURE"

# ==================== Checkpoint 配置 ====================
# 是否启用checkpoint保存 (true/false)
ENABLE_CHECKPOINT=true          # 是否启用checkpoint保存

# checkpoint保存间隔（迭代次数）
CHECKPOINT_INTERVAL=50          # checkpoint保存间隔（迭代次数）

# 是否保留历史checkpoint (true/false)
KEEP_CHECKPOINT_HISTORY=true    # 是否保留历史checkpoint (true/false)

# 构建checkpoint相关的命令行参数
CHECKPOINT_ARGS=""              # 构建checkpoint相关的命令行参数
if [ "$ENABLE_CHECKPOINT" = "true" ]; then
    CHECKPOINT_ARGS="--enable_checkpoint --save_interval $CHECKPOINT_INTERVAL"
    
    if [ "$KEEP_CHECKPOINT_HISTORY" = "true" ]; then
        CHECKPOINT_ARGS="$CHECKPOINT_ARGS --keep_history"
    fi
    
    echo "Checkpoint enabled with interval: $CHECKPOINT_INTERVAL"
else
    echo "Checkpoint disabled"
fi

# ==================== 函数定义 ====================
# 函数：检查checkpoint是否存在
check_checkpoint() {
    local L=$1
    local J2=$2
    local J1=$3
    local checkpoint_path="results/L=$L/J2=$J2/J1=$J1/training/checkpoints/final_GCNN.pkl"
    
    if [ -f "$checkpoint_path" ]; then
        echo "$checkpoint_path"
        return 0
    else
        return 1
    fi
}

# 函数：运行单个微调任务
run_finetune() {
    local L=$1
    local J2=$2
    local J1=$3
    local checkpoint_path=$4
    
    echo "Starting fine-tuning L=$L, J2=$J2, J1=$J1 from checkpoint: $checkpoint_path at: $(date)"
    
    # 运行微调
    python scripts/train.py $L $J2 $J1 \
        --learning_rate $LEARNING_RATE \
        --n_samples $N_SAMPLES \
        --chunk_size $CHUNK_SIZE \
        --n_cycles $N_CYCLES \
        --initial_period $INITIAL_PERIOD \
        --period_mult $PERIOD_MULT \
        --max_temperature $MAX_TEMPERATURE \
        --min_temperature $MIN_TEMPERATURE \
        --num_features $NUM_FEATURES \
        --num_layers $NUM_LAYERS \
        --diag_shift $DIAG_SHIFT \
        --grad_clip $GRAD_CLIP \
        --resume_from "$checkpoint_path" \
        $CHECKPOINT_ARGS
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo "Fine-tuning completed for L=$L, J2=$J2, J1=$J1 at: $(date)"
        return 0
    else
        echo "Fine-tuning failed for L=$L, J2=$J2, J1=$J1 at: $(date)"
        return 1
    fi
}

# 函数：生成J1扩张层次
generate_j1_layers() {
    local start=$1
    local left_bound=$2
    local right_bound=$3
    local step=$4
    
    # 使用Python生成分层序列
    python3 -c "
import numpy as np

start = float('$start')
left_bound = float('$left_bound')
right_bound = float('$right_bound')
step = float('$step')

# 生成向左的序列
left_seq = []
current = start - step
while current >= left_bound - 1e-10:  # 修改为减去容差，确保边界值被包含
    left_seq.append(current)
    current -= step

# 生成向右的序列
right_seq = []
current = start + step
while current <= right_bound + 1e-10:  # 添加小的容差避免浮点精度问题
    right_seq.append(current)
    current += step

# 按层次输出，每层包含左右两个点（如果存在）
max_len = max(len(left_seq), len(right_seq))
for i in range(max_len):
    layer = []
    if i < len(left_seq):
        layer.append(f'{left_seq[i]:.2f}')
    if i < len(right_seq):
        layer.append(f'{right_seq[i]:.2f}')
    
    if layer:
        print(' '.join(layer))
"
}

echo "==================== 开始链式微调 ===================="

for L in $L_VALUES; do
    for J2 in $J2_VALUES; do
        echo "Processing L=$L, J2=$J2"
        
        # 检查起始checkpoint是否存在
        start_checkpoint=$(check_checkpoint $L $J2 $START_J1)
        if [ $? -ne 0 ]; then
            echo "错误: 起始checkpoint不存在: results/L=$L/J2=$J2/J1=$START_J1/training/checkpoints/final_GCNN.pkl"
            echo "请先运行基础训练脚本训练 L=$L, J2=$J2, J1=$START_J1"
            continue
        fi
        
        echo "找到起始checkpoint: $start_checkpoint"
        
        # 生成J1扩张层次（每层包含可并行的左右点）
        j1_layers=$(generate_j1_layers $START_J1 $J1_LEFT_BOUND $J1_RIGHT_BOUND $J1_STEP)
        
        echo "J1扩张层次（每层可并行）:"
        echo "$j1_layers"
        
        # 逐层进行微调，每层内部可并行（最多2个任务）
        layer_num=1
        echo "$j1_layers" | while IFS= read -r layer; do
            if [ -z "$layer" ]; then
                continue
            fi
            
            echo "==================== 处理第 $layer_num 层: $layer ===================="
            
            # 解析当前层的J1值（空格分隔）
            layer_j1_values=($layer)
            
            # 为当前层的每个J1值准备微调任务
            pids=()
            for target_j1 in "${layer_j1_values[@]}"; do
                echo "准备微调 J1=$target_j1"
                
                # 检查目标checkpoint是否已存在
                if check_checkpoint $L $J2 $target_j1 >/dev/null 2>&1; then
                    echo "J1=$target_j1 的checkpoint已存在，跳过"
                    continue
                fi
                
                # 寻找最近的已有checkpoint作为源
                source_checkpoint=""
                min_distance=999999
                
                # 检查起始点
                if check_checkpoint $L $J2 $START_J1 >/dev/null 2>&1; then
                    distance=$(python3 -c "print(abs($target_j1 - $START_J1))")
                    if [ $(python3 -c "print($distance < $min_distance)") = "True" ]; then
                        min_distance=$distance
                        source_checkpoint=$(check_checkpoint $L $J2 $START_J1)
                    fi
                fi
                
                # 检查所有可能的已训练J1值（在当前目标点周围搜索）
                search_range=$(python3 -c "
import numpy as np
target = float('$target_j1')
step = float('$J1_STEP')
left_bound = float('$J1_LEFT_BOUND')
right_bound = float('$J1_RIGHT_BOUND')

# 生成搜索范围（目标点周围的所有可能J1值）
candidates = []
for offset in [-3, -2, -1, 1, 2, 3]:  # 搜索范围
    candidate = target + offset * step
    if left_bound <= candidate <= right_bound:
        candidates.append(f'{candidate:.2f}')

print(' '.join(candidates))
")
                
                for candidate_j1 in $search_range; do
                    if check_checkpoint $L $J2 $candidate_j1 >/dev/null 2>&1; then
                        potential_checkpoint=$(check_checkpoint $L $J2 $candidate_j1)
                        distance=$(python3 -c "print(abs($target_j1 - $candidate_j1))")
                        if [ $(python3 -c "print($distance < $min_distance)") = "True" ]; then
                            min_distance=$distance
                            source_checkpoint=$potential_checkpoint
                        fi
                    fi
                done
                
                if [ -z "$source_checkpoint" ]; then
                    echo "错误: 找不到合适的源checkpoint用于微调 J1=$target_j1"
                    continue
                fi
                
                echo "J1=$target_j1 使用源checkpoint: $source_checkpoint"
                
                # 启动后台微调任务
                (
                    echo "[并行任务] 开始微调 J1=$target_j1 at $(date)"
                    if run_finetune $L $J2 $target_j1 "$source_checkpoint"; then
                        echo "[并行任务] 成功完成 J1=$target_j1 的微调 at $(date)"
                    else
                        echo "[并行任务] 微调失败: J1=$target_j1 at $(date)"
                    fi
                ) &
                
                # 记录后台进程PID
                pids+=($!)
                echo "启动后台任务 PID: $!"
            done
            
            # 等待当前层所有任务完成
            if [ ${#pids[@]} -gt 0 ]; then
                echo "等待第 $layer_num 层的 ${#pids[@]} 个并行任务完成..."
                for pid in "${pids[@]}"; do
                    wait $pid
                    echo "任务 PID $pid 已完成"
                done
                echo "第 $layer_num 层的所有任务已完成"
            else
                echo "第 $layer_num 层没有需要执行的任务"
            fi
            
            layer_num=$((layer_num + 1))
        done
        
        echo "完成 L=$L, J2=$J2 的链式微调"
    done
done

echo "==================== 链式微调完成 ===================="
echo "所有链式微调任务已完成！"
echo "Job finished at: $(date)"