[2025-08-22 17:46:11] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.07/training/checkpoints/final_GCNN.pkl
[2025-08-22 17:46:11]   - 迭代次数: final
[2025-08-22 17:46:11]   - 能量: -55.840923+0.006343j ± 0.085489
[2025-08-22 17:46:11]   - 时间戳: 2025-08-08T00:26:04.652937+08:00
[2025-08-22 17:46:20] ✓ 变分状态参数已从checkpoint恢复
[2025-08-22 17:46:20] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-22 17:46:20] ==================================================
[2025-08-22 17:46:20] GCNN for Shastry-Sutherland Model
[2025-08-22 17:46:20] ==================================================
[2025-08-22 17:46:20] System parameters:
[2025-08-22 17:46:20]   - System size: L=4, N=64
[2025-08-22 17:46:20]   - System parameters: J1=0.08, J2=0.0, Q=1.0
[2025-08-22 17:46:20] --------------------------------------------------
[2025-08-22 17:46:20] Model parameters:
[2025-08-22 17:46:20]   - Number of layers = 4
[2025-08-22 17:46:20]   - Number of features = 4
[2025-08-22 17:46:20]   - Total parameters = 12572
[2025-08-22 17:46:20] --------------------------------------------------
[2025-08-22 17:46:20] Training parameters:
[2025-08-22 17:46:20]   - Learning rate: 0.015
[2025-08-22 17:46:20]   - Total iterations: 1050
[2025-08-22 17:46:20]   - Annealing cycles: 3
[2025-08-22 17:46:20]   - Initial period: 150
[2025-08-22 17:46:20]   - Period multiplier: 2.0
[2025-08-22 17:46:20]   - Temperature range: 0.0-1.0
[2025-08-22 17:46:20]   - Samples: 4096
[2025-08-22 17:46:20]   - Discarded samples: 0
[2025-08-22 17:46:20]   - Chunk size: 2048
[2025-08-22 17:46:20]   - Diagonal shift: 0.2
[2025-08-22 17:46:20]   - Gradient clipping: 1.0
[2025-08-22 17:46:20]   - Checkpoint enabled: interval=100
[2025-08-22 17:46:20]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.08/training/checkpoints
[2025-08-22 17:46:20] --------------------------------------------------
[2025-08-22 17:46:20] Device status:
[2025-08-22 17:46:20]   - Devices model: NVIDIA H200 NVL
[2025-08-22 17:46:20]   - Number of devices: 1
[2025-08-22 17:46:20]   - Sharding: True
[2025-08-22 17:46:20] ============================================================
[2025-08-22 17:47:02] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -56.460621-0.020048j
[2025-08-22 17:47:27] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -56.349775-0.009621j
[2025-08-22 17:47:31] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -56.258169-0.005733j
[2025-08-22 17:47:36] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -56.093541-0.005136j
[2025-08-22 17:47:40] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -56.210016-0.007034j
[2025-08-22 17:47:45] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -56.089529+0.000548j
[2025-08-22 17:47:49] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -56.197020-0.008616j
[2025-08-22 17:47:53] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -56.063572-0.003128j
[2025-08-22 17:47:58] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -56.191995+0.003310j
[2025-08-22 17:48:02] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -56.209189-0.004797j
[2025-08-22 17:48:07] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -56.256265-0.003182j
[2025-08-22 17:48:11] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -56.147855-0.000937j
[2025-08-22 17:48:16] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -56.247075+0.005188j
[2025-08-22 17:48:20] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -56.231535-0.002962j
[2025-08-22 17:48:25] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -56.235463+0.001147j
[2025-08-22 17:48:29] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -56.293026+0.000650j
[2025-08-22 17:48:34] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -56.419455+0.002304j
[2025-08-22 17:48:38] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -56.294489-0.001784j
[2025-08-22 17:48:43] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -56.265215-0.002113j
[2025-08-22 17:48:47] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -56.262350-0.000048j
[2025-08-22 17:48:51] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -56.277084+0.006325j
[2025-08-22 17:48:56] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -56.159398-0.001503j
[2025-08-22 17:49:00] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -56.216546-0.004388j
[2025-08-22 17:49:05] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -56.266117+0.003457j
[2025-08-22 17:49:09] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -56.276623-0.003191j
[2025-08-22 17:49:14] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -56.249116+0.002380j
[2025-08-22 17:49:18] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -56.215710+0.001030j
[2025-08-22 17:49:22] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -56.342420-0.000625j
[2025-08-22 17:49:27] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -56.085748-0.005954j
[2025-08-22 17:49:31] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -56.147454-0.001318j
[2025-08-22 17:49:35] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -56.116840-0.003426j
[2025-08-22 17:49:39] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -56.046668-0.005282j
[2025-08-22 17:49:44] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -56.315573-0.001135j
[2025-08-22 17:49:48] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -56.231750-0.003659j
[2025-08-22 17:49:52] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -56.198887+0.003510j
[2025-08-22 17:49:57] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -56.054525+0.002050j
[2025-08-22 17:50:01] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -56.081060-0.001961j
[2025-08-22 17:50:05] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -56.109431-0.001029j
[2025-08-22 17:50:10] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -56.217579+0.000115j
[2025-08-22 17:50:14] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -56.272202-0.003113j
[2025-08-22 17:50:18] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -56.208195-0.001580j
[2025-08-22 17:50:23] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -56.287776+0.002312j
[2025-08-22 17:50:27] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -56.270923+0.001965j
[2025-08-22 17:50:31] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -56.251108+0.000179j
[2025-08-22 17:50:36] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -56.180583+0.001386j
[2025-08-22 17:50:40] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -56.167353-0.001940j
[2025-08-22 17:50:44] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -56.168905-0.001685j
[2025-08-22 17:50:49] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -56.258009-0.002646j
[2025-08-22 17:50:53] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -56.161079-0.002847j
[2025-08-22 17:50:57] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -56.132427+0.005735j
[2025-08-22 17:51:02] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -56.080201-0.000644j
[2025-08-22 17:51:06] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -56.072964-0.001700j
[2025-08-22 17:51:10] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -56.142772-0.000993j
[2025-08-22 17:51:15] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -56.198053+0.000222j
[2025-08-22 17:51:19] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -56.128849+0.000484j
[2025-08-22 17:51:23] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -56.197705-0.001179j
[2025-08-22 17:51:28] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -56.150904-0.001535j
[2025-08-22 17:51:32] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -56.277291+0.002445j
[2025-08-22 17:51:36] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -56.225661+0.002964j
[2025-08-22 17:51:41] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -56.331952-0.002992j
[2025-08-22 17:51:45] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -56.341221-0.004087j
[2025-08-22 17:51:49] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -56.294074+0.002895j
[2025-08-22 17:51:54] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -56.236773-0.002000j
[2025-08-22 17:51:58] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -56.174745-0.004003j
[2025-08-22 17:52:02] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -56.040392-0.001846j
[2025-08-22 17:52:07] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -56.210951+0.002300j
[2025-08-22 17:52:11] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -56.189862-0.002685j
[2025-08-22 17:52:15] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -56.173646-0.002175j
[2025-08-22 17:52:20] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -56.231534-0.001723j
[2025-08-22 17:52:24] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -56.266431-0.005999j
[2025-08-22 17:52:28] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -56.005705-0.000137j
[2025-08-22 17:52:33] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -56.049023-0.003827j
[2025-08-22 17:52:37] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -56.100880+0.000967j
[2025-08-22 17:52:41] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -56.063108+0.000201j
[2025-08-22 17:52:45] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -56.168890-0.001402j
[2025-08-22 17:52:50] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -56.185930-0.000059j
[2025-08-22 17:52:54] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -56.210085-0.001747j
[2025-08-22 17:52:58] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -56.170649+0.001380j
[2025-08-22 17:53:03] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -56.121247+0.000840j
[2025-08-22 17:53:07] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -56.080074-0.000997j
[2025-08-22 17:53:11] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -56.127054+0.003727j
[2025-08-22 17:53:16] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -56.232275-0.002283j
[2025-08-22 17:53:20] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -56.234104-0.000043j
[2025-08-22 17:53:24] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -56.144710+0.000263j
[2025-08-22 17:53:29] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -56.112407+0.002980j
[2025-08-22 17:53:33] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -56.067403-0.001931j
[2025-08-22 17:53:37] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -56.137760-0.001074j
[2025-08-22 17:53:42] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -56.105170+0.003299j
[2025-08-22 17:53:46] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -56.178933+0.002660j
[2025-08-22 17:53:50] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -56.252425+0.000686j
[2025-08-22 17:53:55] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -56.065743+0.000271j
[2025-08-22 17:53:59] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -56.093816+0.001112j
[2025-08-22 17:54:03] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -56.294062+0.002068j
[2025-08-22 17:54:08] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -56.225384-0.002829j
[2025-08-22 17:54:12] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -56.167974-0.000339j
[2025-08-22 17:54:16] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -56.185144+0.000173j
[2025-08-22 17:54:21] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -56.128663-0.003536j
[2025-08-22 17:54:25] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -56.281649+0.000931j
[2025-08-22 17:54:29] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -56.251931-0.003063j
[2025-08-22 17:54:34] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -56.192187-0.000128j
[2025-08-22 17:54:34] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-22 17:54:38] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -56.317260+0.000194j
[2025-08-22 17:54:42] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -56.379458-0.001941j
[2025-08-22 17:54:47] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -56.363212-0.000738j
[2025-08-22 17:54:51] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -56.406078-0.000698j
[2025-08-22 17:54:55] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -56.391407+0.002148j
[2025-08-22 17:54:59] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -56.251060-0.005285j
[2025-08-22 17:55:04] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -56.118689-0.002691j
[2025-08-22 17:55:08] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -56.214055+0.000968j
[2025-08-22 17:55:12] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -56.333862+0.002348j
[2025-08-22 17:55:17] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -56.180944+0.001004j
[2025-08-22 17:55:21] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -56.425849-0.002595j
[2025-08-22 17:55:25] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -56.249501-0.003991j
[2025-08-22 17:55:30] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -56.187491+0.002732j
[2025-08-22 17:55:34] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -56.145237-0.001099j
[2025-08-22 17:55:38] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -56.277570-0.003088j
[2025-08-22 17:55:43] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -56.344590+0.002330j
[2025-08-22 17:55:47] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -56.259541-0.000138j
[2025-08-22 17:55:51] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -56.142487+0.004759j
[2025-08-22 17:55:56] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -56.227758+0.001728j
[2025-08-22 17:56:00] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -56.228127+0.000956j
[2025-08-22 17:56:04] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -56.238547+0.002747j
[2025-08-22 17:56:09] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -56.266776-0.003833j
[2025-08-22 17:56:13] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -56.127976-0.001953j
[2025-08-22 17:56:17] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -56.219219+0.001333j
[2025-08-22 17:56:22] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -56.296108+0.003349j
[2025-08-22 17:56:26] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -56.142026+0.000156j
[2025-08-22 17:56:30] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -56.170317+0.000804j
[2025-08-22 17:56:35] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -56.225042+0.001676j
[2025-08-22 17:56:39] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -56.147946-0.004406j
[2025-08-22 17:56:43] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -56.303729-0.000311j
[2025-08-22 17:56:48] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -56.197478-0.003735j
[2025-08-22 17:56:52] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -56.265127-0.004118j
[2025-08-22 17:56:56] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -56.387670-0.002084j
[2025-08-22 17:57:01] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -56.205487+0.000787j
[2025-08-22 17:57:05] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -56.241333-0.003240j
[2025-08-22 17:57:09] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -56.168572+0.000604j
[2025-08-22 17:57:13] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -56.245854-0.001919j
[2025-08-22 17:57:18] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -56.273756-0.002529j
[2025-08-22 17:57:22] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -56.237422-0.006169j
[2025-08-22 17:57:26] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -56.229237-0.000757j
[2025-08-22 17:57:31] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -56.329826-0.003979j
[2025-08-22 17:57:35] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -56.331885+0.000191j
[2025-08-22 17:57:39] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -56.259352+0.002017j
[2025-08-22 17:57:44] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -56.224819+0.002998j
[2025-08-22 17:57:48] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -56.303173-0.004096j
[2025-08-22 17:57:52] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -56.388894-0.001088j
[2025-08-22 17:57:57] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -56.060141+0.003942j
[2025-08-22 17:58:01] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -56.214346-0.002875j
[2025-08-22 17:58:05] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -56.235540+0.001191j
[2025-08-22 17:58:10] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -56.232148-0.001224j
[2025-08-22 17:58:10] RESTART #1 | Period: 300
[2025-08-22 17:58:14] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -56.134828-0.001705j
[2025-08-22 17:58:18] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -56.185140+0.000264j
[2025-08-22 17:58:23] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -56.159648+0.003220j
[2025-08-22 17:58:27] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -56.257277+0.003644j
[2025-08-22 17:58:31] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -56.236891+0.000959j
[2025-08-22 17:58:36] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -56.330245+0.001523j
[2025-08-22 17:58:40] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -56.183567+0.001061j
[2025-08-22 17:58:44] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -56.094751-0.001424j
[2025-08-22 17:58:49] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -56.112331+0.002047j
[2025-08-22 17:58:53] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -56.217823+0.003225j
[2025-08-22 17:58:57] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -56.216412-0.003507j
[2025-08-22 17:59:02] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -56.284224+0.001560j
[2025-08-22 17:59:06] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -56.354188+0.002559j
[2025-08-22 17:59:10] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -56.368853-0.004962j
[2025-08-22 17:59:15] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -56.360922-0.001732j
[2025-08-22 17:59:19] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -56.240364+0.000624j
[2025-08-22 17:59:23] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -56.303314+0.001039j
[2025-08-22 17:59:27] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -56.343141+0.000019j
[2025-08-22 17:59:32] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -56.251505+0.004815j
[2025-08-22 17:59:36] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -56.268221+0.001932j
[2025-08-22 17:59:40] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -56.287354-0.002575j
[2025-08-22 17:59:45] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -56.235203-0.001334j
[2025-08-22 17:59:49] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -56.257053-0.003046j
[2025-08-22 17:59:53] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -56.144411-0.000137j
[2025-08-22 17:59:58] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -56.253899+0.001416j
[2025-08-22 18:00:02] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -56.214395+0.004078j
[2025-08-22 18:00:06] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -56.260390+0.001341j
[2025-08-22 18:00:11] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -56.300212+0.000640j
[2025-08-22 18:00:15] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -56.139613-0.003827j
[2025-08-22 18:00:19] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -56.181127+0.001217j
[2025-08-22 18:00:24] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -56.116195-0.002251j
[2025-08-22 18:00:28] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -56.188459-0.000184j
[2025-08-22 18:00:32] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -56.158768-0.000365j
[2025-08-22 18:00:37] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -56.136051-0.006653j
[2025-08-22 18:00:41] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -56.145987+0.002808j
[2025-08-22 18:00:45] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -56.201554-0.003538j
[2025-08-22 18:00:50] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -56.244806+0.002042j
[2025-08-22 18:00:54] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -56.127567-0.006641j
[2025-08-22 18:00:58] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -56.175272-0.002019j
[2025-08-22 18:01:03] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -56.109783+0.000703j
[2025-08-22 18:01:07] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -56.046859-0.004966j
[2025-08-22 18:01:11] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -56.022689+0.003768j
[2025-08-22 18:01:16] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -56.077970-0.000842j
[2025-08-22 18:01:20] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -56.053007+0.000959j
[2025-08-22 18:01:24] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -56.292368+0.003164j
[2025-08-22 18:01:29] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -56.100064+0.000928j
[2025-08-22 18:01:33] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -56.240785+0.002850j
[2025-08-22 18:01:37] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -56.268748+0.003786j
[2025-08-22 18:01:42] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -56.118885+0.003448j
[2025-08-22 18:01:46] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -56.251031-0.001057j
[2025-08-22 18:01:46] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-22 18:01:50] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -56.317652-0.004991j
[2025-08-22 18:01:55] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -56.290902+0.005182j
[2025-08-22 18:01:59] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -56.321739-0.000826j
[2025-08-22 18:02:03] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -56.255224+0.001196j
[2025-08-22 18:02:07] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -56.155702+0.003427j
[2025-08-22 18:02:12] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -55.995277-0.001028j
[2025-08-22 18:02:16] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -56.062887-0.002222j
[2025-08-22 18:02:20] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -56.113162+0.000764j
[2025-08-22 18:02:25] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -56.133006-0.003596j
[2025-08-22 18:02:29] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -56.172002-0.002928j
[2025-08-22 18:02:33] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -56.204266-0.000420j
[2025-08-22 18:02:38] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -56.207509-0.002456j
[2025-08-22 18:02:42] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -56.223059-0.001616j
[2025-08-22 18:02:46] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -56.221718+0.000305j
[2025-08-22 18:02:51] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -56.233906-0.000283j
[2025-08-22 18:02:55] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -56.208937-0.002491j
[2025-08-22 18:02:59] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -56.163991+0.000792j
[2025-08-22 18:03:04] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -56.219404+0.000305j
[2025-08-22 18:03:08] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -56.298344+0.001977j
[2025-08-22 18:03:12] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -56.222612-0.001094j
[2025-08-22 18:03:17] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -56.129719-0.000316j
[2025-08-22 18:03:21] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -56.290669+0.002675j
[2025-08-22 18:03:25] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -56.237106-0.002751j
[2025-08-22 18:03:30] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -56.284245-0.001228j
[2025-08-22 18:03:34] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -56.163133-0.000746j
[2025-08-22 18:03:38] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -56.192580+0.004839j
[2025-08-22 18:03:43] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -56.302685-0.002722j
[2025-08-22 18:03:47] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -56.256583+0.002074j
[2025-08-22 18:03:51] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -56.238475-0.000852j
[2025-08-22 18:03:56] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -56.229468-0.001984j
[2025-08-22 18:04:00] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -56.239985+0.003442j
[2025-08-22 18:04:04] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -56.213186+0.000128j
[2025-08-22 18:04:09] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -56.092794+0.000327j
[2025-08-22 18:04:13] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -56.066175-0.000048j
[2025-08-22 18:04:17] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -56.138242-0.001784j
[2025-08-22 18:04:21] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -56.155661+0.000332j
[2025-08-22 18:04:26] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -56.192240-0.000340j
[2025-08-22 18:04:30] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -56.331333-0.000068j
[2025-08-22 18:04:34] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -56.419603-0.001406j
[2025-08-22 18:04:39] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -56.325461+0.006702j
[2025-08-22 18:04:43] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -56.353450+0.001048j
[2025-08-22 18:04:47] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -56.387138+0.002336j
[2025-08-22 18:04:52] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -56.447710+0.000041j
[2025-08-22 18:04:56] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -56.359401+0.002033j
[2025-08-22 18:05:00] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -56.247844-0.001956j
[2025-08-22 18:05:05] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -56.216966-0.001174j
[2025-08-22 18:05:09] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -56.354864+0.005151j
[2025-08-22 18:05:13] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -56.281314+0.002654j
[2025-08-22 18:05:18] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -56.186147-0.001203j
[2025-08-22 18:05:22] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -56.292508-0.003230j
[2025-08-22 18:05:26] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -56.248854+0.001545j
[2025-08-22 18:05:31] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -56.327660+0.002692j
[2025-08-22 18:05:35] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -56.330797+0.000964j
[2025-08-22 18:05:39] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -56.032134+0.001323j
[2025-08-22 18:05:44] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -56.183812-0.000980j
[2025-08-22 18:05:48] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -56.146906+0.000578j
[2025-08-22 18:05:52] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -56.232327-0.002918j
[2025-08-22 18:05:57] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -56.222874-0.002656j
[2025-08-22 18:06:01] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -56.299191-0.005692j
[2025-08-22 18:06:05] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -56.269295+0.000424j
[2025-08-22 18:06:10] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -56.289504-0.006397j
[2025-08-22 18:06:14] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -56.397254+0.001200j
[2025-08-22 18:06:18] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -56.336848+0.003317j
[2025-08-22 18:06:23] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -56.291955+0.000219j
[2025-08-22 18:06:27] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -56.375016-0.001959j
[2025-08-22 18:06:31] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -56.256841+0.000100j
[2025-08-22 18:06:36] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -56.242145-0.003243j
[2025-08-22 18:06:40] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -56.272953-0.004839j
[2025-08-22 18:06:44] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -56.184847-0.001137j
[2025-08-22 18:06:49] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -56.372772+0.001588j
[2025-08-22 18:06:53] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -56.166692-0.001929j
[2025-08-22 18:06:57] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -56.169051-0.000042j
[2025-08-22 18:07:01] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -56.067870-0.001493j
[2025-08-22 18:07:06] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -56.108938+0.002473j
[2025-08-22 18:07:10] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -56.291384-0.004691j
[2025-08-22 18:07:14] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -56.216436-0.007048j
[2025-08-22 18:07:19] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -56.234098+0.000429j
[2025-08-22 18:07:23] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -56.225947+0.001164j
[2025-08-22 18:07:27] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -56.252803-0.000625j
[2025-08-22 18:07:32] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -56.394734-0.002426j
[2025-08-22 18:07:36] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -56.320627-0.003586j
[2025-08-22 18:07:40] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -56.204904-0.001484j
[2025-08-22 18:07:45] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -56.368574-0.003919j
[2025-08-22 18:07:49] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -56.318073-0.001423j
[2025-08-22 18:07:53] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -56.264936-0.005465j
[2025-08-22 18:07:58] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -56.365674-0.001030j
[2025-08-22 18:08:02] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -56.233180-0.005657j
[2025-08-22 18:08:06] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -56.202307+0.002957j
[2025-08-22 18:08:11] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -56.255399-0.002001j
[2025-08-22 18:08:15] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -56.272169-0.006731j
[2025-08-22 18:08:19] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -56.195051-0.001211j
[2025-08-22 18:08:24] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -56.207676-0.001732j
[2025-08-22 18:08:28] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -56.115346+0.002421j
[2025-08-22 18:08:32] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -56.159592+0.001763j
[2025-08-22 18:08:37] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -56.096897-0.000827j
[2025-08-22 18:08:41] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -56.088035-0.000652j
[2025-08-22 18:08:45] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -56.198129-0.002549j
[2025-08-22 18:08:50] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -56.141323-0.000510j
[2025-08-22 18:08:54] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -56.219593+0.005734j
[2025-08-22 18:08:58] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -56.300825+0.000373j
[2025-08-22 18:08:58] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-22 18:09:03] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -56.254797+0.002329j
[2025-08-22 18:09:07] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -56.153528-0.001354j
[2025-08-22 18:09:11] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -56.148816-0.001359j
[2025-08-22 18:09:15] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -56.250265-0.004625j
[2025-08-22 18:09:20] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -56.077537-0.003203j
[2025-08-22 18:09:24] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -56.185195+0.001444j
[2025-08-22 18:09:28] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -56.246774+0.001093j
[2025-08-22 18:09:33] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -56.252143+0.003280j
[2025-08-22 18:09:37] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -56.285983+0.004798j
[2025-08-22 18:09:41] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -56.323057+0.002719j
[2025-08-22 18:09:46] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -56.214343-0.001298j
[2025-08-22 18:09:50] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -56.114508+0.001853j
[2025-08-22 18:09:54] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -56.070087+0.001223j
[2025-08-22 18:09:59] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -56.162674-0.000228j
[2025-08-22 18:10:03] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -56.194096+0.000766j
[2025-08-22 18:10:07] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -56.358891-0.002978j
[2025-08-22 18:10:12] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -56.268399-0.005864j
[2025-08-22 18:10:16] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -56.306475-0.002361j
[2025-08-22 18:10:20] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -56.333140-0.000438j
[2025-08-22 18:10:25] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -56.200255-0.000765j
[2025-08-22 18:10:29] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -56.186216-0.002016j
[2025-08-22 18:10:33] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -56.277391-0.003339j
[2025-08-22 18:10:38] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -56.204871+0.000412j
[2025-08-22 18:10:42] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -56.053479+0.004139j
[2025-08-22 18:10:46] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -56.043424+0.000914j
[2025-08-22 18:10:51] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -56.137398-0.002319j
[2025-08-22 18:10:55] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -56.118223-0.001625j
[2025-08-22 18:10:59] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -56.152821-0.000126j
[2025-08-22 18:11:03] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -56.193602-0.000641j
[2025-08-22 18:11:08] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -56.038165+0.004598j
[2025-08-22 18:11:12] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -56.129425-0.002031j
[2025-08-22 18:11:16] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -56.267067+0.001903j
[2025-08-22 18:11:21] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -56.145691+0.000160j
[2025-08-22 18:11:25] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -56.031693+0.000791j
[2025-08-22 18:11:29] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -56.062330-0.002887j
[2025-08-22 18:11:34] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -56.225663+0.000264j
[2025-08-22 18:11:38] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -56.231443+0.002018j
[2025-08-22 18:11:42] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -56.348206+0.000473j
[2025-08-22 18:11:47] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -56.266113+0.000308j
[2025-08-22 18:11:51] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -56.320104-0.003569j
[2025-08-22 18:11:55] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -56.398024-0.000933j
[2025-08-22 18:12:00] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -56.195258-0.002553j
[2025-08-22 18:12:04] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -56.147265+0.002395j
[2025-08-22 18:12:08] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -56.100865-0.000162j
[2025-08-22 18:12:13] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -56.256921-0.000626j
[2025-08-22 18:12:17] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -56.253828+0.000061j
[2025-08-22 18:12:21] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -56.250009+0.003426j
[2025-08-22 18:12:26] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -56.223576+0.001648j
[2025-08-22 18:12:30] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -56.201121-0.000302j
[2025-08-22 18:12:34] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -56.283657+0.004541j
[2025-08-22 18:12:39] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -56.223050-0.000854j
[2025-08-22 18:12:43] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -56.211942-0.002244j
[2025-08-22 18:12:47] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -56.302100-0.005355j
[2025-08-22 18:12:51] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -56.305898-0.000245j
[2025-08-22 18:12:56] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -56.223900+0.003665j
[2025-08-22 18:13:00] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -56.144046+0.003130j
[2025-08-22 18:13:04] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -56.221967+0.000804j
[2025-08-22 18:13:09] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -56.261540-0.000417j
[2025-08-22 18:13:13] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -56.246209+0.001817j
[2025-08-22 18:13:17] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -56.208997-0.004502j
[2025-08-22 18:13:22] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -56.116905+0.001172j
[2025-08-22 18:13:26] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -56.098408-0.002051j
[2025-08-22 18:13:30] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -56.120333+0.002288j
[2025-08-22 18:13:35] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -56.103239-0.006753j
[2025-08-22 18:13:39] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -56.165940+0.002757j
[2025-08-22 18:13:43] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -56.243797-0.003800j
[2025-08-22 18:13:48] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -56.221814-0.001724j
[2025-08-22 18:13:52] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -56.177520+0.000553j
[2025-08-22 18:13:56] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -56.104481-0.000005j
[2025-08-22 18:14:01] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -56.177369-0.003545j
[2025-08-22 18:14:05] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -56.164279+0.002189j
[2025-08-22 18:14:09] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -56.054383+0.000170j
[2025-08-22 18:14:14] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -56.178321+0.000502j
[2025-08-22 18:14:18] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -56.392535+0.000595j
[2025-08-22 18:14:22] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -56.221659+0.006558j
[2025-08-22 18:14:27] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -56.281271+0.003384j
[2025-08-22 18:14:31] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -56.247498-0.001628j
[2025-08-22 18:14:35] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -56.145464+0.003088j
[2025-08-22 18:14:39] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -56.178168+0.000059j
[2025-08-22 18:14:44] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -56.213376+0.001436j
[2025-08-22 18:14:48] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -56.189876+0.001871j
[2025-08-22 18:14:52] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -56.216001+0.003356j
[2025-08-22 18:14:57] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -56.202638-0.000948j
[2025-08-22 18:15:01] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -56.139008-0.002259j
[2025-08-22 18:15:05] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -56.114429+0.003863j
[2025-08-22 18:15:10] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -56.252636+0.003451j
[2025-08-22 18:15:14] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -56.168567+0.000035j
[2025-08-22 18:15:18] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -56.046886+0.000202j
[2025-08-22 18:15:23] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -56.023506-0.003463j
[2025-08-22 18:15:27] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -56.218847+0.003636j
[2025-08-22 18:15:31] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -56.162276-0.004127j
[2025-08-22 18:15:36] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -56.205511-0.000777j
[2025-08-22 18:15:40] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -56.248981-0.001248j
[2025-08-22 18:15:44] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -56.218868-0.001982j
[2025-08-22 18:15:49] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -56.240504-0.002685j
[2025-08-22 18:15:53] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -56.272876-0.001037j
[2025-08-22 18:15:57] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -56.214371+0.005435j
[2025-08-22 18:16:02] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -56.227330+0.001001j
[2025-08-22 18:16:06] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -56.269939+0.003179j
[2025-08-22 18:16:10] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -56.345277-0.000728j
[2025-08-22 18:16:10] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-22 18:16:15] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -56.269528-0.000923j
[2025-08-22 18:16:19] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -56.274523+0.003547j
[2025-08-22 18:16:23] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -56.233104-0.000410j
[2025-08-22 18:16:28] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -56.242388-0.000715j
[2025-08-22 18:16:32] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -56.304299-0.003173j
[2025-08-22 18:16:36] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -56.308349+0.001031j
[2025-08-22 18:16:41] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -56.249100+0.003501j
[2025-08-22 18:16:45] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -56.138250-0.002995j
[2025-08-22 18:16:49] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -56.254661+0.001828j
[2025-08-22 18:16:53] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -56.102087-0.003896j
[2025-08-22 18:16:58] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -56.111972-0.002478j
[2025-08-22 18:17:02] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -56.189592-0.000169j
[2025-08-22 18:17:06] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -56.150646-0.001901j
[2025-08-22 18:17:11] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -56.124966-0.001462j
[2025-08-22 18:17:15] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -56.307390+0.000493j
[2025-08-22 18:17:19] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -56.318866-0.005210j
[2025-08-22 18:17:24] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -56.281560+0.003132j
[2025-08-22 18:17:28] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -56.378675+0.001363j
[2025-08-22 18:17:32] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -56.248162-0.000825j
[2025-08-22 18:17:37] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -56.267812+0.001764j
[2025-08-22 18:17:41] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -56.209732-0.001356j
[2025-08-22 18:17:45] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -56.251755+0.000512j
[2025-08-22 18:17:50] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -56.434837-0.000544j
[2025-08-22 18:17:54] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -56.448005+0.001815j
[2025-08-22 18:17:58] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -56.370463+0.001257j
[2025-08-22 18:18:03] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -56.354824+0.001183j
[2025-08-22 18:18:07] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -56.205591+0.001548j
[2025-08-22 18:18:11] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -56.215564+0.000246j
[2025-08-22 18:18:16] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -56.338776+0.002885j
[2025-08-22 18:18:20] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -56.225817+0.001375j
[2025-08-22 18:18:24] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -56.156530+0.003255j
[2025-08-22 18:18:29] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -56.075891-0.001547j
[2025-08-22 18:18:33] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -56.060808+0.001324j
[2025-08-22 18:18:37] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -56.184359+0.002594j
[2025-08-22 18:18:41] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -56.343191-0.000129j
[2025-08-22 18:18:46] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -56.302861+0.000619j
[2025-08-22 18:18:50] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -56.310541-0.002245j
[2025-08-22 18:18:54] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -56.208130+0.000702j
[2025-08-22 18:18:59] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -56.306703+0.004629j
[2025-08-22 18:19:03] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -56.306098-0.000444j
[2025-08-22 18:19:07] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -56.306394-0.008535j
[2025-08-22 18:19:12] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -56.294247+0.001057j
[2025-08-22 18:19:16] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -56.088544-0.004060j
[2025-08-22 18:19:20] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -56.087654-0.000625j
[2025-08-22 18:19:25] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -56.130338-0.001356j
[2025-08-22 18:19:29] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -56.164269+0.004097j
[2025-08-22 18:19:33] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -56.356595+0.005153j
[2025-08-22 18:19:38] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -56.161888+0.001060j
[2025-08-22 18:19:42] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -56.389034+0.000875j
[2025-08-22 18:19:46] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -56.332777-0.002533j
[2025-08-22 18:19:46] RESTART #2 | Period: 600
[2025-08-22 18:19:51] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -56.321076-0.001063j
[2025-08-22 18:19:55] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -56.222264+0.001057j
[2025-08-22 18:19:59] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -56.387407-0.002485j
[2025-08-22 18:20:04] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -56.175662+0.003853j
[2025-08-22 18:20:08] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -56.125785-0.000558j
[2025-08-22 18:20:12] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -56.149416-0.003225j
[2025-08-22 18:20:17] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -56.300362+0.001178j
[2025-08-22 18:20:21] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -56.338207+0.006354j
[2025-08-22 18:20:25] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -56.383042+0.003591j
[2025-08-22 18:20:29] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -56.309030+0.001457j
[2025-08-22 18:20:34] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -56.327065+0.002643j
[2025-08-22 18:20:38] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -56.175811-0.000379j
[2025-08-22 18:20:42] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -56.178693+0.000964j
[2025-08-22 18:20:47] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -56.150193+0.003634j
[2025-08-22 18:20:51] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -56.236468-0.001179j
[2025-08-22 18:20:55] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -56.260916-0.002763j
[2025-08-22 18:21:00] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -56.200136+0.002608j
[2025-08-22 18:21:04] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -56.159977+0.001509j
[2025-08-22 18:21:08] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -56.109427+0.000139j
[2025-08-22 18:21:13] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -56.158533+0.002135j
[2025-08-22 18:21:17] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -56.237879+0.003099j
[2025-08-22 18:21:21] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -56.176324-0.000265j
[2025-08-22 18:21:26] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -56.068718-0.002871j
[2025-08-22 18:21:30] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -56.154707-0.001202j
[2025-08-22 18:21:34] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -56.003542+0.001394j
[2025-08-22 18:21:39] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -56.145534+0.004621j
[2025-08-22 18:21:43] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -56.204808+0.002824j
[2025-08-22 18:21:47] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -56.191179+0.001042j
[2025-08-22 18:21:52] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -56.259757-0.002183j
[2025-08-22 18:21:56] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -56.246079+0.000091j
[2025-08-22 18:22:00] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -56.333523+0.000491j
[2025-08-22 18:22:05] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -56.261583+0.002998j
[2025-08-22 18:22:09] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -56.230489-0.001203j
[2025-08-22 18:22:13] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -56.242335-0.010026j
[2025-08-22 18:22:18] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -56.112747-0.002022j
[2025-08-22 18:22:22] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -56.014717-0.001961j
[2025-08-22 18:22:26] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -56.052157-0.000112j
[2025-08-22 18:22:30] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -56.156530-0.000557j
[2025-08-22 18:22:35] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -56.208221-0.002002j
[2025-08-22 18:22:39] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -56.266016-0.000104j
[2025-08-22 18:22:43] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -56.281726+0.003142j
[2025-08-22 18:22:48] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -56.144347-0.000726j
[2025-08-22 18:22:52] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -56.270037+0.005051j
[2025-08-22 18:22:56] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -56.331808-0.003697j
[2025-08-22 18:23:01] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -56.401408+0.006102j
[2025-08-22 18:23:05] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -56.363436+0.001010j
[2025-08-22 18:23:09] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -56.232480+0.001691j
[2025-08-22 18:23:14] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -56.235355+0.000069j
[2025-08-22 18:23:18] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -56.205315+0.001553j
[2025-08-22 18:23:22] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -56.276021+0.000119j
[2025-08-22 18:23:22] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-22 18:23:27] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -56.241637-0.000035j
[2025-08-22 18:23:31] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -56.273200-0.001742j
[2025-08-22 18:23:35] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -56.263092-0.003766j
[2025-08-22 18:23:40] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -56.225708+0.000440j
[2025-08-22 18:23:44] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -56.276445-0.008189j
[2025-08-22 18:23:48] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -56.235025-0.001865j
[2025-08-22 18:23:53] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -56.247155-0.003627j
[2025-08-22 18:23:57] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -56.248997+0.000343j
[2025-08-22 18:24:01] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -56.281220+0.001132j
[2025-08-22 18:24:06] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -56.239533+0.003240j
[2025-08-22 18:24:10] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -56.299073-0.000782j
[2025-08-22 18:24:14] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -56.299951-0.003523j
[2025-08-22 18:24:19] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -56.163881-0.004452j
[2025-08-22 18:24:23] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -56.270008+0.001257j
[2025-08-22 18:24:27] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -56.176779-0.005783j
[2025-08-22 18:24:31] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -56.217011+0.002659j
[2025-08-22 18:24:36] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -56.242940-0.002415j
[2025-08-22 18:24:40] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -56.092964-0.003277j
[2025-08-22 18:24:44] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -56.192313+0.004365j
[2025-08-22 18:24:49] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -56.141013-0.001591j
[2025-08-22 18:24:53] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -55.993388+0.000170j
[2025-08-22 18:24:57] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -56.231780-0.005748j
[2025-08-22 18:25:02] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -56.197548-0.000917j
[2025-08-22 18:25:06] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -56.227075-0.000341j
[2025-08-22 18:25:10] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -56.124873+0.005130j
[2025-08-22 18:25:15] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -56.187780+0.004932j
[2025-08-22 18:25:19] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -56.275768+0.000432j
[2025-08-22 18:25:23] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -56.312151-0.000601j
[2025-08-22 18:25:28] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -56.235130-0.002015j
[2025-08-22 18:25:32] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -56.285917-0.000553j
[2025-08-22 18:25:36] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -56.224790-0.000377j
[2025-08-22 18:25:41] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -56.098066+0.000830j
[2025-08-22 18:25:45] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -56.074972+0.005197j
[2025-08-22 18:25:49] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -56.120943+0.000726j
[2025-08-22 18:25:54] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -56.179927+0.003405j
[2025-08-22 18:25:58] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -56.186985-0.002656j
[2025-08-22 18:26:02] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -56.050028-0.003255j
[2025-08-22 18:26:07] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -56.268849-0.001798j
[2025-08-22 18:26:11] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -56.157195+0.002102j
[2025-08-22 18:26:15] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -56.344101-0.002819j
[2025-08-22 18:26:19] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -56.312203+0.003941j
[2025-08-22 18:26:24] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -56.163341-0.004409j
[2025-08-22 18:26:28] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -56.300887+0.002302j
[2025-08-22 18:26:32] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -56.236223+0.001246j
[2025-08-22 18:26:37] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -56.302886+0.001326j
[2025-08-22 18:26:41] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -56.221213-0.000784j
[2025-08-22 18:26:45] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -56.163233-0.000890j
[2025-08-22 18:26:50] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -56.311992+0.000671j
[2025-08-22 18:26:54] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -56.275517-0.000038j
[2025-08-22 18:26:58] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -56.318407+0.000905j
[2025-08-22 18:27:03] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -56.208132+0.001984j
[2025-08-22 18:27:07] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -56.186108-0.002620j
[2025-08-22 18:27:11] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -56.297068+0.002706j
[2025-08-22 18:27:16] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -56.245091+0.000698j
[2025-08-22 18:27:20] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -56.203552-0.002092j
[2025-08-22 18:27:24] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -56.327143+0.005708j
[2025-08-22 18:27:29] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -56.327197-0.002064j
[2025-08-22 18:27:33] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -56.164936+0.000742j
[2025-08-22 18:27:37] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -56.135031+0.001053j
[2025-08-22 18:27:42] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -56.194429-0.001264j
[2025-08-22 18:27:46] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -56.174359-0.002248j
[2025-08-22 18:27:50] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -56.129509+0.002806j
[2025-08-22 18:27:55] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -56.187272-0.002224j
[2025-08-22 18:27:59] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -56.289452-0.001515j
[2025-08-22 18:28:03] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -56.139576+0.001266j
[2025-08-22 18:28:08] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -56.242832+0.000123j
[2025-08-22 18:28:12] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -56.238681-0.000292j
[2025-08-22 18:28:16] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -56.224708-0.002792j
[2025-08-22 18:28:21] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -56.200254-0.001916j
[2025-08-22 18:28:25] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -56.090809+0.009962j
[2025-08-22 18:28:29] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -56.123539-0.000870j
[2025-08-22 18:28:34] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -56.132158-0.003742j
[2025-08-22 18:28:38] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -56.096919+0.002162j
[2025-08-22 18:28:42] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -56.163926+0.000357j
[2025-08-22 18:28:46] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -56.237906-0.003434j
[2025-08-22 18:28:51] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -56.180149-0.000899j
[2025-08-22 18:28:55] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -56.217940-0.003064j
[2025-08-22 18:28:59] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -56.027056-0.006217j
[2025-08-22 18:29:04] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -56.130231+0.002444j
[2025-08-22 18:29:08] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -56.211806+0.002542j
[2025-08-22 18:29:12] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -56.412615-0.002244j
[2025-08-22 18:29:17] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -56.136888-0.001274j
[2025-08-22 18:29:21] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -56.235633+0.002550j
[2025-08-22 18:29:25] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -56.259711-0.004931j
[2025-08-22 18:29:30] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -56.275640-0.000700j
[2025-08-22 18:29:34] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -56.145908-0.006218j
[2025-08-22 18:29:38] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -56.243295+0.001642j
[2025-08-22 18:29:43] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -56.405476+0.000625j
[2025-08-22 18:29:47] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -56.289828+0.000784j
[2025-08-22 18:29:51] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -56.205340+0.000800j
[2025-08-22 18:29:56] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -56.222730+0.003377j
[2025-08-22 18:30:00] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -56.223731-0.001787j
[2025-08-22 18:30:04] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -56.199424-0.002402j
[2025-08-22 18:30:09] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -56.158156+0.005089j
[2025-08-22 18:30:13] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -56.195315-0.000419j
[2025-08-22 18:30:17] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -56.154772-0.000421j
[2025-08-22 18:30:22] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -56.246189-0.004247j
[2025-08-22 18:30:26] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -56.210740-0.001120j
[2025-08-22 18:30:30] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -56.208070+0.003121j
[2025-08-22 18:30:34] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -56.189191-0.002726j
[2025-08-22 18:30:35] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-22 18:30:39] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -56.256900+0.003287j
[2025-08-22 18:30:43] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -56.315197-0.002250j
[2025-08-22 18:30:47] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -56.489932+0.005170j
[2025-08-22 18:30:52] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -56.340230-0.005937j
[2025-08-22 18:30:56] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -56.273248+0.000626j
[2025-08-22 18:31:00] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -56.371784-0.002389j
[2025-08-22 18:31:05] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -56.166888+0.000723j
[2025-08-22 18:31:09] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -56.109642+0.003257j
[2025-08-22 18:31:13] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -56.189763+0.003755j
[2025-08-22 18:31:18] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -56.262643+0.000364j
[2025-08-22 18:31:22] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -56.162020-0.000369j
[2025-08-22 18:31:26] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -56.087012-0.000193j
[2025-08-22 18:31:31] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -56.244988-0.000856j
[2025-08-22 18:31:35] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -56.294645-0.000933j
[2025-08-22 18:31:39] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -56.200514+0.000712j
[2025-08-22 18:31:44] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -56.187776-0.001651j
[2025-08-22 18:31:48] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -56.218086+0.002398j
[2025-08-22 18:31:52] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -56.202628-0.000499j
[2025-08-22 18:31:57] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -56.305416+0.002065j
[2025-08-22 18:32:01] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -56.397894-0.005071j
[2025-08-22 18:32:05] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -56.252529+0.000551j
[2025-08-22 18:32:10] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -56.181964-0.001234j
[2025-08-22 18:32:14] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -56.160819-0.001334j
[2025-08-22 18:32:18] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -56.180602-0.000141j
[2025-08-22 18:32:23] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -56.280788+0.002462j
[2025-08-22 18:32:27] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -56.308850-0.004196j
[2025-08-22 18:32:31] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -56.249131-0.003270j
[2025-08-22 18:32:36] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -56.195005+0.001261j
[2025-08-22 18:32:40] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -56.258929-0.002761j
[2025-08-22 18:32:44] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -56.251070-0.005706j
[2025-08-22 18:32:48] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -56.265174+0.000271j
[2025-08-22 18:32:53] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -56.242216+0.000756j
[2025-08-22 18:32:57] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -56.139527-0.001895j
[2025-08-22 18:33:01] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -56.053273+0.000772j
[2025-08-22 18:33:06] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -56.250276+0.000300j
[2025-08-22 18:33:10] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -56.158116-0.006792j
[2025-08-22 18:33:14] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -56.226531+0.000553j
[2025-08-22 18:33:19] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -56.302890-0.000677j
[2025-08-22 18:33:23] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -56.168176-0.000484j
[2025-08-22 18:33:27] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -56.182396-0.001741j
[2025-08-22 18:33:32] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -56.002558-0.001833j
[2025-08-22 18:33:36] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -56.274744+0.002896j
[2025-08-22 18:33:40] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -56.294533-0.001471j
[2025-08-22 18:33:45] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -56.261186-0.000931j
[2025-08-22 18:33:49] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -56.108155-0.000859j
[2025-08-22 18:33:53] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -56.185703-0.003998j
[2025-08-22 18:33:58] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -56.267676-0.002632j
[2025-08-22 18:34:02] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -56.186175-0.002490j
[2025-08-22 18:34:06] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -56.371629+0.001380j
[2025-08-22 18:34:11] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -56.283935+0.007259j
[2025-08-22 18:34:15] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -56.137603-0.010277j
[2025-08-22 18:34:19] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -56.185475-0.000637j
[2025-08-22 18:34:24] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -56.155297+0.003204j
[2025-08-22 18:34:28] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -56.322524-0.002314j
[2025-08-22 18:34:32] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -56.226848-0.001893j
[2025-08-22 18:34:37] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -56.231952-0.000395j
[2025-08-22 18:34:41] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -56.277561+0.003839j
[2025-08-22 18:34:45] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -56.348582+0.000221j
[2025-08-22 18:34:49] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -56.314873-0.002202j
[2025-08-22 18:34:54] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -56.423518+0.002567j
[2025-08-22 18:34:58] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -56.388784+0.001799j
[2025-08-22 18:35:02] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -56.179050-0.001566j
[2025-08-22 18:35:07] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -56.197924-0.000751j
[2025-08-22 18:35:11] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -56.240109-0.000292j
[2025-08-22 18:35:15] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -56.165135+0.001235j
[2025-08-22 18:35:20] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -56.234478+0.006129j
[2025-08-22 18:35:24] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -56.078323-0.000482j
[2025-08-22 18:35:28] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -56.235102+0.005014j
[2025-08-22 18:35:33] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -56.348834-0.008072j
[2025-08-22 18:35:37] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -56.178426+0.003463j
[2025-08-22 18:35:41] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -56.294482-0.003626j
[2025-08-22 18:35:46] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -56.236993+0.001015j
[2025-08-22 18:35:50] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -56.255226+0.000534j
[2025-08-22 18:35:54] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -56.213074-0.001334j
[2025-08-22 18:35:59] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -56.114877-0.000789j
[2025-08-22 18:36:03] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -56.082002-0.004562j
[2025-08-22 18:36:07] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -56.206470+0.000206j
[2025-08-22 18:36:12] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -56.042136-0.000658j
[2025-08-22 18:36:16] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -56.160620-0.000770j
[2025-08-22 18:36:20] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -56.186990+0.000751j
[2025-08-22 18:36:25] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -56.261266-0.001993j
[2025-08-22 18:36:29] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -56.274669-0.000481j
[2025-08-22 18:36:33] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -56.133884+0.000907j
[2025-08-22 18:36:38] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -56.165586+0.003317j
[2025-08-22 18:36:42] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -56.196483+0.000211j
[2025-08-22 18:36:46] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -56.159679+0.002225j
[2025-08-22 18:36:51] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -56.181979+0.000777j
[2025-08-22 18:36:55] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -56.258198-0.000301j
[2025-08-22 18:36:59] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -56.263591-0.004037j
[2025-08-22 18:37:03] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -56.179857+0.000167j
[2025-08-22 18:37:08] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -56.225024-0.004522j
[2025-08-22 18:37:12] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -56.273911-0.001301j
[2025-08-22 18:37:16] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -56.334954+0.004871j
[2025-08-22 18:37:21] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -56.264701-0.001592j
[2025-08-22 18:37:25] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -56.247882-0.003910j
[2025-08-22 18:37:29] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -56.258153+0.001763j
[2025-08-22 18:37:34] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -56.328884+0.003016j
[2025-08-22 18:37:38] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -56.308468-0.002461j
[2025-08-22 18:37:42] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -56.227481+0.005317j
[2025-08-22 18:37:47] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -56.083592-0.002985j
[2025-08-22 18:37:47] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-22 18:37:51] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -56.190212-0.002494j
[2025-08-22 18:37:55] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -56.249049+0.001189j
[2025-08-22 18:38:00] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -56.252202+0.000383j
[2025-08-22 18:38:04] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -56.262372+0.001346j
[2025-08-22 18:38:08] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -56.284765-0.003663j
[2025-08-22 18:38:13] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -56.285434+0.001127j
[2025-08-22 18:38:17] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -56.225750-0.003719j
[2025-08-22 18:38:21] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -56.220872+0.004192j
[2025-08-22 18:38:26] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -56.360045+0.003948j
[2025-08-22 18:38:30] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -56.307714-0.005508j
[2025-08-22 18:38:34] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -56.266822-0.000492j
[2025-08-22 18:38:39] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -56.207335+0.002395j
[2025-08-22 18:38:43] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -56.209296+0.004704j
[2025-08-22 18:38:47] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -56.283209+0.001060j
[2025-08-22 18:38:51] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -56.233007-0.003006j
[2025-08-22 18:38:56] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -56.201794+0.003285j
[2025-08-22 18:39:00] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -56.240005+0.000219j
[2025-08-22 18:39:04] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -56.386839+0.004857j
[2025-08-22 18:39:09] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -56.394980+0.003151j
[2025-08-22 18:39:13] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -56.424019+0.001231j
[2025-08-22 18:39:17] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -56.426545-0.004672j
[2025-08-22 18:39:22] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -56.283914+0.001804j
[2025-08-22 18:39:26] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -56.355897+0.003161j
[2025-08-22 18:39:30] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -56.205715+0.001197j
[2025-08-22 18:39:35] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -56.319327+0.004815j
[2025-08-22 18:39:39] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -56.343430+0.001275j
[2025-08-22 18:39:43] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -56.289901-0.001100j
[2025-08-22 18:39:48] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -56.297362-0.000204j
[2025-08-22 18:39:52] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -56.425709+0.002134j
[2025-08-22 18:39:56] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -56.324816+0.001251j
[2025-08-22 18:40:01] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -56.411994+0.002266j
[2025-08-22 18:40:05] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -56.252693-0.001903j
[2025-08-22 18:40:09] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -56.265243-0.003593j
[2025-08-22 18:40:14] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -56.234675-0.000876j
[2025-08-22 18:40:18] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -56.302306-0.003752j
[2025-08-22 18:40:22] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -56.432320-0.002957j
[2025-08-22 18:40:27] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -56.280625+0.003870j
[2025-08-22 18:40:31] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -56.178836+0.001145j
[2025-08-22 18:40:35] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -56.203342-0.004725j
[2025-08-22 18:40:39] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -56.255411-0.000981j
[2025-08-22 18:40:44] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -56.347387-0.003308j
[2025-08-22 18:40:48] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -56.203670-0.001407j
[2025-08-22 18:40:52] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -56.189127-0.002298j
[2025-08-22 18:40:57] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -56.216290-0.000254j
[2025-08-22 18:41:01] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -56.203689-0.001955j
[2025-08-22 18:41:05] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -56.328485-0.001052j
[2025-08-22 18:41:10] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -56.239522-0.000334j
[2025-08-22 18:41:14] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -56.204488-0.000191j
[2025-08-22 18:41:18] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -56.218075+0.000631j
[2025-08-22 18:41:23] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -56.387281-0.003501j
[2025-08-22 18:41:27] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -56.260296-0.000733j
[2025-08-22 18:41:31] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -56.215054+0.002018j
[2025-08-22 18:41:36] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -56.183347-0.000675j
[2025-08-22 18:41:40] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -56.167553-0.002684j
[2025-08-22 18:41:44] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -56.277877+0.003556j
[2025-08-22 18:41:49] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -56.232998-0.002964j
[2025-08-22 18:41:53] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -56.201730+0.001490j
[2025-08-22 18:41:57] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -56.266311-0.003821j
[2025-08-22 18:42:02] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -56.083079-0.001021j
[2025-08-22 18:42:06] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -56.132507-0.004334j
[2025-08-22 18:42:10] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -56.204341-0.000795j
[2025-08-22 18:42:15] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -56.179542+0.001081j
[2025-08-22 18:42:19] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -56.139398+0.000931j
[2025-08-22 18:42:23] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -56.227491-0.000794j
[2025-08-22 18:42:27] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -56.264034-0.001324j
[2025-08-22 18:42:32] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -56.265466-0.000808j
[2025-08-22 18:42:36] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -56.143567-0.002744j
[2025-08-22 18:42:40] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -56.192450-0.001795j
[2025-08-22 18:42:45] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -56.161113-0.001982j
[2025-08-22 18:42:49] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -56.289323-0.000175j
[2025-08-22 18:42:53] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -56.359678+0.000631j
[2025-08-22 18:42:58] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -56.315440+0.002249j
[2025-08-22 18:43:02] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -56.347962-0.001579j
[2025-08-22 18:43:06] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -56.406509+0.002878j
[2025-08-22 18:43:11] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -56.293705+0.002349j
[2025-08-22 18:43:15] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -56.310945+0.001238j
[2025-08-22 18:43:19] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -56.273411+0.002802j
[2025-08-22 18:43:24] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -56.229798+0.005765j
[2025-08-22 18:43:28] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -56.292911+0.000583j
[2025-08-22 18:43:32] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -56.201335+0.004377j
[2025-08-22 18:43:37] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -56.145155-0.002425j
[2025-08-22 18:43:41] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -56.111279-0.004074j
[2025-08-22 18:43:45] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -56.202950-0.001053j
[2025-08-22 18:43:50] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -56.189376-0.000053j
[2025-08-22 18:43:54] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -56.205977+0.005499j
[2025-08-22 18:43:58] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -56.252623-0.001759j
[2025-08-22 18:44:03] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -56.277841+0.001415j
[2025-08-22 18:44:07] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -56.332046+0.003203j
[2025-08-22 18:44:11] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -56.214240-0.001691j
[2025-08-22 18:44:16] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -56.281557-0.000063j
[2025-08-22 18:44:20] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -56.153810+0.001035j
[2025-08-22 18:44:24] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -56.215219+0.001660j
[2025-08-22 18:44:29] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -56.230016-0.002854j
[2025-08-22 18:44:33] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -56.218911-0.003154j
[2025-08-22 18:44:37] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -56.088300+0.000161j
[2025-08-22 18:44:41] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -56.291828-0.000158j
[2025-08-22 18:44:46] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -56.309911+0.004114j
[2025-08-22 18:44:50] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -56.263773-0.004433j
[2025-08-22 18:44:54] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -56.184018-0.000675j
[2025-08-22 18:44:59] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -56.163876-0.000120j
[2025-08-22 18:44:59] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-22 18:45:03] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -56.268581+0.000492j
[2025-08-22 18:45:07] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -56.287010-0.003136j
[2025-08-22 18:45:12] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -56.122666-0.004864j
[2025-08-22 18:45:16] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -56.184294-0.002291j
[2025-08-22 18:45:20] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -56.131169+0.002215j
[2025-08-22 18:45:25] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -56.335408-0.000868j
[2025-08-22 18:45:29] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -56.260809-0.000408j
[2025-08-22 18:45:33] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -56.249387-0.005086j
[2025-08-22 18:45:38] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -56.321765+0.001096j
[2025-08-22 18:45:42] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -56.245568-0.000092j
[2025-08-22 18:45:46] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -56.320858+0.004498j
[2025-08-22 18:45:51] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -56.286461+0.004881j
[2025-08-22 18:45:55] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -56.321536-0.002457j
[2025-08-22 18:45:59] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -56.359984-0.000761j
[2025-08-22 18:46:04] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -56.348254-0.001162j
[2025-08-22 18:46:08] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -56.313933-0.001983j
[2025-08-22 18:46:12] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -56.182723-0.001776j
[2025-08-22 18:46:17] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -56.046196+0.006457j
[2025-08-22 18:46:21] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -56.101485+0.000051j
[2025-08-22 18:46:25] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -56.085838+0.000932j
[2025-08-22 18:46:30] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -56.188143+0.004225j
[2025-08-22 18:46:34] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -56.172129-0.004324j
[2025-08-22 18:46:38] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -56.112711+0.000608j
[2025-08-22 18:46:43] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -56.193214+0.001745j
[2025-08-22 18:46:47] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -56.206819+0.000651j
[2025-08-22 18:46:51] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -56.227320-0.001800j
[2025-08-22 18:46:55] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -56.282414+0.004822j
[2025-08-22 18:47:00] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -56.338714+0.001324j
[2025-08-22 18:47:04] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -56.199894+0.004866j
[2025-08-22 18:47:08] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -56.144875-0.004556j
[2025-08-22 18:47:13] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -56.119275+0.000523j
[2025-08-22 18:47:17] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -56.032551+0.000907j
[2025-08-22 18:47:21] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -56.111546-0.002896j
[2025-08-22 18:47:26] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -56.176324-0.000460j
[2025-08-22 18:47:30] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -56.246560-0.000205j
[2025-08-22 18:47:34] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -56.260608-0.002712j
[2025-08-22 18:47:39] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -56.230676-0.002235j
[2025-08-22 18:47:43] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -56.209850+0.001916j
[2025-08-22 18:47:47] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -56.234442+0.001106j
[2025-08-22 18:47:52] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -56.216399-0.002205j
[2025-08-22 18:47:56] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -56.241289-0.002565j
[2025-08-22 18:48:00] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -56.254703+0.004697j
[2025-08-22 18:48:05] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -56.069926-0.002436j
[2025-08-22 18:48:09] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -56.232682-0.003202j
[2025-08-22 18:48:13] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -56.210883-0.003225j
[2025-08-22 18:48:18] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -56.187159-0.000087j
[2025-08-22 18:48:22] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -56.214839+0.000992j
[2025-08-22 18:48:26] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -56.162505-0.000422j
[2025-08-22 18:48:31] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -56.231502-0.000536j
[2025-08-22 18:48:35] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -56.315981+0.001481j
[2025-08-22 18:48:39] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -56.243312+0.001992j
[2025-08-22 18:48:44] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -56.349149-0.000271j
[2025-08-22 18:48:48] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -56.035022+0.004865j
[2025-08-22 18:48:52] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -56.182055-0.002105j
[2025-08-22 18:48:56] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -55.955986+0.003063j
[2025-08-22 18:49:02] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -56.110359+0.001170j
[2025-08-22 18:49:06] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -56.159116-0.001806j
[2025-08-22 18:49:10] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -56.201546-0.002468j
[2025-08-22 18:49:15] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -56.241209+0.000892j
[2025-08-22 18:49:19] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -56.260824+0.000839j
[2025-08-22 18:49:23] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -56.171850+0.002732j
[2025-08-22 18:49:28] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -56.199572-0.000641j
[2025-08-22 18:49:32] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -56.151465-0.006973j
[2025-08-22 18:49:36] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -56.162183-0.004331j
[2025-08-22 18:49:40] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -56.290299+0.003664j
[2025-08-22 18:49:45] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -56.206022+0.001812j
[2025-08-22 18:49:49] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -56.235462-0.001265j
[2025-08-22 18:49:53] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -56.209632+0.000231j
[2025-08-22 18:49:58] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -56.198678-0.000394j
[2025-08-22 18:50:02] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -56.136855-0.002479j
[2025-08-22 18:50:06] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -56.135302+0.004268j
[2025-08-22 18:50:11] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -56.230331+0.002851j
[2025-08-22 18:50:15] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -56.220835-0.001857j
[2025-08-22 18:50:19] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -56.255703-0.002085j
[2025-08-22 18:50:24] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -56.190339+0.003177j
[2025-08-22 18:50:28] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -56.096047+0.003042j
[2025-08-22 18:50:32] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -56.267225-0.001562j
[2025-08-22 18:50:37] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -56.260622-0.002786j
[2025-08-22 18:50:41] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -56.202314-0.002512j
[2025-08-22 18:50:45] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -56.218398+0.004995j
[2025-08-22 18:50:50] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -56.162124+0.003237j
[2025-08-22 18:50:54] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -56.152008+0.000842j
[2025-08-22 18:50:58] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -56.155812+0.000640j
[2025-08-22 18:51:03] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -56.302222+0.003513j
[2025-08-22 18:51:07] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -56.122268-0.000159j
[2025-08-22 18:51:11] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -56.325635-0.005566j
[2025-08-22 18:51:16] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -56.266856+0.001414j
[2025-08-22 18:51:20] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -56.286474+0.003946j
[2025-08-22 18:51:24] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -56.263445+0.003013j
[2025-08-22 18:51:28] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -56.260862-0.003970j
[2025-08-22 18:51:33] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -56.391622-0.001155j
[2025-08-22 18:51:37] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -56.237418-0.003837j
[2025-08-22 18:51:41] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -56.208503+0.000361j
[2025-08-22 18:51:46] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -56.306960+0.002093j
[2025-08-22 18:51:50] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -56.235552+0.001227j
[2025-08-22 18:51:54] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -56.154891+0.000625j
[2025-08-22 18:51:59] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -56.210679+0.000117j
[2025-08-22 18:52:03] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -56.176696+0.001773j
[2025-08-22 18:52:07] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -56.084394-0.006121j
[2025-08-22 18:52:12] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -56.182221-0.004298j
[2025-08-22 18:52:12] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-22 18:52:16] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -56.147303-0.000454j
[2025-08-22 18:52:20] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -56.181259-0.002329j
[2025-08-22 18:52:25] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -56.083354+0.001697j
[2025-08-22 18:52:29] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -56.071641-0.001056j
[2025-08-22 18:52:33] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -56.154260+0.001352j
[2025-08-22 18:52:38] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -56.196924+0.001005j
[2025-08-22 18:52:42] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -56.346346-0.003698j
[2025-08-22 18:52:46] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -56.216733-0.001858j
[2025-08-22 18:52:51] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -56.217770-0.002383j
[2025-08-22 18:52:55] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -56.287814+0.001426j
[2025-08-22 18:52:59] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -56.325571-0.002646j
[2025-08-22 18:53:04] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -56.247189+0.001475j
[2025-08-22 18:53:08] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -56.191700+0.001001j
[2025-08-22 18:53:12] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -56.325453-0.002231j
[2025-08-22 18:53:17] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -56.231213-0.000524j
[2025-08-22 18:53:21] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -56.269895+0.000223j
[2025-08-22 18:53:25] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -56.214633-0.001173j
[2025-08-22 18:53:29] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -56.199796-0.002787j
[2025-08-22 18:53:34] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -56.166738-0.001601j
[2025-08-22 18:53:38] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -56.287184+0.001530j
[2025-08-22 18:53:42] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -56.195242+0.004347j
[2025-08-22 18:53:47] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -56.326481+0.005845j
[2025-08-22 18:53:51] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -56.272668-0.003770j
[2025-08-22 18:53:55] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -56.336306+0.003266j
[2025-08-22 18:54:00] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -56.380771-0.000341j
[2025-08-22 18:54:04] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -56.333575-0.002720j
[2025-08-22 18:54:08] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -56.290029+0.004622j
[2025-08-22 18:54:13] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -56.116509+0.005034j
[2025-08-22 18:54:17] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -56.331083+0.002094j
[2025-08-22 18:54:21] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -56.229435-0.001460j
[2025-08-22 18:54:26] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -56.066487+0.002446j
[2025-08-22 18:54:30] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -56.181681-0.001652j
[2025-08-22 18:54:34] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -56.154750+0.000811j
[2025-08-22 18:54:39] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -56.142966-0.000701j
[2025-08-22 18:54:43] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -56.090987-0.001053j
[2025-08-22 18:54:47] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -56.155265+0.001372j
[2025-08-22 18:54:52] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -56.226361-0.009416j
[2025-08-22 18:54:56] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -56.312059-0.003174j
[2025-08-22 18:55:00] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -56.299621-0.003622j
[2025-08-22 18:55:05] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -56.333089+0.000332j
[2025-08-22 18:55:09] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -56.144399+0.001307j
[2025-08-22 18:55:13] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -56.174564+0.003562j
[2025-08-22 18:55:18] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -56.139079-0.002396j
[2025-08-22 18:55:22] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -56.097746-0.004269j
[2025-08-22 18:55:26] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -56.161280-0.003410j
[2025-08-22 18:55:30] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -56.006541-0.001587j
[2025-08-22 18:55:35] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -56.072476-0.002742j
[2025-08-22 18:55:39] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -56.276129+0.000779j
[2025-08-22 18:55:43] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -56.191308-0.003357j
[2025-08-22 18:55:48] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -56.238360+0.000465j
[2025-08-22 18:55:52] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -56.140656-0.001035j
[2025-08-22 18:55:56] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -56.101171-0.001950j
[2025-08-22 18:56:01] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -56.206622-0.002205j
[2025-08-22 18:56:05] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -56.259707+0.000417j
[2025-08-22 18:56:09] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -56.216766+0.001612j
[2025-08-22 18:56:14] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -56.091291-0.000579j
[2025-08-22 18:56:18] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -56.160289+0.000280j
[2025-08-22 18:56:22] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -56.087444-0.006939j
[2025-08-22 18:56:27] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -56.250036-0.001340j
[2025-08-22 18:56:31] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -56.158357+0.002116j
[2025-08-22 18:56:35] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -56.114581+0.002700j
[2025-08-22 18:56:40] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -56.068780-0.001921j
[2025-08-22 18:56:44] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -56.075497-0.002774j
[2025-08-22 18:56:48] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -56.125393-0.001275j
[2025-08-22 18:56:53] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -56.145336-0.002320j
[2025-08-22 18:56:57] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -56.104159-0.004416j
[2025-08-22 18:57:01] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -56.251044-0.001489j
[2025-08-22 18:57:06] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -56.163209+0.000643j
[2025-08-22 18:57:10] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -56.176926-0.001320j
[2025-08-22 18:57:14] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -56.125723+0.005788j
[2025-08-22 18:57:18] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -56.273082+0.005238j
[2025-08-22 18:57:23] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -56.227710+0.003089j
[2025-08-22 18:57:27] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -56.216390+0.001465j
[2025-08-22 18:57:31] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -56.099473-0.001323j
[2025-08-22 18:57:36] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -56.115461-0.002906j
[2025-08-22 18:57:40] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -56.173081+0.002640j
[2025-08-22 18:57:44] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -56.143918+0.000370j
[2025-08-22 18:57:49] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -56.126707+0.001339j
[2025-08-22 18:57:53] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -56.196261+0.002653j
[2025-08-22 18:57:57] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -56.259009+0.001906j
[2025-08-22 18:58:02] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -56.298505-0.000829j
[2025-08-22 18:58:06] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -56.200882+0.002990j
[2025-08-22 18:58:10] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -56.090332-0.000321j
[2025-08-22 18:58:15] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -56.070095-0.005830j
[2025-08-22 18:58:19] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -56.107394-0.000602j
[2025-08-22 18:58:23] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -56.158329-0.000448j
[2025-08-22 18:58:28] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -56.178011+0.002631j
[2025-08-22 18:58:32] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -56.137080-0.000025j
[2025-08-22 18:58:36] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -56.139520+0.002328j
[2025-08-22 18:58:41] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -56.310511-0.000055j
[2025-08-22 18:58:45] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -56.212288+0.002436j
[2025-08-22 18:58:49] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -56.208443-0.002686j
[2025-08-22 18:58:54] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -56.168103-0.004761j
[2025-08-22 18:58:58] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -56.161261-0.001328j
[2025-08-22 18:59:02] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -56.086408-0.000397j
[2025-08-22 18:59:07] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -56.094650+0.001776j
[2025-08-22 18:59:11] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -56.164844-0.001198j
[2025-08-22 18:59:15] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -56.085352+0.002479j
[2025-08-22 18:59:19] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -56.181056-0.001344j
[2025-08-22 18:59:24] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -56.032735-0.000811j
[2025-08-22 18:59:24] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-22 18:59:28] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -56.134078-0.000073j
[2025-08-22 18:59:32] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -56.064665+0.002877j
[2025-08-22 18:59:37] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -56.187402-0.003081j
[2025-08-22 18:59:41] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -56.065860-0.000956j
[2025-08-22 18:59:45] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -56.136688+0.000076j
[2025-08-22 18:59:50] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -56.194905+0.000157j
[2025-08-22 18:59:54] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -56.222783-0.004753j
[2025-08-22 18:59:58] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -56.240278-0.004212j
[2025-08-22 19:00:03] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -56.188033-0.003704j
[2025-08-22 19:00:07] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -56.223101-0.002429j
[2025-08-22 19:00:11] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -56.131802-0.001198j
[2025-08-22 19:00:16] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -56.208395-0.004089j
[2025-08-22 19:00:20] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -56.163660-0.001484j
[2025-08-22 19:00:24] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -56.083187+0.006770j
[2025-08-22 19:00:29] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -56.169817+0.005113j
[2025-08-22 19:00:33] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -56.112106-0.003437j
[2025-08-22 19:00:37] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -56.214964-0.002702j
[2025-08-22 19:00:42] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -56.192316-0.004486j
[2025-08-22 19:00:46] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -56.350531-0.000685j
[2025-08-22 19:00:50] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -56.338345+0.005863j
[2025-08-22 19:00:55] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -56.293146+0.004117j
[2025-08-22 19:00:59] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -56.402206-0.000224j
[2025-08-22 19:01:03] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -56.124735-0.000013j
[2025-08-22 19:01:08] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -56.159281+0.001546j
[2025-08-22 19:01:12] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -56.132010-0.000586j
[2025-08-22 19:01:16] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -56.245843-0.002502j
[2025-08-22 19:01:20] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -56.127515+0.001501j
[2025-08-22 19:01:25] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -56.121184+0.003190j
[2025-08-22 19:01:29] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -56.256744+0.000749j
[2025-08-22 19:01:33] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -56.385195+0.002372j
[2025-08-22 19:01:38] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -56.162507+0.004488j
[2025-08-22 19:01:42] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -56.004443-0.003876j
[2025-08-22 19:01:46] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -56.130504+0.004060j
[2025-08-22 19:01:51] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -56.176918-0.000111j
[2025-08-22 19:01:55] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -56.152641-0.001173j
[2025-08-22 19:01:59] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -56.231648+0.002793j
[2025-08-22 19:02:04] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -56.366852-0.000386j
[2025-08-22 19:02:08] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -56.228228+0.002110j
[2025-08-22 19:02:12] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -56.317459-0.005315j
[2025-08-22 19:02:17] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -56.348047-0.002615j
[2025-08-22 19:02:21] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -56.082191-0.003747j
[2025-08-22 19:02:25] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -56.273504-0.000450j
[2025-08-22 19:02:30] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -56.320336+0.005018j
[2025-08-22 19:02:34] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -56.199564+0.001955j
[2025-08-22 19:02:39] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -56.120030-0.001759j
[2025-08-22 19:02:43] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -56.160394-0.001292j
[2025-08-22 19:02:48] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -56.160070-0.004101j
[2025-08-22 19:02:52] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -56.238670-0.000140j
[2025-08-22 19:02:56] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -56.266949-0.002126j
[2025-08-22 19:03:01] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -56.292282+0.001767j
[2025-08-22 19:03:01] ✅ Training completed | Restarts: 2
[2025-08-22 19:03:01] ============================================================
[2025-08-22 19:03:01] Training completed | Runtime: 4600.4s
[2025-08-22 19:03:02] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-22 19:03:03] ============================================================
