[2025-08-07 13:39:07] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-08-07 13:39:07]   - 迭代次数: final
[2025-08-07 13:39:07]   - 能量: -54.663975-0.000269j ± 0.043090
[2025-08-07 13:39:07]   - 时间戳: 2025-07-30T17:44:56.179657
[2025-08-07 13:39:14] ✓ 变分状态参数已从checkpoint恢复
[2025-08-07 13:39:14] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-07 13:39:14] ==================================================
[2025-08-07 13:39:14] GCNN for Shastry-Sutherland Model
[2025-08-07 13:39:14] ==================================================
[2025-08-07 13:39:14] System parameters:
[2025-08-07 13:39:14]   - System size: L=4, N=64
[2025-08-07 13:39:14]   - System parameters: J1=0.05, J2=0.0, Q=1.0
[2025-08-07 13:39:14] --------------------------------------------------
[2025-08-07 13:39:14] Model parameters:
[2025-08-07 13:39:14]   - Number of layers = 4
[2025-08-07 13:39:14]   - Number of features = 4
[2025-08-07 13:39:14]   - Total parameters = 12572
[2025-08-07 13:39:14] --------------------------------------------------
[2025-08-07 13:39:14] Training parameters:
[2025-08-07 13:39:14]   - Learning rate: 0.015
[2025-08-07 13:39:14]   - Total iterations: 1050
[2025-08-07 13:39:14]   - Annealing cycles: 3
[2025-08-07 13:39:14]   - Initial period: 150
[2025-08-07 13:39:14]   - Period multiplier: 2.0
[2025-08-07 13:39:14]   - Temperature range: 0.0-1.0
[2025-08-07 13:39:14]   - Samples: 4096
[2025-08-07 13:39:14]   - Discarded samples: 0
[2025-08-07 13:39:14]   - Chunk size: 2048
[2025-08-07 13:39:14]   - Diagonal shift: 0.2
[2025-08-07 13:39:14]   - Gradient clipping: 1.0
[2025-08-07 13:39:14]   - Checkpoint enabled: interval=100
[2025-08-07 13:39:14]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.05/training/checkpoints
[2025-08-07 13:39:14] --------------------------------------------------
[2025-08-07 13:39:14] Device status:
[2025-08-07 13:39:14]   - Devices model: A100
[2025-08-07 13:39:14]   - Number of devices: 1
[2025-08-07 13:39:14]   - Sharding: True
[2025-08-07 13:39:14] ============================================================
[2025-08-07 13:39:43] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -54.988196+0.002065j
[2025-08-07 13:40:01] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -54.844056-0.004215j
[2025-08-07 13:40:05] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -54.805144-0.010160j
[2025-08-07 13:40:09] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -54.827018-0.004310j
[2025-08-07 13:40:13] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -55.047797-0.004789j
[2025-08-07 13:40:17] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -55.103218-0.010280j
[2025-08-07 13:40:21] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -55.069577-0.002684j
[2025-08-07 13:40:26] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -55.006124+0.006186j
[2025-08-07 13:40:30] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -55.022843-0.007102j
[2025-08-07 13:40:34] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -54.964583+0.004871j
[2025-08-07 13:40:38] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -54.954588-0.005916j
[2025-08-07 13:40:42] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -55.048977-0.000714j
[2025-08-07 13:40:46] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -54.981102-0.006935j
[2025-08-07 13:40:50] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -55.000768-0.001456j
[2025-08-07 13:40:55] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -54.907461-0.002919j
[2025-08-07 13:40:59] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -54.883869-0.000236j
[2025-08-07 13:41:03] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -54.905633-0.006295j
[2025-08-07 13:41:07] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -54.858802-0.005645j
[2025-08-07 13:41:11] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -54.916949+0.001520j
[2025-08-07 13:41:15] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -54.942178+0.004220j
[2025-08-07 13:41:20] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -54.866252-0.002834j
[2025-08-07 13:41:24] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -55.053620-0.002149j
[2025-08-07 13:41:28] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -54.989962+0.003488j
[2025-08-07 13:41:32] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -54.946357-0.002449j
[2025-08-07 13:41:36] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -54.974634+0.002214j
[2025-08-07 13:41:40] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -54.894357-0.001002j
[2025-08-07 13:41:44] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -54.925691-0.007454j
[2025-08-07 13:41:49] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -54.939388+0.000472j
[2025-08-07 13:41:53] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -54.992324-0.003423j
[2025-08-07 13:41:57] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -55.005938+0.001477j
[2025-08-07 13:42:01] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -54.974521-0.002978j
[2025-08-07 13:42:05] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -54.947393+0.003836j
[2025-08-07 13:42:09] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -54.997369+0.003318j
[2025-08-07 13:42:14] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -55.053965-0.008405j
[2025-08-07 13:42:18] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -54.983647-0.000592j
[2025-08-07 13:42:22] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -54.979820-0.000404j
[2025-08-07 13:42:26] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -55.043932+0.002993j
[2025-08-07 13:42:30] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.901383-0.000157j
[2025-08-07 13:42:34] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -54.938853-0.001546j
[2025-08-07 13:42:39] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -55.034213-0.000066j
[2025-08-07 13:42:43] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -54.963616+0.000719j
[2025-08-07 13:42:47] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -54.881801+0.001266j
[2025-08-07 13:42:51] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -54.932131+0.000642j
[2025-08-07 13:42:55] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -54.920683-0.000796j
[2025-08-07 13:42:59] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -54.981882-0.005014j
[2025-08-07 13:43:04] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -55.003958-0.004656j
[2025-08-07 13:43:08] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -55.087511+0.000067j
[2025-08-07 13:43:12] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -54.989126+0.000071j
[2025-08-07 13:43:16] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -55.139528-0.002886j
[2025-08-07 13:43:20] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -55.070181+0.000558j
[2025-08-07 13:43:24] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -55.071003+0.004927j
[2025-08-07 13:43:28] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -55.048690+0.005027j
[2025-08-07 13:43:33] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -55.037800+0.003459j
[2025-08-07 13:43:37] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -54.968972-0.005337j
[2025-08-07 13:43:41] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -54.885302+0.003857j
[2025-08-07 13:43:45] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -54.933716+0.004495j
[2025-08-07 13:43:49] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -54.798049+0.004228j
[2025-08-07 13:43:53] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -54.749745-0.004304j
[2025-08-07 13:43:58] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -54.862396+0.003028j
[2025-08-07 13:44:02] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -54.898017+0.000054j
[2025-08-07 13:44:06] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -54.905478+0.003007j
[2025-08-07 13:44:10] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -55.046124+0.007661j
[2025-08-07 13:44:14] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -54.934016-0.000821j
[2025-08-07 13:44:18] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -55.060050-0.006879j
[2025-08-07 13:44:22] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -55.040434-0.006859j
[2025-08-07 13:44:27] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -55.087747+0.001579j
[2025-08-07 13:44:31] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -55.047312-0.001179j
[2025-08-07 13:44:35] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -55.106545+0.000940j
[2025-08-07 13:44:39] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -55.149033+0.003279j
[2025-08-07 13:44:43] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -55.035027-0.002771j
[2025-08-07 13:44:47] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -55.121781-0.001115j
[2025-08-07 13:44:51] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -54.996288+0.001488j
[2025-08-07 13:44:56] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -54.946682-0.004927j
[2025-08-07 13:45:00] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -55.089062-0.001467j
[2025-08-07 13:45:04] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -55.008787+0.003175j
[2025-08-07 13:45:08] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -55.044356-0.000747j
[2025-08-07 13:45:12] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -55.037593+0.000324j
[2025-08-07 13:45:16] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -55.103673+0.004629j
[2025-08-07 13:45:21] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -55.093477-0.000986j
[2025-08-07 13:45:25] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -55.032346+0.000540j
[2025-08-07 13:45:29] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -54.906439-0.004344j
[2025-08-07 13:45:33] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -55.059801-0.001760j
[2025-08-07 13:45:37] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -55.040439+0.000450j
[2025-08-07 13:45:41] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -55.048839-0.007573j
[2025-08-07 13:45:45] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -54.928326-0.000366j
[2025-08-07 13:45:50] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -55.012764-0.006661j
[2025-08-07 13:45:54] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -55.040555-0.002095j
[2025-08-07 13:45:58] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -54.941048+0.000314j
[2025-08-07 13:46:02] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -54.938440-0.000005j
[2025-08-07 13:46:06] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -54.846595-0.003002j
[2025-08-07 13:46:10] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -54.973082-0.002404j
[2025-08-07 13:46:14] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -54.897716+0.005585j
[2025-08-07 13:46:19] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -54.897549+0.003357j
[2025-08-07 13:46:23] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -54.899841+0.002741j
[2025-08-07 13:46:27] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -54.958466-0.003861j
[2025-08-07 13:46:31] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -55.041162+0.001424j
[2025-08-07 13:46:35] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -55.032367+0.005482j
[2025-08-07 13:46:39] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -54.954652+0.004956j
[2025-08-07 13:46:44] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -54.876845-0.005451j
[2025-08-07 13:46:48] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -54.968907-0.004736j
[2025-08-07 13:46:48] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-07 13:46:52] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -54.989023-0.007464j
[2025-08-07 13:46:56] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -54.993702-0.003336j
[2025-08-07 13:47:00] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -55.061065+0.003281j
[2025-08-07 13:47:05] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -55.128632-0.001017j
[2025-08-07 13:47:09] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -54.999519+0.003713j
[2025-08-07 13:47:13] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -55.156904+0.001858j
[2025-08-07 13:47:17] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -54.985988-0.000303j
[2025-08-07 13:47:21] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -55.142634+0.001333j
[2025-08-07 13:47:25] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -54.976408+0.005577j
[2025-08-07 13:47:29] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -54.885686+0.005811j
[2025-08-07 13:47:34] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -55.009243+0.003134j
[2025-08-07 13:47:38] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -55.030608-0.005890j
[2025-08-07 13:47:42] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -54.944510+0.002113j
[2025-08-07 13:47:46] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -54.986251-0.002591j
[2025-08-07 13:47:50] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -54.933978-0.001487j
[2025-08-07 13:47:54] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -54.797330-0.002133j
[2025-08-07 13:47:59] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -54.860911+0.002787j
[2025-08-07 13:48:03] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -54.936381+0.002374j
[2025-08-07 13:48:07] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -54.866284+0.000189j
[2025-08-07 13:48:11] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -54.944797+0.005442j
[2025-08-07 13:48:15] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -54.964870+0.002565j
[2025-08-07 13:48:19] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -54.854060+0.000699j
[2025-08-07 13:48:23] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -54.894530-0.001976j
[2025-08-07 13:48:28] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -54.908213+0.004479j
[2025-08-07 13:48:32] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -55.016499-0.005570j
[2025-08-07 13:48:36] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -54.978677+0.000736j
[2025-08-07 13:48:40] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -54.980828+0.000478j
[2025-08-07 13:48:44] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -54.975945-0.001975j
[2025-08-07 13:48:48] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -54.924170+0.005143j
[2025-08-07 13:48:52] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -54.951294+0.001026j
[2025-08-07 13:48:57] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -55.036404+0.003901j
[2025-08-07 13:49:01] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -55.012541+0.005147j
[2025-08-07 13:49:05] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -54.891434-0.002625j
[2025-08-07 13:49:09] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -54.914822+0.000066j
[2025-08-07 13:49:13] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -54.938517-0.001851j
[2025-08-07 13:49:17] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -54.832680+0.002827j
[2025-08-07 13:49:21] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -55.004225+0.002962j
[2025-08-07 13:49:26] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -54.874812-0.005000j
[2025-08-07 13:49:30] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -54.863091-0.000936j
[2025-08-07 13:49:34] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -54.912446+0.008151j
[2025-08-07 13:49:38] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -54.844258+0.002942j
[2025-08-07 13:49:42] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -54.819332+0.000107j
[2025-08-07 13:49:46] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -54.905393+0.000543j
[2025-08-07 13:49:50] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -54.995731+0.005653j
[2025-08-07 13:49:55] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -54.922363-0.000028j
[2025-08-07 13:49:59] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -54.985879+0.001890j
[2025-08-07 13:50:03] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -55.154796+0.001847j
[2025-08-07 13:50:07] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -54.992593+0.000232j
[2025-08-07 13:50:11] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -54.880043-0.000880j
[2025-08-07 13:50:15] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -54.958391+0.001221j
[2025-08-07 13:50:15] RESTART #1 | Period: 300
[2025-08-07 13:50:20] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -54.923584-0.000925j
[2025-08-07 13:50:24] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -54.945865+0.007292j
[2025-08-07 13:50:28] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -54.920107-0.002945j
[2025-08-07 13:50:32] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -54.928261-0.000944j
[2025-08-07 13:50:36] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -54.889064+0.000576j
[2025-08-07 13:50:40] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -54.873270-0.000283j
[2025-08-07 13:50:44] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -54.891248+0.001852j
[2025-08-07 13:50:49] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -55.057813+0.002472j
[2025-08-07 13:50:53] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -54.996836+0.005554j
[2025-08-07 13:50:57] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -54.987823-0.001433j
[2025-08-07 13:51:01] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -55.011796-0.005672j
[2025-08-07 13:51:05] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -54.954616+0.000158j
[2025-08-07 13:51:09] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -54.954301+0.000077j
[2025-08-07 13:51:14] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -55.114700+0.007295j
[2025-08-07 13:51:18] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -55.014999-0.000978j
[2025-08-07 13:51:22] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -55.069590+0.000002j
[2025-08-07 13:51:26] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -55.132238+0.006312j
[2025-08-07 13:51:30] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -54.931883+0.000769j
[2025-08-07 13:51:34] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -55.012649+0.003150j
[2025-08-07 13:51:39] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -55.078706+0.000374j
[2025-08-07 13:51:43] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -55.121899+0.002352j
[2025-08-07 13:51:47] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -55.067268+0.000921j
[2025-08-07 13:51:51] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -55.047616-0.001137j
[2025-08-07 13:51:55] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -54.998724+0.007522j
[2025-08-07 13:51:59] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -55.011732+0.006516j
[2025-08-07 13:52:04] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -54.985805+0.008504j
[2025-08-07 13:52:08] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -55.092602+0.001989j
[2025-08-07 13:52:12] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -55.016677-0.004203j
[2025-08-07 13:52:16] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -55.121010+0.003603j
[2025-08-07 13:52:20] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -55.022612-0.001651j
[2025-08-07 13:52:24] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -55.020509-0.004335j
[2025-08-07 13:52:28] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -55.053090+0.000490j
[2025-08-07 13:52:33] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -55.027166+0.003263j
[2025-08-07 13:52:37] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -55.131302+0.002018j
[2025-08-07 13:52:41] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -54.971995-0.005072j
[2025-08-07 13:52:45] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -55.094697+0.002553j
[2025-08-07 13:52:49] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -55.109382+0.003647j
[2025-08-07 13:52:53] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -55.126102-0.001047j
[2025-08-07 13:52:58] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -55.054897+0.005251j
[2025-08-07 13:53:02] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -55.029925-0.000794j
[2025-08-07 13:53:06] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -55.004424+0.003231j
[2025-08-07 13:53:10] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -55.071593+0.001786j
[2025-08-07 13:53:14] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -55.002665+0.000739j
[2025-08-07 13:53:18] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -54.918936-0.001759j
[2025-08-07 13:53:22] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -54.987986-0.000713j
[2025-08-07 13:53:27] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -54.992036+0.000745j
[2025-08-07 13:53:31] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -54.917519-0.010031j
[2025-08-07 13:53:35] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -54.887378-0.005094j
[2025-08-07 13:53:39] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -54.834290-0.000929j
[2025-08-07 13:53:43] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -54.958590-0.000553j
[2025-08-07 13:53:43] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-07 13:53:47] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -55.017916-0.002800j
[2025-08-07 13:53:51] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -55.033831-0.001024j
[2025-08-07 13:53:56] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -55.083835-0.001969j
[2025-08-07 13:54:00] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -55.161308+0.003354j
[2025-08-07 13:54:04] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -55.108229+0.000401j
[2025-08-07 13:54:08] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -55.146656+0.001459j
[2025-08-07 13:54:12] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -55.159571+0.006271j
[2025-08-07 13:54:16] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -55.089067-0.000207j
[2025-08-07 13:54:20] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -54.986001+0.005148j
[2025-08-07 13:54:25] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -55.176406+0.004857j
[2025-08-07 13:54:29] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -55.091121+0.000509j
[2025-08-07 13:54:33] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -55.018460+0.000540j
[2025-08-07 13:54:37] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -55.040169-0.004652j
[2025-08-07 13:54:41] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -55.132584-0.004788j
[2025-08-07 13:54:45] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -55.171158-0.000528j
[2025-08-07 13:54:49] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -55.183136+0.006831j
[2025-08-07 13:54:54] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -55.155332-0.005046j
[2025-08-07 13:54:58] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -55.184634-0.007873j
[2025-08-07 13:55:02] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -55.130175+0.000331j
[2025-08-07 13:55:06] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -55.176823-0.002312j
[2025-08-07 13:55:10] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -55.174585-0.002663j
[2025-08-07 13:55:14] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -55.038525+0.000531j
[2025-08-07 13:55:18] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -55.117858-0.000312j
[2025-08-07 13:55:23] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -55.058467+0.003016j
[2025-08-07 13:55:27] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -55.023703-0.005454j
[2025-08-07 13:55:31] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -55.009068-0.004225j
[2025-08-07 13:55:35] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -55.045623-0.003434j
[2025-08-07 13:55:39] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -55.099278-0.005699j
[2025-08-07 13:55:43] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -54.977424-0.003855j
[2025-08-07 13:55:48] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -55.002457+0.003959j
[2025-08-07 13:55:52] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -55.071953+0.003199j
[2025-08-07 13:55:56] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -55.093066-0.002524j
[2025-08-07 13:56:00] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -55.038324+0.003735j
[2025-08-07 13:56:04] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -55.016997-0.000843j
[2025-08-07 13:56:08] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -55.151236+0.010021j
[2025-08-07 13:56:12] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -55.023108-0.001751j
[2025-08-07 13:56:17] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -54.924107-0.001384j
[2025-08-07 13:56:21] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -55.043204+0.000089j
[2025-08-07 13:56:25] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -54.975943+0.002188j
[2025-08-07 13:56:29] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -55.027326-0.007013j
[2025-08-07 13:56:33] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -54.960542+0.001323j
[2025-08-07 13:56:37] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -54.946012-0.001160j
[2025-08-07 13:56:41] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -54.931570-0.005137j
[2025-08-07 13:56:46] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -54.839408-0.004933j
[2025-08-07 13:56:50] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -54.873951-0.005959j
[2025-08-07 13:56:54] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -54.962635+0.001511j
[2025-08-07 13:56:58] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -55.037143-0.002606j
[2025-08-07 13:57:02] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -55.028016+0.000632j
[2025-08-07 13:57:06] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -54.954164-0.000150j
[2025-08-07 13:57:11] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -55.031843-0.001137j
[2025-08-07 13:57:15] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -54.953292+0.000856j
[2025-08-07 13:57:19] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -54.988566-0.005658j
[2025-08-07 13:57:23] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -55.086470-0.001202j
[2025-08-07 13:57:27] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -55.033932-0.001958j
[2025-08-07 13:57:31] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -55.077362+0.000808j
[2025-08-07 13:57:35] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -55.020675+0.001770j
[2025-08-07 13:57:40] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -55.134205-0.005413j
[2025-08-07 13:57:44] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -55.027368+0.002965j
[2025-08-07 13:57:48] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -55.088695-0.000711j
[2025-08-07 13:57:52] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -55.090813-0.000300j
[2025-08-07 13:57:56] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -54.970419-0.001300j
[2025-08-07 13:58:00] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -55.055879+0.003473j
[2025-08-07 13:58:04] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -55.037462+0.000209j
[2025-08-07 13:58:09] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -55.023678+0.000180j
[2025-08-07 13:58:13] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -55.051532-0.006449j
[2025-08-07 13:58:17] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -55.074035-0.002232j
[2025-08-07 13:58:21] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -55.122934-0.003777j
[2025-08-07 13:58:25] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -54.927530-0.000896j
[2025-08-07 13:58:29] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -54.964668+0.004780j
[2025-08-07 13:58:34] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -54.865129+0.004947j
[2025-08-07 13:58:38] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -54.974614+0.001857j
[2025-08-07 13:58:42] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -54.900611-0.001559j
[2025-08-07 13:58:46] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -54.824897-0.002093j
[2025-08-07 13:58:50] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -54.881712-0.004192j
[2025-08-07 13:58:54] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -54.852924-0.000138j
[2025-08-07 13:58:58] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -54.970085-0.004208j
[2025-08-07 13:59:03] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -54.908266+0.000321j
[2025-08-07 13:59:07] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -54.863556+0.005486j
[2025-08-07 13:59:11] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -54.919944+0.003146j
[2025-08-07 13:59:15] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -54.867074-0.000243j
[2025-08-07 13:59:19] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -55.027222+0.001878j
[2025-08-07 13:59:23] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -54.987106-0.002097j
[2025-08-07 13:59:28] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -54.898748-0.002582j
[2025-08-07 13:59:32] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -54.939669+0.000938j
[2025-08-07 13:59:36] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -54.911150+0.001167j
[2025-08-07 13:59:40] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -54.945698-0.000186j
[2025-08-07 13:59:44] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -55.074702-0.000858j
[2025-08-07 13:59:48] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -55.001429-0.006511j
[2025-08-07 13:59:52] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -54.980164-0.001520j
[2025-08-07 13:59:57] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -54.955218-0.001910j
[2025-08-07 14:00:01] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -55.011096+0.000591j
[2025-08-07 14:00:05] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -54.892181-0.000385j
[2025-08-07 14:00:09] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -54.886750+0.000237j
[2025-08-07 14:00:13] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -54.952244+0.001383j
[2025-08-07 14:00:17] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -54.960714-0.000439j
[2025-08-07 14:00:21] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -54.987372+0.001562j
[2025-08-07 14:00:26] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -54.932665+0.003049j
[2025-08-07 14:00:30] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -54.993040-0.007323j
[2025-08-07 14:00:34] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -54.988448-0.005053j
[2025-08-07 14:00:38] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -55.031578+0.001756j
[2025-08-07 14:00:38] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-07 14:00:42] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -55.018115-0.000632j
[2025-08-07 14:00:46] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -54.906975+0.001481j
[2025-08-07 14:00:50] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -54.897659-0.001935j
[2025-08-07 14:00:55] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -54.949958-0.002937j
[2025-08-07 14:00:59] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -54.966295-0.001277j
[2025-08-07 14:01:03] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -54.824397+0.005723j
[2025-08-07 14:01:07] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -54.854415-0.000696j
[2025-08-07 14:01:11] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -54.924788-0.000524j
[2025-08-07 14:01:15] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -54.946972-0.003670j
[2025-08-07 14:01:20] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -54.944789-0.002003j
[2025-08-07 14:01:24] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -54.998179-0.000386j
[2025-08-07 14:01:28] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -55.015891+0.001353j
[2025-08-07 14:01:32] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -55.029849+0.000947j
[2025-08-07 14:01:36] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -55.011362+0.003374j
[2025-08-07 14:01:40] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -55.103289-0.000958j
[2025-08-07 14:01:44] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -55.116885-0.005492j
[2025-08-07 14:01:49] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -55.056032+0.000222j
[2025-08-07 14:01:53] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -55.006278+0.002798j
[2025-08-07 14:01:57] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -55.104897+0.007379j
[2025-08-07 14:02:01] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -55.026666-0.015700j
[2025-08-07 14:02:05] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -55.026267-0.002984j
[2025-08-07 14:02:09] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -54.972781-0.003196j
[2025-08-07 14:02:14] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -54.962994-0.005410j
[2025-08-07 14:02:18] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -54.960279+0.002629j
[2025-08-07 14:02:22] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -55.017027-0.002619j
[2025-08-07 14:02:26] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -54.973672+0.000275j
[2025-08-07 14:02:30] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -55.039895-0.001784j
[2025-08-07 14:02:34] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -55.040202+0.000602j
[2025-08-07 14:02:39] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -55.033069-0.000943j
[2025-08-07 14:02:43] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -54.943729+0.000323j
[2025-08-07 14:02:47] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -54.888057-0.001467j
[2025-08-07 14:02:51] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -54.882690-0.004284j
[2025-08-07 14:02:55] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -55.005932+0.003843j
[2025-08-07 14:02:59] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -55.028292+0.003675j
[2025-08-07 14:03:04] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -54.975449+0.001867j
[2025-08-07 14:03:08] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -55.024726+0.002732j
[2025-08-07 14:03:12] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -55.029152+0.000587j
[2025-08-07 14:03:16] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -54.946867-0.002494j
[2025-08-07 14:03:20] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -54.924898-0.002798j
[2025-08-07 14:03:24] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -54.898862-0.002857j
[2025-08-07 14:03:28] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -54.952062+0.001924j
[2025-08-07 14:03:33] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -54.978273+0.006948j
[2025-08-07 14:03:37] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -54.945469+0.002455j
[2025-08-07 14:03:41] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -54.990120-0.001238j
[2025-08-07 14:03:45] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -55.011095+0.001999j
[2025-08-07 14:03:49] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -54.976004+0.001356j
[2025-08-07 14:03:53] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -55.017486-0.001601j
[2025-08-07 14:03:58] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -55.098710-0.002947j
[2025-08-07 14:04:02] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -54.951016-0.001001j
[2025-08-07 14:04:06] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -54.874231+0.000475j
[2025-08-07 14:04:10] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -54.938388+0.002282j
[2025-08-07 14:04:14] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -54.979248-0.002940j
[2025-08-07 14:04:18] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -54.997396-0.000136j
[2025-08-07 14:04:22] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -54.913694+0.001889j
[2025-08-07 14:04:27] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -54.974748+0.003284j
[2025-08-07 14:04:31] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -54.992904-0.003888j
[2025-08-07 14:04:35] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -55.037024-0.001115j
[2025-08-07 14:04:39] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -55.096338+0.002597j
[2025-08-07 14:04:43] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -55.091394-0.001801j
[2025-08-07 14:04:47] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -55.079384-0.004462j
[2025-08-07 14:04:51] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -55.021158-0.004914j
[2025-08-07 14:04:56] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -54.949593-0.005716j
[2025-08-07 14:05:00] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -55.008411-0.000407j
[2025-08-07 14:05:04] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -55.026748-0.001041j
[2025-08-07 14:05:08] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -54.950464-0.001289j
[2025-08-07 14:05:12] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -54.897343+0.003464j
[2025-08-07 14:05:16] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -54.829356+0.008342j
[2025-08-07 14:05:20] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -55.052012+0.001864j
[2025-08-07 14:05:25] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -55.019323-0.000419j
[2025-08-07 14:05:29] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -55.010958+0.002084j
[2025-08-07 14:05:33] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -54.986983-0.002005j
[2025-08-07 14:05:37] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -55.054229-0.001676j
[2025-08-07 14:05:41] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -54.946524-0.000904j
[2025-08-07 14:05:45] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -55.068973-0.004953j
[2025-08-07 14:05:49] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -55.107230+0.002068j
[2025-08-07 14:05:54] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -55.034606+0.004712j
[2025-08-07 14:05:58] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -55.104831+0.004092j
[2025-08-07 14:06:02] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -55.035793+0.002252j
[2025-08-07 14:06:06] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -55.035385+0.003045j
[2025-08-07 14:06:10] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -55.133829+0.002944j
[2025-08-07 14:06:14] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -55.156347+0.000504j
[2025-08-07 14:06:19] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -54.996644+0.000720j
[2025-08-07 14:06:23] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -54.972618+0.002269j
[2025-08-07 14:06:27] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -55.019392+0.003158j
[2025-08-07 14:06:31] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -55.000023+0.002195j
[2025-08-07 14:06:35] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -54.873039-0.004286j
[2025-08-07 14:06:39] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -54.818618+0.001009j
[2025-08-07 14:06:44] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -54.837034+0.005698j
[2025-08-07 14:06:48] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -54.806143-0.003955j
[2025-08-07 14:06:52] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -54.871843-0.000871j
[2025-08-07 14:06:56] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -55.045630-0.007793j
[2025-08-07 14:07:00] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -55.111205+0.002240j
[2025-08-07 14:07:04] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -55.122746+0.004025j
[2025-08-07 14:07:09] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -55.018952-0.002287j
[2025-08-07 14:07:13] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -55.031504+0.003334j
[2025-08-07 14:07:17] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -55.034672-0.003602j
[2025-08-07 14:07:21] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -55.069915-0.003159j
[2025-08-07 14:07:25] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -55.047599+0.004811j
[2025-08-07 14:07:29] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -55.012724+0.000460j
[2025-08-07 14:07:34] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -55.002148-0.001725j
[2025-08-07 14:07:34] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-07 14:07:38] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -55.039975+0.005219j
[2025-08-07 14:07:42] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -54.973901-0.000454j
[2025-08-07 14:07:46] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -54.979985-0.000197j
[2025-08-07 14:07:50] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -55.067674+0.000993j
[2025-08-07 14:07:54] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -55.095465-0.006941j
[2025-08-07 14:07:58] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -55.074313-0.001393j
[2025-08-07 14:08:03] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -55.069865-0.001637j
[2025-08-07 14:08:07] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -55.089359-0.003883j
[2025-08-07 14:08:11] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -54.929456+0.002926j
[2025-08-07 14:08:15] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -55.019386-0.000307j
[2025-08-07 14:08:19] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -55.089404-0.002376j
[2025-08-07 14:08:23] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -55.076581+0.000663j
[2025-08-07 14:08:27] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -54.993013-0.002446j
[2025-08-07 14:08:32] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -55.111191+0.003845j
[2025-08-07 14:08:36] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -54.944800+0.002213j
[2025-08-07 14:08:40] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -54.928355+0.000473j
[2025-08-07 14:08:44] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -55.004256+0.001073j
[2025-08-07 14:08:48] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -55.169300+0.005548j
[2025-08-07 14:08:52] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -55.114825+0.002386j
[2025-08-07 14:08:57] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -55.159343+0.004210j
[2025-08-07 14:09:01] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -55.139120-0.000777j
[2025-08-07 14:09:05] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -55.203609-0.006670j
[2025-08-07 14:09:09] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -55.112311+0.000897j
[2025-08-07 14:09:13] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -55.178993+0.004436j
[2025-08-07 14:09:17] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -55.045150-0.003776j
[2025-08-07 14:09:21] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -55.028529+0.002583j
[2025-08-07 14:09:26] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -55.051960-0.001766j
[2025-08-07 14:09:30] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -55.071061-0.000150j
[2025-08-07 14:09:34] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -55.049744-0.000342j
[2025-08-07 14:09:38] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -55.088712-0.002313j
[2025-08-07 14:09:42] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -55.058318-0.002080j
[2025-08-07 14:09:46] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -55.097429-0.002795j
[2025-08-07 14:09:50] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -55.082330-0.001112j
[2025-08-07 14:09:55] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -55.127468+0.006368j
[2025-08-07 14:09:59] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -54.997667-0.002055j
[2025-08-07 14:10:03] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -54.956486+0.000771j
[2025-08-07 14:10:07] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -54.957662+0.001422j
[2025-08-07 14:10:11] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -55.009099+0.003826j
[2025-08-07 14:10:15] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -55.065658-0.002053j
[2025-08-07 14:10:19] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -55.073796+0.001236j
[2025-08-07 14:10:24] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -55.060319+0.000900j
[2025-08-07 14:10:28] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -55.032733-0.005476j
[2025-08-07 14:10:32] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -55.023408-0.000991j
[2025-08-07 14:10:36] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -55.059642+0.000808j
[2025-08-07 14:10:40] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -55.058054+0.005868j
[2025-08-07 14:10:44] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -55.064649+0.004904j
[2025-08-07 14:10:49] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -54.967634-0.002177j
[2025-08-07 14:10:53] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -55.007405-0.004717j
[2025-08-07 14:10:57] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -54.999501+0.004804j
[2025-08-07 14:11:01] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -55.163299+0.004940j
[2025-08-07 14:11:01] RESTART #2 | Period: 600
[2025-08-07 14:11:05] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -55.004980-0.001443j
[2025-08-07 14:11:09] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -54.933709+0.001225j
[2025-08-07 14:11:13] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -54.977431-0.002015j
[2025-08-07 14:11:18] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -54.989094-0.005712j
[2025-08-07 14:11:22] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -55.130742-0.009731j
[2025-08-07 14:11:26] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -55.124599-0.001265j
[2025-08-07 14:11:30] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -55.016827+0.002109j
[2025-08-07 14:11:34] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -55.017764-0.000477j
[2025-08-07 14:11:38] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -54.862652+0.000611j
[2025-08-07 14:11:42] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -55.004537+0.002449j
[2025-08-07 14:11:47] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -55.016266-0.000568j
[2025-08-07 14:11:51] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -55.067431-0.001399j
[2025-08-07 14:11:55] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -55.086677-0.001939j
[2025-08-07 14:11:59] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -55.057073-0.007358j
[2025-08-07 14:12:03] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -55.054182-0.003561j
[2025-08-07 14:12:07] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -55.033901+0.001272j
[2025-08-07 14:12:11] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -54.903384+0.002360j
[2025-08-07 14:12:16] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -54.970144-0.000016j
[2025-08-07 14:12:20] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -55.045428+0.000705j
[2025-08-07 14:12:24] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -55.002520+0.001025j
[2025-08-07 14:12:28] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -54.975019+0.000727j
[2025-08-07 14:12:32] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -54.981495+0.000218j
[2025-08-07 14:12:36] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -55.047936-0.002273j
[2025-08-07 14:12:41] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -55.119583-0.006719j
[2025-08-07 14:12:45] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -55.122988-0.000486j
[2025-08-07 14:12:49] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -55.107939+0.003266j
[2025-08-07 14:12:53] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -55.037650+0.000426j
[2025-08-07 14:12:57] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -55.024134+0.001269j
[2025-08-07 14:13:01] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -54.998244+0.000913j
[2025-08-07 14:13:05] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -55.154524+0.001731j
[2025-08-07 14:13:10] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -55.064141-0.000968j
[2025-08-07 14:13:14] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -55.032491+0.001469j
[2025-08-07 14:13:18] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -54.964392-0.003051j
[2025-08-07 14:13:22] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -55.031578+0.001230j
[2025-08-07 14:13:26] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -55.041834+0.000039j
[2025-08-07 14:13:30] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -55.019503+0.000881j
[2025-08-07 14:13:35] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -54.912705-0.000593j
[2025-08-07 14:13:39] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -54.968251+0.001711j
[2025-08-07 14:13:43] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -54.977014+0.001064j
[2025-08-07 14:13:47] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -54.998358-0.000418j
[2025-08-07 14:13:51] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -54.985600-0.002535j
[2025-08-07 14:13:55] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -54.892706-0.001999j
[2025-08-07 14:13:59] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -54.914368-0.004412j
[2025-08-07 14:14:04] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -54.904215+0.001471j
[2025-08-07 14:14:08] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -55.003905-0.003794j
[2025-08-07 14:14:12] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -55.015758+0.002123j
[2025-08-07 14:14:16] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -55.111767-0.001940j
[2025-08-07 14:14:20] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -55.096657-0.004002j
[2025-08-07 14:14:24] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -55.057643-0.000088j
[2025-08-07 14:14:28] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -54.942867-0.000766j
[2025-08-07 14:14:28] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-07 14:14:33] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -54.963841-0.001357j
[2025-08-07 14:14:37] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -54.991957+0.000271j
[2025-08-07 14:14:41] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -54.967244-0.003852j
[2025-08-07 14:14:45] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -54.961384-0.001459j
[2025-08-07 14:14:49] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -55.012291-0.001015j
[2025-08-07 14:14:53] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -54.859539-0.005857j
[2025-08-07 14:14:58] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -54.964209-0.002966j
[2025-08-07 14:15:02] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -54.939601-0.002415j
[2025-08-07 14:15:06] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -55.033760-0.003996j
[2025-08-07 14:15:10] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -55.027490+0.003638j
[2025-08-07 14:15:14] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -54.969765+0.001608j
[2025-08-07 14:15:18] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -54.972358+0.001496j
[2025-08-07 14:15:22] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -54.932122-0.000416j
[2025-08-07 14:15:27] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -54.914226+0.001352j
[2025-08-07 14:15:31] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -54.819528+0.003972j
[2025-08-07 14:15:35] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -54.793728-0.004546j
[2025-08-07 14:15:39] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -54.833470+0.002762j
[2025-08-07 14:15:43] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -54.880326-0.002197j
[2025-08-07 14:15:47] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -54.718975+0.003500j
[2025-08-07 14:15:51] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -54.785458-0.000521j
[2025-08-07 14:15:56] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -54.899274+0.002202j
[2025-08-07 14:16:00] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -54.928075-0.004500j
[2025-08-07 14:16:04] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -55.118446-0.000798j
[2025-08-07 14:16:08] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -55.078206+0.001544j
[2025-08-07 14:16:12] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -55.142428-0.003031j
[2025-08-07 14:16:16] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -55.157661+0.001428j
[2025-08-07 14:16:20] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -55.109936+0.001135j
[2025-08-07 14:16:25] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -55.161955-0.004927j
[2025-08-07 14:16:29] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -55.119941-0.002122j
[2025-08-07 14:16:33] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -55.033258-0.000447j
[2025-08-07 14:16:37] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -54.998762-0.004346j
[2025-08-07 14:16:41] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -55.035806+0.000668j
[2025-08-07 14:16:45] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -55.082967-0.002149j
[2025-08-07 14:16:49] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -54.829763-0.001545j
[2025-08-07 14:16:54] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -54.933551-0.000899j
[2025-08-07 14:16:58] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -54.886836-0.003958j
[2025-08-07 14:17:02] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -54.871168-0.003635j
[2025-08-07 14:17:06] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -54.872271-0.002881j
[2025-08-07 14:17:10] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -54.897076+0.010509j
[2025-08-07 14:17:14] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -54.963403-0.003842j
[2025-08-07 14:17:18] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -55.006346-0.002386j
[2025-08-07 14:17:23] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.958306+0.003454j
[2025-08-07 14:17:27] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -54.953525-0.001019j
[2025-08-07 14:17:31] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -55.041095-0.000138j
[2025-08-07 14:17:35] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -54.898531+0.003577j
[2025-08-07 14:17:39] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -54.901720-0.005447j
[2025-08-07 14:17:43] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -54.851265-0.001029j
[2025-08-07 14:17:48] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -54.952220+0.004597j
[2025-08-07 14:17:52] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -54.858761-0.000016j
[2025-08-07 14:17:56] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -54.783071+0.000409j
[2025-08-07 14:18:00] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -54.729467+0.001232j
[2025-08-07 14:18:04] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -54.936578-0.001582j
[2025-08-07 14:18:08] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -54.990777-0.001825j
[2025-08-07 14:18:12] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -55.019409+0.002916j
[2025-08-07 14:18:17] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -55.018122-0.002238j
[2025-08-07 14:18:21] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -54.871104-0.004054j
[2025-08-07 14:18:25] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -54.910681-0.003636j
[2025-08-07 14:18:29] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -54.940233-0.003631j
[2025-08-07 14:18:33] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -54.830885-0.004504j
[2025-08-07 14:18:37] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -54.868906-0.000338j
[2025-08-07 14:18:42] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -54.990120+0.003339j
[2025-08-07 14:18:46] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -55.004068-0.003956j
[2025-08-07 14:18:50] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -55.083846+0.002644j
[2025-08-07 14:18:54] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -54.956458-0.001004j
[2025-08-07 14:18:58] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -55.139505-0.002853j
[2025-08-07 14:19:02] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -55.048375-0.005057j
[2025-08-07 14:19:06] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -55.025462+0.000781j
[2025-08-07 14:19:11] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -55.050770+0.000421j
[2025-08-07 14:19:15] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -55.073687+0.001575j
[2025-08-07 14:19:19] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -55.160112-0.000562j
[2025-08-07 14:19:23] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -55.065657-0.000139j
[2025-08-07 14:19:27] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -54.970391+0.006046j
[2025-08-07 14:19:31] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -54.921723+0.003334j
[2025-08-07 14:19:35] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -54.946728-0.002635j
[2025-08-07 14:19:40] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -54.913806-0.007646j
[2025-08-07 14:19:44] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -54.942483-0.006056j
[2025-08-07 14:19:48] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -54.932094+0.002309j
[2025-08-07 14:19:52] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -54.988122-0.002719j
[2025-08-07 14:19:56] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -55.077171-0.001425j
[2025-08-07 14:20:00] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -55.015135-0.000340j
[2025-08-07 14:20:04] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -55.120119-0.001776j
[2025-08-07 14:20:09] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -55.055869+0.004517j
[2025-08-07 14:20:13] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -55.053154-0.003559j
[2025-08-07 14:20:17] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -54.993022+0.005017j
[2025-08-07 14:20:21] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -54.913353+0.000536j
[2025-08-07 14:20:25] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -55.009619-0.003868j
[2025-08-07 14:20:29] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -55.036671+0.000623j
[2025-08-07 14:20:34] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -55.106035+0.003680j
[2025-08-07 14:20:38] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -55.048393-0.001481j
[2025-08-07 14:20:42] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -55.045383+0.002444j
[2025-08-07 14:20:46] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -55.039642-0.002359j
[2025-08-07 14:20:50] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -55.114991+0.001876j
[2025-08-07 14:20:54] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -55.003467+0.001020j
[2025-08-07 14:20:58] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -55.131898-0.011784j
[2025-08-07 14:21:03] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -55.116721+0.003326j
[2025-08-07 14:21:07] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -55.050989+0.000267j
[2025-08-07 14:21:11] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -55.125550-0.002842j
[2025-08-07 14:21:15] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -55.096355-0.001128j
[2025-08-07 14:21:19] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -54.967644+0.001277j
[2025-08-07 14:21:23] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -54.937060-0.003069j
[2025-08-07 14:21:23] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-07 14:21:28] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -54.968736-0.001208j
[2025-08-07 14:21:32] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -54.831374+0.000640j
[2025-08-07 14:21:36] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -54.965502-0.005382j
[2025-08-07 14:21:40] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -55.013777+0.005662j
[2025-08-07 14:21:44] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -55.091389+0.001262j
[2025-08-07 14:21:48] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -54.868783+0.004124j
[2025-08-07 14:21:52] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -54.964793-0.004185j
[2025-08-07 14:21:57] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -54.970899+0.002552j
[2025-08-07 14:22:01] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -54.930179+0.004482j
[2025-08-07 14:22:05] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -55.060949-0.002617j
[2025-08-07 14:22:09] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -55.004471+0.000147j
[2025-08-07 14:22:13] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -55.028528-0.001131j
[2025-08-07 14:22:17] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -55.011208+0.002867j
[2025-08-07 14:22:21] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -54.944158+0.001542j
[2025-08-07 14:22:26] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -54.938056+0.001098j
[2025-08-07 14:22:30] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -54.944279+0.002975j
[2025-08-07 14:22:34] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -55.036151-0.000704j
[2025-08-07 14:22:38] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -54.983633+0.000507j
[2025-08-07 14:22:42] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -54.980758+0.002061j
[2025-08-07 14:22:46] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -55.007956-0.000666j
[2025-08-07 14:22:50] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -55.046115+0.000369j
[2025-08-07 14:22:55] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -55.009782+0.003470j
[2025-08-07 14:22:59] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -55.000411+0.005016j
[2025-08-07 14:23:03] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -55.001069+0.005826j
[2025-08-07 14:23:07] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -54.947149+0.002495j
[2025-08-07 14:23:11] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -54.997374-0.004979j
[2025-08-07 14:23:15] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -54.951673-0.002035j
[2025-08-07 14:23:19] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -54.847759+0.000611j
[2025-08-07 14:23:24] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -54.855617+0.000697j
[2025-08-07 14:23:28] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -54.882046-0.001752j
[2025-08-07 14:23:32] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -55.022389-0.003772j
[2025-08-07 14:23:36] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -55.046642-0.000393j
[2025-08-07 14:23:40] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -54.987716-0.003647j
[2025-08-07 14:23:44] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -54.922971+0.001418j
[2025-08-07 14:23:49] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -54.984237-0.005466j
[2025-08-07 14:23:53] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -55.110078-0.001289j
[2025-08-07 14:23:57] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -55.132396+0.001241j
[2025-08-07 14:24:01] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -55.048651-0.004524j
[2025-08-07 14:24:05] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -55.043548-0.003741j
[2025-08-07 14:24:09] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -55.094875+0.004073j
[2025-08-07 14:24:14] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -55.020825-0.002521j
[2025-08-07 14:24:18] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -55.036800-0.004283j
[2025-08-07 14:24:22] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -54.997298+0.002149j
[2025-08-07 14:24:26] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -55.086562-0.002415j
[2025-08-07 14:24:30] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -55.044124+0.007597j
[2025-08-07 14:24:34] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -55.023921-0.000154j
[2025-08-07 14:24:39] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -55.164940-0.000471j
[2025-08-07 14:24:43] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -54.880813+0.000901j
[2025-08-07 14:24:47] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -54.935659-0.001265j
[2025-08-07 14:24:51] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -55.020215+0.002909j
[2025-08-07 14:24:55] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -54.944156-0.001700j
[2025-08-07 14:24:59] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -54.993169-0.003223j
[2025-08-07 14:25:04] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -55.068435-0.008019j
[2025-08-07 14:25:08] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -55.056441-0.004380j
[2025-08-07 14:25:12] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -55.000016+0.004971j
[2025-08-07 14:25:16] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -55.064683+0.001891j
[2025-08-07 14:25:20] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -55.141683-0.007773j
[2025-08-07 14:25:24] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -55.076513-0.002181j
[2025-08-07 14:25:28] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -55.052717-0.002212j
[2025-08-07 14:25:33] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -55.147196-0.007267j
[2025-08-07 14:25:37] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -55.161593+0.001751j
[2025-08-07 14:25:41] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -55.154588+0.001951j
[2025-08-07 14:25:45] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -55.148087+0.005892j
[2025-08-07 14:25:49] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -55.188458+0.001120j
[2025-08-07 14:25:53] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -55.063610+0.002743j
[2025-08-07 14:25:58] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -55.110371-0.001766j
[2025-08-07 14:26:02] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -54.950383+0.003526j
[2025-08-07 14:26:06] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -55.002111-0.005478j
[2025-08-07 14:26:10] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -54.985316-0.000636j
[2025-08-07 14:26:14] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -54.980515-0.000354j
[2025-08-07 14:26:18] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -55.106947-0.006520j
[2025-08-07 14:26:22] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -54.906733-0.006278j
[2025-08-07 14:26:27] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -54.891252-0.004368j
[2025-08-07 14:26:31] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -54.983216+0.001122j
[2025-08-07 14:26:35] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -54.969743+0.006524j
[2025-08-07 14:26:39] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -54.886968-0.004537j
[2025-08-07 14:26:43] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -54.772739-0.000939j
[2025-08-07 14:26:47] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -55.044109-0.000681j
[2025-08-07 14:26:51] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -54.856945-0.000920j
[2025-08-07 14:26:56] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -55.059126+0.003630j
[2025-08-07 14:27:00] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -55.041881-0.000582j
[2025-08-07 14:27:04] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -55.058776-0.002001j
[2025-08-07 14:27:08] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -54.786237+0.008984j
[2025-08-07 14:27:12] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -54.989795+0.002488j
[2025-08-07 14:27:16] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -54.979730-0.001380j
[2025-08-07 14:27:20] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -54.961349-0.006610j
[2025-08-07 14:27:25] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -54.993791-0.005349j
[2025-08-07 14:27:29] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -55.135209-0.000909j
[2025-08-07 14:27:33] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -55.032366-0.000115j
[2025-08-07 14:27:37] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -55.011060+0.000519j
[2025-08-07 14:27:41] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -55.099281-0.003567j
[2025-08-07 14:27:45] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -55.082236-0.001610j
[2025-08-07 14:27:50] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -54.987914-0.002554j
[2025-08-07 14:27:54] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -55.014313-0.002156j
[2025-08-07 14:27:58] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -55.047174+0.007426j
[2025-08-07 14:28:02] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -55.030643+0.000604j
[2025-08-07 14:28:06] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -54.954703-0.007009j
[2025-08-07 14:28:10] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -54.947006-0.001269j
[2025-08-07 14:28:14] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -54.968272-0.004310j
[2025-08-07 14:28:19] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -54.965695-0.002782j
[2025-08-07 14:28:19] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-07 14:28:23] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -54.971750-0.002367j
[2025-08-07 14:28:27] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -54.998493+0.003824j
[2025-08-07 14:28:31] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -55.020112+0.000677j
[2025-08-07 14:28:35] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -55.022638+0.003543j
[2025-08-07 14:28:39] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -54.987471-0.002943j
[2025-08-07 14:28:44] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -54.954580-0.002648j
[2025-08-07 14:28:48] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -55.005929-0.000112j
[2025-08-07 14:28:52] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -55.146156+0.002555j
[2025-08-07 14:28:56] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -54.977767+0.007016j
[2025-08-07 14:29:00] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -55.054252-0.001397j
[2025-08-07 14:29:04] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -55.001079-0.004821j
[2025-08-07 14:29:09] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -55.136343-0.003895j
[2025-08-07 14:29:13] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -55.149538-0.001655j
[2025-08-07 14:29:17] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -55.023431-0.001388j
[2025-08-07 14:29:21] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -54.973244-0.001103j
[2025-08-07 14:29:25] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -54.980400-0.000545j
[2025-08-07 14:29:29] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -55.002265+0.000074j
[2025-08-07 14:29:34] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -55.039547+0.003251j
[2025-08-07 14:29:38] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -54.942041+0.002319j
[2025-08-07 14:29:42] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -55.040512+0.000121j
[2025-08-07 14:29:46] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -54.998767-0.002125j
[2025-08-07 14:29:50] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -55.034485+0.001823j
[2025-08-07 14:29:54] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -54.945862-0.002691j
[2025-08-07 14:29:58] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -55.060968+0.001745j
[2025-08-07 14:30:03] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -55.065734-0.002960j
[2025-08-07 14:30:07] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -55.106130-0.007212j
[2025-08-07 14:30:11] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -54.967304-0.000776j
[2025-08-07 14:30:15] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -54.855913+0.003942j
[2025-08-07 14:30:19] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -54.908527+0.002354j
[2025-08-07 14:30:23] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -54.894083-0.002464j
[2025-08-07 14:30:28] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -54.939678+0.000405j
[2025-08-07 14:30:32] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -55.023368+0.003957j
[2025-08-07 14:30:36] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -55.099889+0.005896j
[2025-08-07 14:30:40] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -54.980269-0.000509j
[2025-08-07 14:30:44] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -54.937364+0.002398j
[2025-08-07 14:30:48] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -54.859137-0.006590j
[2025-08-07 14:30:52] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -54.772126-0.004823j
[2025-08-07 14:30:57] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -54.949133-0.001399j
[2025-08-07 14:31:01] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -54.926797-0.006135j
[2025-08-07 14:31:05] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -54.943563-0.006436j
[2025-08-07 14:31:09] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -54.979983+0.001109j
[2025-08-07 14:31:13] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -54.940090+0.003920j
[2025-08-07 14:31:17] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -54.866629+0.001062j
[2025-08-07 14:31:21] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -54.988589+0.002282j
[2025-08-07 14:31:26] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -55.004517+0.000364j
[2025-08-07 14:31:30] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -55.030716+0.001466j
[2025-08-07 14:31:34] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -55.048006+0.003004j
[2025-08-07 14:31:38] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -55.040289-0.001055j
[2025-08-07 14:31:42] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -54.965632-0.004567j
[2025-08-07 14:31:46] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -55.075875-0.001431j
[2025-08-07 14:31:50] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -55.042843+0.000939j
[2025-08-07 14:31:55] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -54.956004-0.001040j
[2025-08-07 14:31:59] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -54.927404-0.002724j
[2025-08-07 14:32:03] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -55.049807-0.000827j
[2025-08-07 14:32:07] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -55.061474-0.000360j
[2025-08-07 14:32:11] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -55.093290-0.003117j
[2025-08-07 14:32:15] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -55.043990+0.002939j
[2025-08-07 14:32:20] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -55.002643-0.006703j
[2025-08-07 14:32:24] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -55.097460+0.000325j
[2025-08-07 14:32:28] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -55.210273+0.001954j
[2025-08-07 14:32:32] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -55.013042-0.000721j
[2025-08-07 14:32:36] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -54.997376+0.001302j
[2025-08-07 14:32:40] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -55.019266+0.006093j
[2025-08-07 14:32:44] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -55.002424+0.006741j
[2025-08-07 14:32:49] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -55.058610+0.003956j
[2025-08-07 14:32:53] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -54.993990+0.004392j
[2025-08-07 14:32:57] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -54.962662+0.004967j
[2025-08-07 14:33:01] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -55.046892-0.000631j
[2025-08-07 14:33:05] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -55.143018-0.004839j
[2025-08-07 14:33:09] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -55.136936+0.002432j
[2025-08-07 14:33:14] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -55.112787+0.002167j
[2025-08-07 14:33:18] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -55.100260-0.001220j
[2025-08-07 14:33:22] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -55.101352-0.004005j
[2025-08-07 14:33:26] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -55.076773-0.000847j
[2025-08-07 14:33:30] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -54.969604-0.001656j
[2025-08-07 14:33:34] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -55.018266+0.001045j
[2025-08-07 14:33:39] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -54.990282-0.003611j
[2025-08-07 14:33:43] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -54.991689-0.007366j
[2025-08-07 14:33:47] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -55.124668-0.003511j
[2025-08-07 14:33:51] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -55.038617+0.003687j
[2025-08-07 14:33:55] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -54.984807-0.002106j
[2025-08-07 14:33:59] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -55.123060-0.003926j
[2025-08-07 14:34:04] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -55.108069+0.009303j
[2025-08-07 14:34:08] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -55.066239+0.001013j
[2025-08-07 14:34:12] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -54.990396+0.007373j
[2025-08-07 14:34:16] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -55.047548+0.000488j
[2025-08-07 14:34:20] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -55.054410-0.003911j
[2025-08-07 14:34:24] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -55.097886+0.000504j
[2025-08-07 14:34:28] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -55.163262-0.004471j
[2025-08-07 14:34:33] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -55.164123+0.001480j
[2025-08-07 14:34:37] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -55.226167-0.004407j
[2025-08-07 14:34:41] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -55.243206-0.002553j
[2025-08-07 14:34:45] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -55.324656-0.003518j
[2025-08-07 14:34:49] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -55.134864+0.000258j
[2025-08-07 14:34:53] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -55.093325-0.002243j
[2025-08-07 14:34:58] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -54.951146-0.003958j
[2025-08-07 14:35:02] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -54.988670+0.002767j
[2025-08-07 14:35:06] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -55.038361+0.001390j
[2025-08-07 14:35:10] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -55.091155+0.001812j
[2025-08-07 14:35:14] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -55.006631+0.001923j
[2025-08-07 14:35:14] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-07 14:35:18] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -55.079135-0.002204j
[2025-08-07 14:35:22] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -55.091248+0.004958j
[2025-08-07 14:35:27] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -55.072157+0.001253j
[2025-08-07 14:35:31] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -55.018253+0.000621j
[2025-08-07 14:35:35] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -54.948199+0.003266j
[2025-08-07 14:35:39] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -55.098213+0.003542j
[2025-08-07 14:35:43] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -55.024170+0.001065j
[2025-08-07 14:35:47] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -54.959977+0.000465j
[2025-08-07 14:35:51] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -55.062949+0.005257j
[2025-08-07 14:35:56] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -55.102254+0.000590j
[2025-08-07 14:36:00] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -55.027253+0.001062j
[2025-08-07 14:36:04] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -55.114525-0.003962j
[2025-08-07 14:36:08] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -55.115364-0.003423j
[2025-08-07 14:36:12] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -55.071963-0.002130j
[2025-08-07 14:36:16] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -55.014327-0.000404j
[2025-08-07 14:36:20] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -55.104822-0.003854j
[2025-08-07 14:36:25] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -55.076305-0.003084j
[2025-08-07 14:36:29] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -55.009100+0.001843j
[2025-08-07 14:36:33] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -54.955845+0.000750j
[2025-08-07 14:36:37] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -54.951749-0.001013j
[2025-08-07 14:36:41] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -55.020679-0.000333j
[2025-08-07 14:36:45] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -54.964701+0.001334j
[2025-08-07 14:36:49] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -55.006168+0.000752j
[2025-08-07 14:36:54] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -54.997077+0.001180j
[2025-08-07 14:36:58] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -54.965907-0.005208j
[2025-08-07 14:37:02] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -55.004796+0.000999j
[2025-08-07 14:37:06] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -54.978137+0.001213j
[2025-08-07 14:37:10] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -55.092368-0.001109j
[2025-08-07 14:37:14] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -55.123004+0.007234j
[2025-08-07 14:37:18] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -55.026514-0.000834j
[2025-08-07 14:37:23] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -55.032887-0.003148j
[2025-08-07 14:37:27] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -55.000043+0.002283j
[2025-08-07 14:37:31] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -55.006643-0.001250j
[2025-08-07 14:37:35] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -55.114218+0.002584j
[2025-08-07 14:37:39] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -55.017142-0.002917j
[2025-08-07 14:37:43] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -55.090735-0.004635j
[2025-08-07 14:37:48] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -55.085773+0.000776j
[2025-08-07 14:37:52] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -55.071890-0.003491j
[2025-08-07 14:37:56] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -55.167780-0.000628j
[2025-08-07 14:38:00] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -55.088320+0.005143j
[2025-08-07 14:38:04] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -55.158832+0.000776j
[2025-08-07 14:38:08] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -54.964390+0.001539j
[2025-08-07 14:38:12] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -54.944519-0.002006j
[2025-08-07 14:38:17] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -54.992148+0.001541j
[2025-08-07 14:38:21] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -55.063475+0.002468j
[2025-08-07 14:38:25] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -55.090674+0.003432j
[2025-08-07 14:38:29] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -55.004166-0.001737j
[2025-08-07 14:38:33] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -54.985893+0.000413j
[2025-08-07 14:38:37] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -55.041911+0.000350j
[2025-08-07 14:38:41] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -55.115755-0.001797j
[2025-08-07 14:38:46] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -55.055950-0.001885j
[2025-08-07 14:38:50] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -55.062549-0.000245j
[2025-08-07 14:38:54] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -55.098232-0.003607j
[2025-08-07 14:38:58] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -55.121401+0.002082j
[2025-08-07 14:39:02] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -55.128660-0.000049j
[2025-08-07 14:39:06] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -55.111184+0.000121j
[2025-08-07 14:39:11] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -55.239227+0.000013j
[2025-08-07 14:39:15] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -55.099310-0.004181j
[2025-08-07 14:39:19] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -54.955815-0.003735j
[2025-08-07 14:39:23] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -55.108106-0.003130j
[2025-08-07 14:39:27] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -55.055202+0.001211j
[2025-08-07 14:39:31] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -55.131336+0.007408j
[2025-08-07 14:39:35] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -55.087150-0.000155j
[2025-08-07 14:39:40] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -54.963819-0.001482j
[2025-08-07 14:39:44] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -54.970314+0.005111j
[2025-08-07 14:39:48] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -55.009977+0.003261j
[2025-08-07 14:39:52] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -55.062068-0.005619j
[2025-08-07 14:39:56] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -54.956659-0.005544j
[2025-08-07 14:40:00] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -54.971304+0.002785j
[2025-08-07 14:40:04] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -54.968646-0.000517j
[2025-08-07 14:40:09] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -55.025363-0.008223j
[2025-08-07 14:40:13] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -55.063863-0.004403j
[2025-08-07 14:40:17] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -55.041217-0.000772j
[2025-08-07 14:40:21] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -54.999611-0.000140j
[2025-08-07 14:40:25] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -54.987926-0.000857j
[2025-08-07 14:40:29] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -55.162513-0.003721j
[2025-08-07 14:40:34] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -55.110883-0.004633j
[2025-08-07 14:40:38] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -54.916376+0.004076j
[2025-08-07 14:40:42] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -54.920593+0.001306j
[2025-08-07 14:40:46] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -55.043089+0.006604j
[2025-08-07 14:40:50] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -55.012700+0.004209j
[2025-08-07 14:40:54] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -55.000994+0.001660j
[2025-08-07 14:40:58] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -55.012762-0.001811j
[2025-08-07 14:41:03] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -55.011480+0.000249j
[2025-08-07 14:41:07] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -54.986877-0.000972j
[2025-08-07 14:41:11] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -54.900829-0.001318j
[2025-08-07 14:41:15] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -54.889668-0.006422j
[2025-08-07 14:41:19] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -55.004111+0.000478j
[2025-08-07 14:41:23] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -54.978976-0.001001j
[2025-08-07 14:41:28] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -55.054881-0.000170j
[2025-08-07 14:41:32] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -55.089032+0.005946j
[2025-08-07 14:41:36] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -55.185055-0.004940j
[2025-08-07 14:41:40] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -55.084981+0.000261j
[2025-08-07 14:41:44] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -55.112565-0.001461j
[2025-08-07 14:41:48] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -55.085951-0.002967j
[2025-08-07 14:41:52] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -54.991119+0.007362j
[2025-08-07 14:41:57] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -54.968395+0.003522j
[2025-08-07 14:42:01] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -54.986797+0.002683j
[2025-08-07 14:42:05] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -54.991684+0.001151j
[2025-08-07 14:42:09] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -55.041830-0.003543j
[2025-08-07 14:42:09] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-07 14:42:13] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -55.039925-0.002854j
[2025-08-07 14:42:17] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -55.109392+0.004286j
[2025-08-07 14:42:21] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -55.040397+0.000819j
[2025-08-07 14:42:26] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -55.016834+0.000285j
[2025-08-07 14:42:30] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -55.021475+0.000869j
[2025-08-07 14:42:34] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -54.987875+0.002660j
[2025-08-07 14:42:38] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -55.050865+0.004413j
[2025-08-07 14:42:42] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -55.032757-0.003987j
[2025-08-07 14:42:46] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -55.094544+0.003368j
[2025-08-07 14:42:50] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -55.064727-0.004590j
[2025-08-07 14:42:55] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -55.215438-0.001623j
[2025-08-07 14:42:59] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -55.232832-0.001271j
[2025-08-07 14:43:03] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -55.033233-0.002034j
[2025-08-07 14:43:07] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -55.084256+0.001911j
[2025-08-07 14:43:11] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -55.182013-0.002987j
[2025-08-07 14:43:15] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -55.178146-0.001865j
[2025-08-07 14:43:19] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -55.162415+0.000968j
[2025-08-07 14:43:24] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -55.118839+0.000446j
[2025-08-07 14:43:28] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -55.163181-0.001618j
[2025-08-07 14:43:32] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -55.242512-0.001864j
[2025-08-07 14:43:36] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -55.121432-0.000928j
[2025-08-07 14:43:40] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -55.098726-0.003705j
[2025-08-07 14:43:44] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -54.952437+0.002126j
[2025-08-07 14:43:49] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -55.073292-0.000344j
[2025-08-07 14:43:53] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -55.110355+0.000318j
[2025-08-07 14:43:57] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -55.070787+0.002074j
[2025-08-07 14:44:01] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -54.929563+0.003601j
[2025-08-07 14:44:05] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -54.869133+0.000728j
[2025-08-07 14:44:09] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -54.998940+0.002442j
[2025-08-07 14:44:14] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -54.984396+0.000486j
[2025-08-07 14:44:18] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -55.074806-0.003912j
[2025-08-07 14:44:22] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -55.022134-0.004093j
[2025-08-07 14:44:26] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -55.008221-0.002450j
[2025-08-07 14:44:30] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -54.964992-0.008699j
[2025-08-07 14:44:34] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -54.986220-0.000943j
[2025-08-07 14:44:39] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -55.047494-0.001549j
[2025-08-07 14:44:43] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -55.049553-0.001357j
[2025-08-07 14:44:47] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -55.016147+0.003174j
[2025-08-07 14:44:51] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -55.011860+0.002557j
[2025-08-07 14:44:55] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -55.048794-0.000430j
[2025-08-07 14:44:59] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -55.036738+0.000488j
[2025-08-07 14:45:04] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -55.065706-0.003463j
[2025-08-07 14:45:08] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -54.970467-0.004468j
[2025-08-07 14:45:12] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -54.918607-0.004343j
[2025-08-07 14:45:16] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -54.986406-0.000198j
[2025-08-07 14:45:20] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -54.966272+0.001546j
[2025-08-07 14:45:24] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -54.978314-0.000228j
[2025-08-07 14:45:28] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -54.937355+0.003387j
[2025-08-07 14:45:33] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -55.101536-0.004449j
[2025-08-07 14:45:37] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -55.010615+0.001929j
[2025-08-07 14:45:41] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -55.152631-0.002998j
[2025-08-07 14:45:45] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -55.042708+0.001472j
[2025-08-07 14:45:49] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -55.023534+0.000738j
[2025-08-07 14:45:53] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -55.099852-0.001131j
[2025-08-07 14:45:58] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -54.979553+0.000056j
[2025-08-07 14:46:02] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -55.046923+0.003947j
[2025-08-07 14:46:06] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -55.028440-0.009663j
[2025-08-07 14:46:10] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -54.991313-0.000166j
[2025-08-07 14:46:14] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -54.875792-0.005212j
[2025-08-07 14:46:18] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -54.847739+0.000493j
[2025-08-07 14:46:22] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -54.899833+0.001539j
[2025-08-07 14:46:27] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -54.810247-0.000078j
[2025-08-07 14:46:31] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -55.014563-0.003926j
[2025-08-07 14:46:35] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -55.061587-0.002144j
[2025-08-07 14:46:39] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -54.999486+0.001392j
[2025-08-07 14:46:43] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -55.147418+0.000789j
[2025-08-07 14:46:47] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -55.055693+0.001676j
[2025-08-07 14:46:51] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -55.010727+0.001261j
[2025-08-07 14:46:56] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -54.987748+0.002881j
[2025-08-07 14:47:00] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -54.951357-0.000052j
[2025-08-07 14:47:04] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -55.011897+0.000606j
[2025-08-07 14:47:08] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -55.115300+0.000770j
[2025-08-07 14:47:12] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -54.905294-0.001557j
[2025-08-07 14:47:16] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -55.007617+0.002655j
[2025-08-07 14:47:20] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -55.069964-0.002609j
[2025-08-07 14:47:25] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -55.041395+0.001983j
[2025-08-07 14:47:29] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -55.000705+0.002384j
[2025-08-07 14:47:33] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -54.844806-0.001245j
[2025-08-07 14:47:37] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -55.147976+0.000870j
[2025-08-07 14:47:41] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -55.134020-0.007219j
[2025-08-07 14:47:45] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -54.988342+0.001250j
[2025-08-07 14:47:49] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -55.059713+0.002691j
[2025-08-07 14:47:54] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -54.973205-0.003484j
[2025-08-07 14:47:58] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -55.074001-0.002710j
[2025-08-07 14:48:02] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -55.114110-0.001961j
[2025-08-07 14:48:06] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -54.989653-0.000062j
[2025-08-07 14:48:10] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -54.920658+0.002656j
[2025-08-07 14:48:14] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -55.018384-0.001681j
[2025-08-07 14:48:19] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -54.946656-0.003007j
[2025-08-07 14:48:23] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -54.882602+0.001348j
[2025-08-07 14:48:27] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -55.053787+0.007223j
[2025-08-07 14:48:31] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -55.045398+0.002660j
[2025-08-07 14:48:35] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -55.010547+0.003851j
[2025-08-07 14:48:39] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -54.958299+0.006549j
[2025-08-07 14:48:43] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -54.961333+0.003960j
[2025-08-07 14:48:48] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -55.032605+0.000404j
[2025-08-07 14:48:52] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -55.064848+0.001606j
[2025-08-07 14:48:56] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -55.094001-0.002788j
[2025-08-07 14:49:00] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -55.193684+0.000044j
[2025-08-07 14:49:04] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -55.155846+0.002631j
[2025-08-07 14:49:04] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-07 14:49:08] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -55.238963-0.004100j
[2025-08-07 14:49:12] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -55.167839+0.002095j
[2025-08-07 14:49:17] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -55.152512-0.003396j
[2025-08-07 14:49:21] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -54.979141+0.004499j
[2025-08-07 14:49:25] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -55.012017-0.003537j
[2025-08-07 14:49:29] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -55.038202-0.002579j
[2025-08-07 14:49:33] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -55.118454-0.000506j
[2025-08-07 14:50:41] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -55.224016-0.006291j
[2025-08-07 14:50:45] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -55.214499+0.002238j
[2025-08-07 14:50:49] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -55.151064+0.002623j
[2025-08-07 14:50:54] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -55.172751+0.002376j
[2025-08-07 14:50:58] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -55.121340+0.004986j
[2025-08-07 14:51:02] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -55.090670-0.001936j
[2025-08-07 14:51:06] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -55.037889-0.002348j
[2025-08-07 14:51:10] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -54.982316-0.002106j
[2025-08-07 14:51:14] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -54.960317-0.006450j
[2025-08-07 14:51:18] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -54.915536-0.002773j
[2025-08-07 14:51:23] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -55.029253-0.001868j
[2025-08-07 14:51:27] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -55.039795-0.002819j
[2025-08-07 14:51:31] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -54.917730-0.001865j
[2025-08-07 14:51:35] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -54.984323+0.004468j
[2025-08-07 14:51:39] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -54.963079-0.004050j
[2025-08-07 14:51:43] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -55.149681+0.001010j
[2025-08-07 14:51:48] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -55.076905+0.000713j
[2025-08-07 14:51:52] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -55.043462-0.000410j
[2025-08-07 14:51:56] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -54.988304-0.002755j
[2025-08-07 14:52:00] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -55.074220+0.005126j
[2025-08-07 14:52:04] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -55.011508-0.001067j
[2025-08-07 14:52:08] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -55.068142-0.003294j
[2025-08-07 14:52:12] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -55.041847-0.000738j
[2025-08-07 14:52:17] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -54.994271-0.003338j
[2025-08-07 14:52:21] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -55.039282+0.003133j
[2025-08-07 14:52:25] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -55.013173-0.001152j
[2025-08-07 14:52:29] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -55.022721+0.004873j
[2025-08-07 14:52:33] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -55.023100-0.001875j
[2025-08-07 14:52:37] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -55.011853+0.001306j
[2025-08-07 14:52:41] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -55.046205-0.001775j
[2025-08-07 14:52:46] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -55.034496-0.000710j
[2025-08-07 14:52:50] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -55.004124-0.004005j
[2025-08-07 14:52:54] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -55.066850-0.000904j
[2025-08-07 14:52:58] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -55.051641+0.004718j
[2025-08-07 14:53:02] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -54.987261+0.002466j
[2025-08-07 14:53:06] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -55.069114-0.003735j
[2025-08-07 14:53:11] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -54.884598+0.001903j
[2025-08-07 14:53:15] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -55.027107+0.000585j
[2025-08-07 14:53:19] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -55.131121-0.001683j
[2025-08-07 14:53:23] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -55.176638-0.003057j
[2025-08-07 14:53:27] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -55.058926-0.003802j
[2025-08-07 14:53:31] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -54.915518+0.003405j
[2025-08-07 14:53:35] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -54.982566-0.001878j
[2025-08-07 14:53:35] ✅ Training completed | Restarts: 2
[2025-08-07 14:53:35] ============================================================
[2025-08-07 14:53:35] Training completed | Runtime: 4461.3s
[2025-08-07 14:53:48] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-07 14:53:48] ============================================================
