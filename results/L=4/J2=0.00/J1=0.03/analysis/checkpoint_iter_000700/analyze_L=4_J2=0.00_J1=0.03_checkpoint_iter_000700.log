[2025-08-07 18:25:38] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.03/training/checkpoints/checkpoint_iter_000700.pkl
[2025-08-07 18:25:45] ✓ 从checkpoint加载参数: 700
[2025-08-07 18:25:45]   - 能量: -54.280729+0.003277j ± 0.085637
[2025-08-07 18:25:45] ================================================================================
[2025-08-07 18:25:45] 加载量子态: L=4, J2=0.00, J1=0.03, checkpoint=checkpoint_iter_000700
[2025-08-07 18:25:45] 设置样本数为: 1048576
[2025-08-07 18:25:45] 开始生成共享样本集...
[2025-08-07 18:28:17] 样本生成完成,耗时: 151.822 秒
[2025-08-07 18:28:17] ================================================================================
[2025-08-07 18:28:17] 开始计算自旋结构因子...
[2025-08-07 18:28:17] 初始化操作符缓存...
[2025-08-07 18:28:17] 预构建所有自旋相关操作符...
[2025-08-07 18:28:17] 开始计算自旋相关函数...
[2025-08-07 18:28:27] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.107s
[2025-08-07 18:28:40] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.180s
[2025-08-07 18:28:48] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.222s
[2025-08-07 18:28:57] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.183s
[2025-08-07 18:29:05] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.222s
[2025-08-07 18:29:13] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.223s
[2025-08-07 18:29:21] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.186s
[2025-08-07 18:29:29] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.224s
[2025-08-07 18:29:38] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.183s
[2025-08-07 18:29:46] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.184s
[2025-08-07 18:29:54] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.224s
[2025-08-07 18:30:02] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.185s
[2025-08-07 18:30:10] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.224s
[2025-08-07 18:30:19] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.184s
[2025-08-07 18:30:27] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.224s
[2025-08-07 18:30:35] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.197s
[2025-08-07 18:30:43] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.223s
[2025-08-07 18:30:51] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.225s
[2025-08-07 18:31:00] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.195s
[2025-08-07 18:31:08] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.226s
[2025-08-07 18:31:16] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.184s
[2025-08-07 18:31:24] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.186s
[2025-08-07 18:31:33] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.224s
[2025-08-07 18:31:41] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.186s
[2025-08-07 18:31:49] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.225s
[2025-08-07 18:31:57] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.224s
[2025-08-07 18:32:05] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.189s
[2025-08-07 18:32:14] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.225s
[2025-08-07 18:32:22] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.185s
[2025-08-07 18:32:30] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.186s
[2025-08-07 18:32:38] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.224s
[2025-08-07 18:32:46] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.223s
[2025-08-07 18:32:55] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.184s
[2025-08-07 18:33:03] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.185s
[2025-08-07 18:33:11] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.224s
[2025-08-07 18:33:19] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.186s
[2025-08-07 18:33:27] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.223s
[2025-08-07 18:33:36] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.224s
[2025-08-07 18:33:44] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.186s
[2025-08-07 18:33:52] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.225s
[2025-08-07 18:34:00] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.184s
[2025-08-07 18:34:08] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.194s
[2025-08-07 18:34:17] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.223s
[2025-08-07 18:34:25] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.186s
[2025-08-07 18:34:33] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.224s
[2025-08-07 18:34:41] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.225s
[2025-08-07 18:34:49] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.186s
[2025-08-07 18:34:58] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.225s
[2025-08-07 18:35:06] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.185s
[2025-08-07 18:35:14] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.187s
[2025-08-07 18:35:22] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.226s
[2025-08-07 18:35:30] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.185s
[2025-08-07 18:35:39] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.187s
[2025-08-07 18:35:47] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.186s
[2025-08-07 18:35:55] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.225s
[2025-08-07 18:36:03] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.185s
[2025-08-07 18:36:11] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.224s
[2025-08-07 18:36:20] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.226s
[2025-08-07 18:36:28] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.185s
[2025-08-07 18:36:36] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.225s
[2025-08-07 18:36:44] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.185s
[2025-08-07 18:36:52] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.186s
[2025-08-07 18:37:01] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.233s
[2025-08-07 18:37:09] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.237s
[2025-08-07 18:37:09] 自旋相关函数计算完成,总耗时 532.11 秒
[2025-08-07 18:37:09] 计算傅里叶变换...
[2025-08-07 18:37:10] 自旋结构因子计算完成
[2025-08-07 18:37:10] 自旋相关函数平均误差: 0.000668
