[2025-08-07 13:46:11] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-08-07 13:46:11]   - 迭代次数: final
[2025-08-07 13:46:11]   - 能量: -54.663975-0.000269j ± 0.043090
[2025-08-07 13:46:11]   - 时间戳: 2025-07-30T17:44:56.179657
[2025-08-07 13:46:19] ✓ 变分状态参数已从checkpoint恢复
[2025-08-07 13:46:19] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-07 13:46:19] ==================================================
[2025-08-07 13:46:19] GCNN for Shastry-Sutherland Model
[2025-08-07 13:46:19] ==================================================
[2025-08-07 13:46:19] System parameters:
[2025-08-07 13:46:19]   - System size: L=4, N=64
[2025-08-07 13:46:19]   - System parameters: J1=0.03, J2=0.0, Q=1.0
[2025-08-07 13:46:19] --------------------------------------------------
[2025-08-07 13:46:19] Model parameters:
[2025-08-07 13:46:19]   - Number of layers = 4
[2025-08-07 13:46:19]   - Number of features = 4
[2025-08-07 13:46:19]   - Total parameters = 12572
[2025-08-07 13:46:19] --------------------------------------------------
[2025-08-07 13:46:19] Training parameters:
[2025-08-07 13:46:19]   - Learning rate: 0.015
[2025-08-07 13:46:19]   - Total iterations: 1050
[2025-08-07 13:46:19]   - Annealing cycles: 3
[2025-08-07 13:46:19]   - Initial period: 150
[2025-08-07 13:46:19]   - Period multiplier: 2.0
[2025-08-07 13:46:19]   - Temperature range: 0.0-1.0
[2025-08-07 13:46:19]   - Samples: 4096
[2025-08-07 13:46:19]   - Discarded samples: 0
[2025-08-07 13:46:19]   - Chunk size: 2048
[2025-08-07 13:46:19]   - Diagonal shift: 0.2
[2025-08-07 13:46:19]   - Gradient clipping: 1.0
[2025-08-07 13:46:19]   - Checkpoint enabled: interval=100
[2025-08-07 13:46:19]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.03/training/checkpoints
[2025-08-07 13:46:19] --------------------------------------------------
[2025-08-07 13:46:19] Device status:
[2025-08-07 13:46:19]   - Devices model: A100
[2025-08-07 13:46:19]   - Number of devices: 1
[2025-08-07 13:46:19]   - Sharding: True
[2025-08-07 13:46:19] ============================================================
[2025-08-07 13:46:50] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -54.245400-0.008508j
[2025-08-07 13:47:09] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -54.193298-0.001804j
[2025-08-07 13:47:14] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -54.284743-0.007336j
[2025-08-07 13:47:18] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -54.400169-0.004534j
[2025-08-07 13:47:22] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -54.282342-0.006938j
[2025-08-07 13:47:26] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -54.378378-0.000540j
[2025-08-07 13:47:30] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -54.237005-0.001337j
[2025-08-07 13:47:34] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -54.199549-0.003606j
[2025-08-07 13:47:38] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -54.287700+0.005568j
[2025-08-07 13:47:43] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -54.255543-0.003070j
[2025-08-07 13:47:47] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -54.257540-0.002538j
[2025-08-07 13:47:51] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -54.222856-0.005436j
[2025-08-07 13:47:55] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -54.379055-0.004840j
[2025-08-07 13:47:59] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -54.346710-0.002837j
[2025-08-07 13:48:03] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -54.362004-0.005998j
[2025-08-07 13:48:07] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -54.343717-0.004197j
[2025-08-07 13:48:12] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -54.331689-0.000972j
[2025-08-07 13:48:16] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -54.403930+0.002505j
[2025-08-07 13:48:20] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -54.388793-0.001602j
[2025-08-07 13:48:24] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -54.290593+0.001029j
[2025-08-07 13:48:28] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -54.252924+0.004866j
[2025-08-07 13:48:32] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -54.244593-0.000946j
[2025-08-07 13:48:36] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -54.263964-0.000020j
[2025-08-07 13:48:41] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -54.225156-0.005054j
[2025-08-07 13:48:45] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -54.160301-0.002802j
[2025-08-07 13:48:49] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -54.198250+0.000771j
[2025-08-07 13:48:53] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -54.141442+0.003760j
[2025-08-07 13:48:57] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -54.193755-0.000567j
[2025-08-07 13:49:01] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -54.231385-0.000544j
[2025-08-07 13:49:06] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -54.171574+0.003628j
[2025-08-07 13:49:10] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -54.198739-0.002999j
[2025-08-07 13:49:14] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -54.131780-0.003594j
[2025-08-07 13:49:18] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -54.201786+0.005240j
[2025-08-07 13:49:22] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -54.242384-0.005978j
[2025-08-07 13:49:26] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -54.173798+0.003137j
[2025-08-07 13:49:30] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -54.108609+0.000339j
[2025-08-07 13:49:35] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -54.228701+0.004043j
[2025-08-07 13:49:39] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.225824-0.004102j
[2025-08-07 13:49:43] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -54.333489-0.000818j
[2025-08-07 13:49:47] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -54.379009+0.003747j
[2025-08-07 13:49:51] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -54.323722+0.002433j
[2025-08-07 13:49:55] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -54.317895+0.001935j
[2025-08-07 13:50:00] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -54.346502+0.001355j
[2025-08-07 13:50:04] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -54.231359+0.004550j
[2025-08-07 13:50:08] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -54.368537+0.006352j
[2025-08-07 13:50:12] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -54.318077+0.000277j
[2025-08-07 13:50:16] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -54.279089-0.002100j
[2025-08-07 13:50:20] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -54.203888-0.001476j
[2025-08-07 13:50:25] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -54.289986-0.000681j
[2025-08-07 13:50:29] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -54.321634+0.000976j
[2025-08-07 13:50:33] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -54.250667-0.010390j
[2025-08-07 13:50:37] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -54.263935+0.003899j
[2025-08-07 13:50:41] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -54.230245-0.000399j
[2025-08-07 13:50:45] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -54.281142+0.001509j
[2025-08-07 13:50:50] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -54.252622+0.004082j
[2025-08-07 13:50:54] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -54.289450-0.002279j
[2025-08-07 13:50:58] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -54.309422-0.002662j
[2025-08-07 13:51:02] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -54.165249+0.003206j
[2025-08-07 13:51:06] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -54.199718-0.002245j
[2025-08-07 13:51:10] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -54.239699+0.005436j
[2025-08-07 13:51:15] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -54.178484+0.001456j
[2025-08-07 13:51:19] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -54.137599+0.002926j
[2025-08-07 13:51:23] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -54.047317+0.001283j
[2025-08-07 13:51:27] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -54.037777+0.000932j
[2025-08-07 13:51:31] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -54.075862-0.000963j
[2025-08-07 13:51:35] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -54.096415-0.002190j
[2025-08-07 13:51:39] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -54.201362-0.001006j
[2025-08-07 13:51:44] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -54.059573+0.006350j
[2025-08-07 13:51:48] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -54.168017-0.003611j
[2025-08-07 13:51:52] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -54.183704-0.001586j
[2025-08-07 13:51:56] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -54.193790+0.001725j
[2025-08-07 13:52:00] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -54.116965-0.000360j
[2025-08-07 13:52:04] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -54.137447+0.001311j
[2025-08-07 13:52:09] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -54.203835-0.001285j
[2025-08-07 13:52:13] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -54.163055-0.000102j
[2025-08-07 13:52:17] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -54.148631+0.006265j
[2025-08-07 13:52:21] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -54.232319-0.003228j
[2025-08-07 13:52:25] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -54.260810+0.004653j
[2025-08-07 13:52:29] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -54.273088-0.002881j
[2025-08-07 13:52:34] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -54.275995+0.004487j
[2025-08-07 13:52:38] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -54.264732+0.002977j
[2025-08-07 13:52:42] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -54.159180+0.005881j
[2025-08-07 13:52:46] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -54.199974+0.001371j
[2025-08-07 13:52:50] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -54.161662+0.002708j
[2025-08-07 13:52:54] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -54.060151-0.000939j
[2025-08-07 13:52:58] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -54.227418-0.000249j
[2025-08-07 13:53:03] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -54.205207+0.002859j
[2025-08-07 13:53:07] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -54.200787-0.000626j
[2025-08-07 13:53:11] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -54.183416+0.003112j
[2025-08-07 13:53:15] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -54.263582+0.002715j
[2025-08-07 13:53:19] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -54.260549+0.000148j
[2025-08-07 13:53:23] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -54.187543+0.000398j
[2025-08-07 13:53:27] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -54.205806+0.001086j
[2025-08-07 13:53:32] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -54.340254-0.005245j
[2025-08-07 13:53:36] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -54.309849-0.002705j
[2025-08-07 13:53:40] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -54.330829-0.000124j
[2025-08-07 13:53:44] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -54.267919+0.001325j
[2025-08-07 13:53:48] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -54.213335+0.003607j
[2025-08-07 13:53:52] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -54.201823+0.004285j
[2025-08-07 13:53:56] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -54.153091+0.002594j
[2025-08-07 13:53:56] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-07 13:54:01] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -54.129909+0.002793j
[2025-08-07 13:54:05] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -54.104397+0.001624j
[2025-08-07 13:54:09] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -54.208972-0.000907j
[2025-08-07 13:54:13] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -54.236927-0.001065j
[2025-08-07 13:54:17] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -54.234598-0.001233j
[2025-08-07 13:54:21] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -54.296775+0.002904j
[2025-08-07 13:54:25] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -54.290102-0.001768j
[2025-08-07 13:54:30] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -54.354866-0.007500j
[2025-08-07 13:54:34] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -54.393444-0.000823j
[2025-08-07 13:54:38] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -54.349840-0.003724j
[2025-08-07 13:54:42] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -54.258868-0.000458j
[2025-08-07 13:54:46] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -54.179648+0.002746j
[2025-08-07 13:54:50] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -54.183011+0.002217j
[2025-08-07 13:54:55] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -54.258733+0.003910j
[2025-08-07 13:54:59] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -54.156105-0.000662j
[2025-08-07 13:55:03] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -54.189962-0.003182j
[2025-08-07 13:55:07] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -54.102887+0.001864j
[2025-08-07 13:55:11] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -54.159886-0.002538j
[2025-08-07 13:55:15] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -54.232948-0.002961j
[2025-08-07 13:55:20] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -54.220277+0.000152j
[2025-08-07 13:55:24] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -54.203888+0.003531j
[2025-08-07 13:55:28] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -54.244890+0.000226j
[2025-08-07 13:55:32] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -54.304914-0.002435j
[2025-08-07 13:55:36] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -54.277453-0.002432j
[2025-08-07 13:55:40] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -54.281674-0.006571j
[2025-08-07 13:55:45] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -54.261605-0.004561j
[2025-08-07 13:55:49] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -54.276378-0.000801j
[2025-08-07 13:55:53] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -54.318734+0.001050j
[2025-08-07 13:55:57] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -54.344824-0.000192j
[2025-08-07 13:56:01] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -54.249564-0.004558j
[2025-08-07 13:56:05] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -54.238119+0.000909j
[2025-08-07 13:56:09] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -54.380214+0.011611j
[2025-08-07 13:56:14] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -54.296861+0.000808j
[2025-08-07 13:56:18] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -54.254308+0.003391j
[2025-08-07 13:56:22] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -54.370016+0.001271j
[2025-08-07 13:56:26] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -54.344704-0.008485j
[2025-08-07 13:56:30] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -54.283893+0.000354j
[2025-08-07 13:56:34] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -54.223366+0.006694j
[2025-08-07 13:56:39] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -54.169745+0.000430j
[2025-08-07 13:56:43] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -54.175181-0.003734j
[2025-08-07 13:56:47] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -54.143304-0.003163j
[2025-08-07 13:56:51] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -54.092762-0.001143j
[2025-08-07 13:56:55] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -54.190212+0.004543j
[2025-08-07 13:56:59] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -54.145674-0.002874j
[2025-08-07 13:57:04] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -54.141536-0.007896j
[2025-08-07 13:57:08] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -54.125069-0.000737j
[2025-08-07 13:57:12] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -54.151417-0.002340j
[2025-08-07 13:57:16] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -54.184239-0.001248j
[2025-08-07 13:57:20] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -54.155000+0.001937j
[2025-08-07 13:57:24] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -54.272533+0.000472j
[2025-08-07 13:57:24] RESTART #1 | Period: 300
[2025-08-07 13:57:28] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -54.231825-0.003163j
[2025-08-07 13:57:33] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -54.285660+0.003610j
[2025-08-07 13:57:37] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -54.258855+0.001993j
[2025-08-07 13:57:41] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -54.218473+0.000547j
[2025-08-07 13:57:45] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -54.384976+0.004621j
[2025-08-07 13:57:49] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -54.289775+0.005853j
[2025-08-07 13:57:53] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -54.216034-0.002899j
[2025-08-07 13:57:57] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -54.294622+0.000154j
[2025-08-07 13:58:02] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -54.352074-0.003856j
[2025-08-07 13:58:06] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -54.321959+0.000901j
[2025-08-07 13:58:10] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -54.196998+0.005640j
[2025-08-07 13:58:14] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -54.338050+0.000923j
[2025-08-07 13:58:18] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -54.218956-0.004745j
[2025-08-07 13:58:22] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -54.186875+0.002828j
[2025-08-07 13:58:27] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -54.185168-0.005903j
[2025-08-07 13:58:31] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -54.190162-0.009382j
[2025-08-07 13:58:35] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -54.266782-0.000017j
[2025-08-07 13:58:39] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -54.347286-0.001329j
[2025-08-07 13:58:43] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -54.106083+0.005119j
[2025-08-07 13:58:47] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -54.135583+0.005127j
[2025-08-07 13:58:51] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -54.221789+0.003672j
[2025-08-07 13:58:56] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -54.280207+0.009739j
[2025-08-07 13:59:00] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -54.278624-0.001178j
[2025-08-07 13:59:04] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -54.250761+0.006880j
[2025-08-07 13:59:08] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -54.219239+0.000154j
[2025-08-07 13:59:12] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -54.344656-0.005794j
[2025-08-07 13:59:16] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -54.286236-0.001015j
[2025-08-07 13:59:20] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -54.306457+0.004539j
[2025-08-07 13:59:25] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -54.254901+0.001409j
[2025-08-07 13:59:29] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -54.157199-0.002421j
[2025-08-07 13:59:33] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -54.131592+0.000533j
[2025-08-07 13:59:37] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -54.229632+0.002080j
[2025-08-07 13:59:41] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -54.246454-0.000130j
[2025-08-07 13:59:45] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -54.190594+0.001055j
[2025-08-07 13:59:50] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -54.095589+0.002269j
[2025-08-07 13:59:54] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -54.112884-0.002659j
[2025-08-07 13:59:58] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -54.181217+0.005815j
[2025-08-07 14:00:02] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -54.145908-0.003717j
[2025-08-07 14:00:06] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -54.192148+0.001525j
[2025-08-07 14:00:10] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -54.184906+0.000089j
[2025-08-07 14:00:15] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -54.114158+0.002043j
[2025-08-07 14:00:19] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -54.170282-0.002777j
[2025-08-07 14:00:23] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -54.188985+0.000240j
[2025-08-07 14:00:27] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -54.140139-0.002787j
[2025-08-07 14:00:31] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -54.195177-0.000564j
[2025-08-07 14:00:35] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -54.257326-0.007138j
[2025-08-07 14:00:39] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -54.126880+0.002599j
[2025-08-07 14:00:44] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -54.218795-0.003534j
[2025-08-07 14:00:48] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -54.182040-0.002697j
[2025-08-07 14:00:52] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -54.196939-0.000573j
[2025-08-07 14:00:52] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-07 14:00:56] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -54.195585-0.001652j
[2025-08-07 14:01:00] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -54.230681-0.001875j
[2025-08-07 14:01:04] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -54.246737+0.000828j
[2025-08-07 14:01:09] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -54.299031+0.001348j
[2025-08-07 14:01:13] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -54.220532+0.002978j
[2025-08-07 14:01:17] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -54.211772-0.000352j
[2025-08-07 14:01:21] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -54.233029-0.005258j
[2025-08-07 14:01:25] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -54.254022-0.003901j
[2025-08-07 14:01:29] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -54.103970-0.001392j
[2025-08-07 14:01:34] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -54.171506-0.001028j
[2025-08-07 14:01:38] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -54.275420-0.005595j
[2025-08-07 14:01:42] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -54.229555-0.005554j
[2025-08-07 14:01:46] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -54.215242-0.002231j
[2025-08-07 14:01:50] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -54.096308+0.003657j
[2025-08-07 14:01:54] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -54.159812-0.002032j
[2025-08-07 14:01:58] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -54.216854-0.001995j
[2025-08-07 14:02:03] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -54.275725+0.007043j
[2025-08-07 14:02:07] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -54.319627+0.000504j
[2025-08-07 14:02:11] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -54.315234+0.004080j
[2025-08-07 14:02:15] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -54.361103+0.005188j
[2025-08-07 14:02:19] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -54.296461+0.003289j
[2025-08-07 14:02:23] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -54.207327-0.000901j
[2025-08-07 14:02:28] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -54.224696+0.002802j
[2025-08-07 14:02:32] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -54.273541-0.002119j
[2025-08-07 14:02:36] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -54.361996+0.001444j
[2025-08-07 14:02:40] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -54.152884-0.005640j
[2025-08-07 14:02:44] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -54.347827-0.002582j
[2025-08-07 14:02:48] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -54.266439-0.000883j
[2025-08-07 14:02:52] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -54.184840-0.001357j
[2025-08-07 14:02:57] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -54.268484-0.001606j
[2025-08-07 14:03:01] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -54.275453-0.001779j
[2025-08-07 14:03:05] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -54.204797-0.002483j
[2025-08-07 14:03:09] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -54.171948+0.001824j
[2025-08-07 14:03:13] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -54.090638-0.006532j
[2025-08-07 14:03:17] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -54.112983+0.000757j
[2025-08-07 14:03:21] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -54.203221+0.003821j
[2025-08-07 14:03:26] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -54.177095+0.001853j
[2025-08-07 14:03:30] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -54.215091+0.002770j
[2025-08-07 14:03:34] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -54.142247-0.006843j
[2025-08-07 14:03:38] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -54.224047-0.002724j
[2025-08-07 14:03:42] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -54.199731-0.000024j
[2025-08-07 14:03:46] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -54.157153+0.003225j
[2025-08-07 14:03:50] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -54.187670-0.001423j
[2025-08-07 14:03:55] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -54.089774-0.001869j
[2025-08-07 14:03:59] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -54.224275+0.001382j
[2025-08-07 14:04:03] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -54.120412-0.001483j
[2025-08-07 14:04:07] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -54.104072+0.005774j
[2025-08-07 14:04:11] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -54.110861-0.001764j
[2025-08-07 14:04:15] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -54.140796+0.002465j
[2025-08-07 14:04:20] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -54.129254+0.000405j
[2025-08-07 14:04:24] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -54.113800-0.000749j
[2025-08-07 14:04:28] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -54.037936+0.002851j
[2025-08-07 14:04:32] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -54.191993+0.004041j
[2025-08-07 14:04:36] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -54.254701-0.000594j
[2025-08-07 14:04:40] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -54.179656+0.000081j
[2025-08-07 14:04:45] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -54.159547+0.002904j
[2025-08-07 14:04:49] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -54.107488+0.001682j
[2025-08-07 14:04:53] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -54.113710-0.000783j
[2025-08-07 14:04:57] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -54.177394-0.000930j
[2025-08-07 14:05:01] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -54.225260+0.001701j
[2025-08-07 14:05:05] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -54.236038+0.004155j
[2025-08-07 14:05:09] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -54.165807+0.002164j
[2025-08-07 14:05:14] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -54.177908+0.001641j
[2025-08-07 14:05:18] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -54.309725-0.001890j
[2025-08-07 14:05:22] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -54.243678+0.001433j
[2025-08-07 14:05:26] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -54.230754-0.000080j
[2025-08-07 14:05:30] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -54.184266-0.001829j
[2025-08-07 14:05:34] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -54.139920-0.000855j
[2025-08-07 14:05:39] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -54.161514-0.002075j
[2025-08-07 14:05:43] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -54.136050+0.004234j
[2025-08-07 14:05:47] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -54.162054-0.002251j
[2025-08-07 14:05:51] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -54.001707+0.002115j
[2025-08-07 14:05:55] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -54.201798-0.000811j
[2025-08-07 14:05:59] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -54.268211+0.001237j
[2025-08-07 14:06:04] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -54.204850+0.001892j
[2025-08-07 14:06:08] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -54.291374+0.001397j
[2025-08-07 14:06:12] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -54.244869+0.006943j
[2025-08-07 14:06:16] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -54.281540-0.003266j
[2025-08-07 14:06:20] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -54.305310+0.000682j
[2025-08-07 14:06:24] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -54.268900+0.001995j
[2025-08-07 14:06:28] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -54.225775+0.005462j
[2025-08-07 14:06:33] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -54.264347+0.006873j
[2025-08-07 14:06:37] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -54.308652-0.004932j
[2025-08-07 14:06:41] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -54.330030-0.005812j
[2025-08-07 14:06:45] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -54.290164-0.003688j
[2025-08-07 14:06:49] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -54.222920+0.003702j
[2025-08-07 14:06:53] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -54.221138-0.002228j
[2025-08-07 14:06:57] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -54.217884+0.002782j
[2025-08-07 14:07:02] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -54.226609+0.002634j
[2025-08-07 14:07:06] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -54.259707+0.003928j
[2025-08-07 14:07:10] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -54.218168+0.004005j
[2025-08-07 14:07:14] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -54.191486-0.007903j
[2025-08-07 14:07:18] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -54.053006+0.003586j
[2025-08-07 14:07:22] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -54.080288-0.000237j
[2025-08-07 14:07:26] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -54.258892-0.001649j
[2025-08-07 14:07:31] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -54.248250-0.001608j
[2025-08-07 14:07:35] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -54.129233-0.003047j
[2025-08-07 14:07:39] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -54.170186-0.007549j
[2025-08-07 14:07:43] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -54.297514+0.001658j
[2025-08-07 14:07:47] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -54.321564-0.005283j
[2025-08-07 14:07:47] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-07 14:07:51] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -54.232966+0.003383j
[2025-08-07 14:07:55] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -54.174837-0.007585j
[2025-08-07 14:08:00] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -54.174800-0.006282j
[2025-08-07 14:08:04] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -54.147118-0.001382j
[2025-08-07 14:08:08] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -54.098470+0.006473j
[2025-08-07 14:08:12] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -54.176598+0.000224j
[2025-08-07 14:08:16] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -54.146437-0.002626j
[2025-08-07 14:08:20] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -54.289574+0.001547j
[2025-08-07 14:08:25] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -54.248390+0.000758j
[2025-08-07 14:08:29] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -54.239419-0.003125j
[2025-08-07 14:08:33] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -54.316959+0.001540j
[2025-08-07 14:08:37] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -54.252114+0.004087j
[2025-08-07 14:08:41] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -54.284980+0.001007j
[2025-08-07 14:08:45] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -54.249519-0.001091j
[2025-08-07 14:08:50] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -54.307700+0.002740j
[2025-08-07 14:08:54] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -54.313737+0.004877j
[2025-08-07 14:08:58] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -54.352738-0.002841j
[2025-08-07 14:09:02] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -54.406213+0.000730j
[2025-08-07 14:09:06] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -54.390598+0.000472j
[2025-08-07 14:09:10] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -54.386427+0.002516j
[2025-08-07 14:09:15] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -54.270286+0.000743j
[2025-08-07 14:09:19] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -54.302175+0.002053j
[2025-08-07 14:09:23] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -54.386042-0.004508j
[2025-08-07 14:09:27] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -54.310585+0.000854j
[2025-08-07 14:09:31] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -54.334621+0.000212j
[2025-08-07 14:09:35] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -54.366935+0.005428j
[2025-08-07 14:09:39] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -54.402564-0.002770j
[2025-08-07 14:09:44] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -54.295302+0.002392j
[2025-08-07 14:09:48] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -54.282183+0.001064j
[2025-08-07 14:09:52] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -54.354620-0.000598j
[2025-08-07 14:09:56] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -54.278019-0.001064j
[2025-08-07 14:10:00] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -54.322795+0.002549j
[2025-08-07 14:10:04] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -54.397270+0.003212j
[2025-08-07 14:10:09] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -54.445969-0.002002j
[2025-08-07 14:10:13] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -54.431534-0.001610j
[2025-08-07 14:10:17] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -54.415680+0.002253j
[2025-08-07 14:10:21] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -54.275916+0.000946j
[2025-08-07 14:10:25] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -54.387353-0.005288j
[2025-08-07 14:10:29] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -54.406170+0.003354j
[2025-08-07 14:10:34] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -54.260131-0.002275j
[2025-08-07 14:10:38] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -54.277408+0.006925j
[2025-08-07 14:10:42] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -54.215158-0.002653j
[2025-08-07 14:10:46] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -54.258141+0.000733j
[2025-08-07 14:10:50] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -54.180494-0.003451j
[2025-08-07 14:10:54] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -54.228202-0.000815j
[2025-08-07 14:10:58] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -54.297198-0.001211j
[2025-08-07 14:11:03] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -54.312893-0.003627j
[2025-08-07 14:11:07] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -54.230940+0.001470j
[2025-08-07 14:11:11] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -54.333639+0.001929j
[2025-08-07 14:11:15] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -54.350627-0.005450j
[2025-08-07 14:11:19] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -54.292012+0.004956j
[2025-08-07 14:11:23] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -54.140218+0.004619j
[2025-08-07 14:11:27] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -54.228153+0.003448j
[2025-08-07 14:11:32] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -54.356168+0.001410j
[2025-08-07 14:11:36] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -54.207198+0.004624j
[2025-08-07 14:11:40] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -54.273349+0.001296j
[2025-08-07 14:11:44] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -54.182544+0.007693j
[2025-08-07 14:11:48] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -54.239026-0.000991j
[2025-08-07 14:11:52] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -54.280846-0.000056j
[2025-08-07 14:11:57] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -54.222848+0.002173j
[2025-08-07 14:12:01] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -54.178378-0.004393j
[2025-08-07 14:12:05] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -54.265212+0.000912j
[2025-08-07 14:12:09] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -54.348604+0.001380j
[2025-08-07 14:12:13] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -54.403298+0.001003j
[2025-08-07 14:12:17] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -54.344306-0.004505j
[2025-08-07 14:12:21] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -54.345516-0.000747j
[2025-08-07 14:12:26] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -54.234367+0.000672j
[2025-08-07 14:12:30] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -54.223411-0.004187j
[2025-08-07 14:12:34] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -54.345841+0.002086j
[2025-08-07 14:12:38] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -54.225902-0.000417j
[2025-08-07 14:12:42] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -54.195349+0.000440j
[2025-08-07 14:12:46] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -54.217861+0.000450j
[2025-08-07 14:12:50] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -54.112930-0.000728j
[2025-08-07 14:12:55] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -54.136705+0.000214j
[2025-08-07 14:12:59] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -54.080330-0.000111j
[2025-08-07 14:13:03] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -54.090003+0.001154j
[2025-08-07 14:13:07] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -54.109380+0.003623j
[2025-08-07 14:13:11] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -54.082326+0.001702j
[2025-08-07 14:13:15] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -54.233351-0.001060j
[2025-08-07 14:13:20] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -54.249565+0.000046j
[2025-08-07 14:13:24] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -54.276628-0.001934j
[2025-08-07 14:13:28] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -54.314917+0.001226j
[2025-08-07 14:13:32] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -54.207027-0.001898j
[2025-08-07 14:13:36] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -54.219842+0.002618j
[2025-08-07 14:13:40] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -54.246657-0.002221j
[2025-08-07 14:13:45] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -54.201892+0.001226j
[2025-08-07 14:13:49] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -54.216492+0.002186j
[2025-08-07 14:13:53] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -54.240603-0.002375j
[2025-08-07 14:13:57] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -54.160647-0.001739j
[2025-08-07 14:14:01] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -54.127628+0.002463j
[2025-08-07 14:14:05] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -54.165882-0.001269j
[2025-08-07 14:14:09] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -54.234000+0.001020j
[2025-08-07 14:14:14] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -54.292793-0.003741j
[2025-08-07 14:14:18] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -54.185222-0.000335j
[2025-08-07 14:14:22] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -54.200319+0.003649j
[2025-08-07 14:14:26] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -54.238555+0.000773j
[2025-08-07 14:14:30] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -54.178840-0.004704j
[2025-08-07 14:14:34] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -54.187832+0.005895j
[2025-08-07 14:14:39] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -54.108468+0.003476j
[2025-08-07 14:14:43] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -54.230164-0.004154j
[2025-08-07 14:14:43] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-07 14:14:47] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -54.144352-0.000819j
[2025-08-07 14:14:51] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -54.162723+0.000785j
[2025-08-07 14:14:55] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -54.125638+0.000646j
[2025-08-07 14:14:59] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -54.069240+0.000271j
[2025-08-07 14:15:04] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -54.066180+0.004005j
[2025-08-07 14:15:08] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -54.074927-0.000768j
[2025-08-07 14:15:12] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -53.989383+0.004243j
[2025-08-07 14:15:16] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -54.046744-0.000018j
[2025-08-07 14:15:20] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -54.132721-0.000559j
[2025-08-07 14:15:24] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -54.203389-0.002860j
[2025-08-07 14:15:28] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -54.153277-0.006072j
[2025-08-07 14:15:33] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -54.211873+0.001046j
[2025-08-07 14:15:37] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -54.170047+0.001211j
[2025-08-07 14:15:41] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -54.185922-0.000448j
[2025-08-07 14:15:45] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -54.254177-0.000262j
[2025-08-07 14:15:49] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -54.115288+0.005718j
[2025-08-07 14:15:53] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -54.278732+0.002553j
[2025-08-07 14:15:57] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -54.222511-0.004713j
[2025-08-07 14:16:02] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -54.259250+0.004116j
[2025-08-07 14:16:06] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -54.286322+0.000583j
[2025-08-07 14:16:10] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -54.181710-0.001137j
[2025-08-07 14:16:14] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -54.192139-0.004500j
[2025-08-07 14:16:18] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -54.255887-0.000852j
[2025-08-07 14:16:22] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -54.250586-0.003511j
[2025-08-07 14:16:26] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -54.285438+0.002170j
[2025-08-07 14:16:31] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -54.124822+0.004009j
[2025-08-07 14:16:35] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -54.178507+0.001345j
[2025-08-07 14:16:39] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -54.187236+0.000776j
[2025-08-07 14:16:43] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -54.221631+0.000332j
[2025-08-07 14:16:47] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -54.241068-0.003535j
[2025-08-07 14:16:51] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -54.170499+0.003157j
[2025-08-07 14:16:55] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -54.218160-0.002252j
[2025-08-07 14:17:00] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -54.264643+0.001783j
[2025-08-07 14:17:04] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -54.332404+0.002665j
[2025-08-07 14:17:08] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -54.362340+0.000170j
[2025-08-07 14:17:12] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -54.287990-0.000043j
[2025-08-07 14:17:16] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -54.325187+0.000186j
[2025-08-07 14:17:20] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -54.213848-0.005341j
[2025-08-07 14:17:25] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -54.349062+0.001557j
[2025-08-07 14:17:29] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -54.244863-0.003173j
[2025-08-07 14:17:33] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -54.250939+0.009308j
[2025-08-07 14:17:37] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -54.262767-0.003864j
[2025-08-07 14:17:41] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -54.309503-0.000634j
[2025-08-07 14:17:45] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -54.342583-0.005588j
[2025-08-07 14:17:50] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -54.294098-0.000911j
[2025-08-07 14:17:54] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -54.282600+0.001620j
[2025-08-07 14:17:58] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -54.300939-0.004829j
[2025-08-07 14:18:02] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -54.382882+0.003694j
[2025-08-07 14:18:06] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -54.409283+0.002108j
[2025-08-07 14:18:10] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -54.411175+0.004802j
[2025-08-07 14:18:10] RESTART #2 | Period: 600
[2025-08-07 14:18:15] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -54.360889+0.001615j
[2025-08-07 14:18:19] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -54.413677-0.000751j
[2025-08-07 14:18:23] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -54.240529-0.002971j
[2025-08-07 14:18:27] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -54.307970+0.000134j
[2025-08-07 14:18:31] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -54.295700+0.002612j
[2025-08-07 14:18:35] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -54.329548+0.005828j
[2025-08-07 14:18:39] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -54.292385-0.002765j
[2025-08-07 14:18:44] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -54.238771-0.003193j
[2025-08-07 14:18:48] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -54.314804+0.002149j
[2025-08-07 14:18:52] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -54.314998+0.003644j
[2025-08-07 14:18:56] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -54.243415+0.001584j
[2025-08-07 14:19:00] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -54.275672+0.001308j
[2025-08-07 14:19:04] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -54.277439+0.004934j
[2025-08-07 14:19:09] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -54.298589-0.000590j
[2025-08-07 14:19:13] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -54.269186+0.007395j
[2025-08-07 14:19:17] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -54.333595-0.005009j
[2025-08-07 14:19:21] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -54.263431+0.000255j
[2025-08-07 14:19:25] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -54.296961+0.001240j
[2025-08-07 14:19:29] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -54.211596-0.000906j
[2025-08-07 14:19:34] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -54.165154-0.002171j
[2025-08-07 14:19:38] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -54.209767+0.000800j
[2025-08-07 14:19:42] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -54.180484+0.002875j
[2025-08-07 14:19:46] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -54.242580+0.004947j
[2025-08-07 14:19:50] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -54.226807+0.002091j
[2025-08-07 14:19:54] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -54.164561+0.007792j
[2025-08-07 14:19:58] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -54.109867+0.002648j
[2025-08-07 14:20:03] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -54.183755+0.001388j
[2025-08-07 14:20:07] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -54.181930+0.003786j
[2025-08-07 14:20:11] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -54.250029+0.003889j
[2025-08-07 14:20:15] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -54.326261-0.001386j
[2025-08-07 14:20:19] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -54.202337+0.000757j
[2025-08-07 14:20:23] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -54.200757-0.001846j
[2025-08-07 14:20:27] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -54.182326-0.001385j
[2025-08-07 14:20:32] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -54.174856+0.000182j
[2025-08-07 14:20:36] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -54.194410+0.002871j
[2025-08-07 14:20:40] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -54.285411+0.001129j
[2025-08-07 14:20:44] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -54.254743+0.000397j
[2025-08-07 14:20:48] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -54.269533+0.000855j
[2025-08-07 14:20:52] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -54.249294-0.000253j
[2025-08-07 14:20:56] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -54.349372+0.000577j
[2025-08-07 14:21:01] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -54.249194+0.002438j
[2025-08-07 14:21:05] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -54.273773-0.003042j
[2025-08-07 14:21:09] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -54.159924+0.002790j
[2025-08-07 14:21:13] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -54.278000-0.000190j
[2025-08-07 14:21:17] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -54.226315-0.003747j
[2025-08-07 14:21:21] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -54.247605+0.000218j
[2025-08-07 14:21:26] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -54.266372+0.005437j
[2025-08-07 14:21:30] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -54.244588+0.002629j
[2025-08-07 14:21:34] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -54.192884+0.006393j
[2025-08-07 14:21:38] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -54.193650+0.001365j
[2025-08-07 14:21:38] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-07 14:21:42] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -54.064854+0.002167j
[2025-08-07 14:21:46] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -54.110233+0.001449j
[2025-08-07 14:21:50] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -54.162890+0.005805j
[2025-08-07 14:21:55] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -54.263142-0.005123j
[2025-08-07 14:21:59] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -54.203402+0.002961j
[2025-08-07 14:22:03] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -54.212714+0.000827j
[2025-08-07 14:22:07] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -54.195134-0.004192j
[2025-08-07 14:22:11] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -54.324031+0.004480j
[2025-08-07 14:22:15] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -54.280772-0.003956j
[2025-08-07 14:22:20] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -54.238438-0.000858j
[2025-08-07 14:22:24] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -54.286504-0.006660j
[2025-08-07 14:22:28] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -54.264202-0.000367j
[2025-08-07 14:22:32] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -54.250971-0.000699j
[2025-08-07 14:22:36] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -54.209964+0.001659j
[2025-08-07 14:22:40] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -54.201068-0.000814j
[2025-08-07 14:22:45] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -54.209662+0.000688j
[2025-08-07 14:22:49] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -54.174113-0.000185j
[2025-08-07 14:22:53] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -54.232476-0.001327j
[2025-08-07 14:22:57] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -54.193209+0.001489j
[2025-08-07 14:23:01] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -54.132334-0.001529j
[2025-08-07 14:23:05] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -54.243965+0.001839j
[2025-08-07 14:23:09] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -54.243140+0.000318j
[2025-08-07 14:23:14] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -54.357676+0.002220j
[2025-08-07 14:23:18] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -54.344566+0.000874j
[2025-08-07 14:23:22] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -54.325402-0.001701j
[2025-08-07 14:23:26] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -54.200392-0.005082j
[2025-08-07 14:23:30] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -54.352179+0.004633j
[2025-08-07 14:23:34] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -54.427704-0.001699j
[2025-08-07 14:23:39] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -54.232996+0.002142j
[2025-08-07 14:23:43] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -54.305256-0.003884j
[2025-08-07 14:23:47] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -54.298462+0.001231j
[2025-08-07 14:23:51] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -54.331990+0.001030j
[2025-08-07 14:23:55] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -54.234216+0.001898j
[2025-08-07 14:23:59] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -54.255282+0.000566j
[2025-08-07 14:24:04] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -54.099359-0.000176j
[2025-08-07 14:24:08] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -54.110514-0.001522j
[2025-08-07 14:24:12] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -54.080995-0.000475j
[2025-08-07 14:24:16] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -54.160250-0.001745j
[2025-08-07 14:24:20] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -54.163613-0.002945j
[2025-08-07 14:24:24] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -54.101563-0.000145j
[2025-08-07 14:24:28] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -54.241198-0.001206j
[2025-08-07 14:24:33] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.303446+0.004993j
[2025-08-07 14:24:37] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -54.195310+0.005285j
[2025-08-07 14:24:41] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -54.175287-0.003565j
[2025-08-07 14:24:45] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -54.245762-0.003228j
[2025-08-07 14:24:49] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -54.250949+0.003707j
[2025-08-07 14:24:53] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -54.211293-0.003381j
[2025-08-07 14:24:57] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -54.226735+0.001099j
[2025-08-07 14:25:02] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -54.224293+0.001946j
[2025-08-07 14:25:06] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -54.143430+0.003132j
[2025-08-07 14:25:10] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -54.181726+0.001832j
[2025-08-07 14:25:14] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -54.221026-0.003831j
[2025-08-07 14:25:18] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -54.216315+0.002062j
[2025-08-07 14:25:22] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -54.242515+0.002192j
[2025-08-07 14:25:27] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -54.207015-0.000850j
[2025-08-07 14:25:31] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -54.170043-0.004650j
[2025-08-07 14:25:35] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -54.053721-0.001559j
[2025-08-07 14:25:39] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -54.204970+0.010226j
[2025-08-07 14:25:43] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -54.246134-0.000838j
[2025-08-07 14:25:47] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -54.168946+0.002855j
[2025-08-07 14:25:51] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -54.163399+0.001151j
[2025-08-07 14:25:56] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -54.193326-0.001449j
[2025-08-07 14:26:00] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -54.328271+0.001402j
[2025-08-07 14:26:04] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -54.265101+0.000071j
[2025-08-07 14:26:08] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -54.244656-0.001214j
[2025-08-07 14:26:12] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -54.240919+0.002885j
[2025-08-07 14:26:16] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -54.197881-0.004665j
[2025-08-07 14:26:20] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -54.215638+0.008244j
[2025-08-07 14:26:25] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -54.281596+0.001479j
[2025-08-07 14:26:29] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -54.312986+0.003594j
[2025-08-07 14:26:33] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -54.287125-0.002110j
[2025-08-07 14:26:37] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -54.267323+0.004100j
[2025-08-07 14:26:41] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -54.306134-0.009296j
[2025-08-07 14:26:45] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -54.226695+0.003818j
[2025-08-07 14:26:50] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -54.202264+0.002319j
[2025-08-07 14:26:54] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -54.213108+0.005074j
[2025-08-07 14:26:58] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -54.221100+0.001687j
[2025-08-07 14:27:02] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -54.246882-0.008306j
[2025-08-07 14:27:06] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -54.343870-0.000833j
[2025-08-07 14:27:10] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -54.337351-0.002582j
[2025-08-07 14:27:15] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -54.360312-0.001346j
[2025-08-07 14:27:19] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -54.297900-0.001635j
[2025-08-07 14:27:23] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -54.319453+0.005596j
[2025-08-07 14:27:27] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -54.303701-0.000803j
[2025-08-07 14:27:31] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -54.404050+0.004558j
[2025-08-07 14:27:35] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -54.320603+0.002836j
[2025-08-07 14:27:39] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -54.315685+0.000634j
[2025-08-07 14:27:44] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -54.268608+0.002514j
[2025-08-07 14:27:48] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -54.235217+0.005102j
[2025-08-07 14:27:52] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -54.202903-0.002754j
[2025-08-07 14:27:56] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -54.279534+0.003412j
[2025-08-07 14:28:00] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -54.195519+0.002711j
[2025-08-07 14:28:04] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -54.191674+0.005387j
[2025-08-07 14:28:09] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -54.187861+0.007488j
[2025-08-07 14:28:13] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -54.255059-0.000319j
[2025-08-07 14:28:17] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -54.265655-0.003113j
[2025-08-07 14:28:21] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -54.246291-0.001203j
[2025-08-07 14:28:25] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -54.339254-0.006601j
[2025-08-07 14:28:29] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -54.233541-0.000459j
[2025-08-07 14:28:34] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -54.244536+0.001952j
[2025-08-07 14:28:34] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-07 14:28:38] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -54.204350-0.006094j
[2025-08-07 14:28:42] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -54.196060-0.006736j
[2025-08-07 14:28:46] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -54.274270+0.002475j
[2025-08-07 14:28:50] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -54.175527+0.001075j
[2025-08-07 14:28:54] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -54.300798+0.004040j
[2025-08-07 14:28:58] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -54.276629-0.001528j
[2025-08-07 14:29:03] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -54.223023+0.002727j
[2025-08-07 14:29:07] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -54.203470-0.001873j
[2025-08-07 14:29:11] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -54.190268-0.001171j
[2025-08-07 14:29:15] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -54.078538+0.002644j
[2025-08-07 14:29:19] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -54.125850-0.000417j
[2025-08-07 14:29:23] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -54.165665+0.000981j
[2025-08-07 14:29:27] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -54.277407-0.000368j
[2025-08-07 14:29:32] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -54.319211-0.007603j
[2025-08-07 14:29:36] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -54.209554-0.000316j
[2025-08-07 14:29:40] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -54.295879+0.001312j
[2025-08-07 14:29:44] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -54.159118+0.001745j
[2025-08-07 14:29:48] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -54.101447-0.001944j
[2025-08-07 14:29:52] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -54.168192-0.000181j
[2025-08-07 14:29:56] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -54.195172-0.003145j
[2025-08-07 14:30:01] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -54.199379-0.000260j
[2025-08-07 14:30:05] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -54.263170+0.000479j
[2025-08-07 14:30:09] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -54.274285-0.001787j
[2025-08-07 14:30:13] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -54.111257-0.000682j
[2025-08-07 14:30:17] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -54.182243-0.002752j
[2025-08-07 14:30:21] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -54.199672+0.002856j
[2025-08-07 14:30:26] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -54.192880+0.003326j
[2025-08-07 14:30:30] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -54.405391-0.004852j
[2025-08-07 14:30:34] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -54.366109+0.000692j
[2025-08-07 14:30:38] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -54.440509-0.000092j
[2025-08-07 14:30:42] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -54.222458-0.000490j
[2025-08-07 14:30:46] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -54.283510+0.001318j
[2025-08-07 14:30:50] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -54.288586+0.001160j
[2025-08-07 14:30:55] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -54.310732-0.002969j
[2025-08-07 14:30:59] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -54.260191+0.006680j
[2025-08-07 14:31:03] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -54.257189+0.000690j
[2025-08-07 14:31:07] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -54.233248+0.001132j
[2025-08-07 14:31:11] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -54.278212-0.002331j
[2025-08-07 14:31:15] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -54.265031-0.005614j
[2025-08-07 14:31:20] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -54.270633-0.004609j
[2025-08-07 14:31:24] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -54.239133-0.001569j
[2025-08-07 14:31:28] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -54.296152-0.001316j
[2025-08-07 14:31:32] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -54.154088+0.000372j
[2025-08-07 14:31:36] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -54.240568-0.000143j
[2025-08-07 14:31:40] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -54.181910-0.001102j
[2025-08-07 14:31:45] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -54.206958-0.001505j
[2025-08-07 14:31:49] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -54.101537+0.004234j
[2025-08-07 14:31:53] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -54.076500+0.002102j
[2025-08-07 14:31:57] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -54.045524-0.005920j
[2025-08-07 14:32:01] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -54.151776+0.001438j
[2025-08-07 14:32:05] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -54.071410-0.000654j
[2025-08-07 14:32:09] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -54.184014+0.002907j
[2025-08-07 14:32:14] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -54.244402+0.001652j
[2025-08-07 14:32:18] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -54.264273-0.002536j
[2025-08-07 14:32:22] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -54.175305+0.002730j
[2025-08-07 14:32:26] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -54.165737+0.001204j
[2025-08-07 14:32:30] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -54.005008-0.001555j
[2025-08-07 14:32:34] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -54.065782+0.002222j
[2025-08-07 14:32:39] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -54.073566+0.001477j
[2025-08-07 14:32:43] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -54.206697-0.002699j
[2025-08-07 14:32:47] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -54.176726-0.005934j
[2025-08-07 14:32:51] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -54.164303+0.000006j
[2025-08-07 14:32:55] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -54.114248-0.003521j
[2025-08-07 14:32:59] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -54.224135+0.005151j
[2025-08-07 14:33:04] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -54.247001-0.007593j
[2025-08-07 14:33:08] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -54.228614-0.000562j
[2025-08-07 14:33:12] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -54.221752+0.000372j
[2025-08-07 14:33:16] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -54.144823-0.004028j
[2025-08-07 14:33:20] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -54.191820-0.000525j
[2025-08-07 14:33:24] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -54.042215-0.001148j
[2025-08-07 14:33:28] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -54.134069+0.003757j
[2025-08-07 14:33:33] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -54.217060+0.002844j
[2025-08-07 14:33:37] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -54.158805+0.000522j
[2025-08-07 14:33:41] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -54.192487+0.002733j
[2025-08-07 14:33:45] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -54.193890-0.004016j
[2025-08-07 14:33:49] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -54.142429-0.000932j
[2025-08-07 14:33:53] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -54.075292-0.003219j
[2025-08-07 14:33:57] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -54.060320-0.002139j
[2025-08-07 14:34:02] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -54.040843-0.010636j
[2025-08-07 14:34:06] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -53.973994+0.000158j
[2025-08-07 14:34:10] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -53.977411-0.001891j
[2025-08-07 14:34:14] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -54.164343+0.002012j
[2025-08-07 14:34:18] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -54.179040+0.004016j
[2025-08-07 14:34:22] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -54.131867+0.003610j
[2025-08-07 14:34:26] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -54.063603+0.005003j
[2025-08-07 14:34:31] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.993659+0.002001j
[2025-08-07 14:34:35] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -54.100906+0.000263j
[2025-08-07 14:34:39] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -54.025744+0.003702j
[2025-08-07 14:34:43] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -54.062401+0.001312j
[2025-08-07 14:34:47] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -54.028838-0.001227j
[2025-08-07 14:34:51] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -54.091484+0.002905j
[2025-08-07 14:34:56] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -54.246873-0.009184j
[2025-08-07 14:35:00] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -54.138340-0.000727j
[2025-08-07 14:35:04] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -54.288051-0.000503j
[2025-08-07 14:35:08] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -54.249572-0.001586j
[2025-08-07 14:35:12] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -54.230061-0.005153j
[2025-08-07 14:35:16] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -54.254521+0.004499j
[2025-08-07 14:35:20] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -54.292088+0.003940j
[2025-08-07 14:35:25] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -54.252918+0.002718j
[2025-08-07 14:35:29] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -54.280729+0.003277j
[2025-08-07 14:35:29] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-07 14:35:33] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -54.261932+0.004950j
[2025-08-07 14:35:37] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -54.243887+0.003988j
[2025-08-07 14:35:41] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -54.196348+0.003203j
[2025-08-07 14:35:45] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -54.353056+0.002603j
[2025-08-07 14:35:50] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -54.392392+0.001685j
[2025-08-07 14:35:54] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -54.348069-0.001468j
[2025-08-07 14:35:58] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -54.308697-0.002915j
[2025-08-07 14:36:02] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -54.291902-0.000768j
[2025-08-07 14:36:06] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -54.384980-0.001879j
[2025-08-07 14:36:10] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -54.202723+0.005630j
[2025-08-07 14:36:15] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -54.126781+0.002055j
[2025-08-07 14:36:19] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -54.141911-0.007354j
[2025-08-07 14:36:23] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -54.241980-0.001653j
[2025-08-07 14:36:27] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -54.222585-0.000550j
[2025-08-07 14:36:31] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -54.130419-0.005495j
[2025-08-07 14:36:35] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -54.179166+0.003431j
[2025-08-07 14:36:39] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -54.172172+0.007420j
[2025-08-07 14:36:44] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -54.248242+0.004501j
[2025-08-07 14:36:48] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -54.149564+0.005232j
[2025-08-07 14:36:52] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -54.251539+0.001438j
[2025-08-07 14:36:56] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -54.229087-0.002192j
[2025-08-07 14:37:00] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -54.229038+0.003068j
[2025-08-07 14:37:04] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -54.220679+0.002289j
[2025-08-07 14:37:09] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -54.090089-0.002634j
[2025-08-07 14:37:13] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -54.222097-0.003324j
[2025-08-07 14:37:17] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -54.256547+0.000731j
[2025-08-07 14:37:21] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -54.206842-0.000274j
[2025-08-07 14:37:25] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -54.159570-0.005304j
[2025-08-07 14:37:29] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -54.358988-0.001781j
[2025-08-07 14:37:34] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -54.333994+0.000894j
[2025-08-07 14:37:38] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -54.213273-0.003337j
[2025-08-07 14:37:42] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -54.345074-0.000538j
[2025-08-07 14:37:46] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -54.110983+0.000874j
[2025-08-07 14:37:50] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -54.152860+0.002083j
[2025-08-07 14:37:54] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -54.092045-0.004339j
[2025-08-07 14:37:58] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -54.033895-0.003473j
[2025-08-07 14:38:03] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -53.968914-0.004228j
[2025-08-07 14:38:07] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -54.079852-0.002529j
[2025-08-07 14:38:11] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -54.104629-0.002218j
[2025-08-07 14:38:15] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -54.139151+0.004090j
[2025-08-07 14:38:19] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -54.127661+0.001455j
[2025-08-07 14:38:23] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -54.149195-0.000936j
[2025-08-07 14:38:28] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -54.131013+0.000078j
[2025-08-07 14:38:32] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -54.103013+0.000566j
[2025-08-07 14:38:36] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -54.207497+0.001792j
[2025-08-07 14:38:40] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -54.078135-0.005316j
[2025-08-07 14:38:44] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -54.124587-0.002545j
[2025-08-07 14:38:48] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -54.245936-0.001517j
[2025-08-07 14:38:52] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -54.207834-0.000593j
[2025-08-07 14:38:57] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -54.211369-0.003468j
[2025-08-07 14:39:01] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -54.306505+0.006376j
[2025-08-07 14:39:05] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -54.312126+0.000361j
[2025-08-07 14:39:09] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -54.111323-0.002132j
[2025-08-07 14:39:13] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -54.159802-0.000217j
[2025-08-07 14:39:17] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -54.355267+0.001381j
[2025-08-07 14:39:21] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -54.306576-0.000950j
[2025-08-07 14:39:26] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -54.159082-0.003259j
[2025-08-07 14:39:30] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -54.184006-0.001581j
[2025-08-07 14:39:34] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -54.203300-0.004775j
[2025-08-07 14:39:38] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -54.230315-0.001779j
[2025-08-07 14:39:42] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -54.203211+0.000642j
[2025-08-07 14:39:46] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -54.230917-0.000431j
[2025-08-07 14:39:50] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -54.158677-0.000083j
[2025-08-07 14:39:55] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -54.114923-0.000246j
[2025-08-07 14:39:59] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -54.234702+0.000579j
[2025-08-07 14:40:03] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -54.122503-0.001719j
[2025-08-07 14:40:07] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -54.186148-0.001010j
[2025-08-07 14:40:11] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -54.134374-0.004864j
[2025-08-07 14:40:15] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -54.220578-0.006672j
[2025-08-07 14:40:20] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -54.156076+0.002498j
[2025-08-07 14:40:24] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -54.278130+0.000154j
[2025-08-07 14:40:28] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -54.216276+0.002718j
[2025-08-07 14:40:32] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -54.240544+0.001445j
[2025-08-07 14:40:36] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -54.261700-0.002465j
[2025-08-07 14:40:40] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -54.324925-0.000216j
[2025-08-07 14:40:45] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -54.214726-0.001991j
[2025-08-07 14:40:49] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -54.094764+0.002162j
[2025-08-07 14:40:53] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -54.188341-0.001275j
[2025-08-07 14:40:57] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -54.264616+0.002416j
[2025-08-07 14:41:01] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -54.142419+0.001108j
[2025-08-07 14:41:05] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -54.177970-0.006218j
[2025-08-07 14:41:09] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -54.207566+0.006980j
[2025-08-07 14:41:14] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -54.124391+0.003759j
[2025-08-07 14:41:18] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -54.151998+0.004141j
[2025-08-07 14:41:22] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -54.159135-0.003213j
[2025-08-07 14:41:26] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -54.125654-0.002993j
[2025-08-07 14:41:30] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -54.110767+0.000504j
[2025-08-07 14:41:34] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -54.106294+0.004170j
[2025-08-07 14:41:39] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -54.182023-0.003562j
[2025-08-07 14:41:43] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -54.147095-0.004211j
[2025-08-07 14:41:47] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -54.135634+0.004281j
[2025-08-07 14:41:51] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -54.249220+0.003506j
[2025-08-07 14:41:55] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -54.174175+0.005428j
[2025-08-07 14:41:59] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -54.213595-0.001383j
[2025-08-07 14:42:04] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -54.180588-0.002650j
[2025-08-07 14:42:08] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -54.232009+0.002910j
[2025-08-07 14:42:12] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -54.300415+0.001287j
[2025-08-07 14:42:16] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -54.339187+0.005314j
[2025-08-07 14:42:20] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -54.230734+0.000772j
[2025-08-07 14:42:24] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -54.313762+0.000732j
[2025-08-07 14:42:24] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-07 14:42:28] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -54.316594+0.002348j
[2025-08-07 14:42:33] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -54.236141+0.000477j
[2025-08-07 14:42:37] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -54.354316-0.001504j
[2025-08-07 14:42:41] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -54.280119-0.000982j
[2025-08-07 14:42:45] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -54.182187+0.000525j
[2025-08-07 14:42:49] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -54.221697+0.005893j
[2025-08-07 14:42:53] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -54.258846+0.000817j
[2025-08-07 14:42:57] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -54.195159-0.004058j
[2025-08-07 14:43:02] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -54.125961-0.003822j
[2025-08-07 14:43:06] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -54.148490+0.000278j
[2025-08-07 14:43:10] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -54.109045-0.002401j
[2025-08-07 14:43:14] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -54.142145-0.001084j
[2025-08-07 14:43:18] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -54.211459+0.002463j
[2025-08-07 14:43:22] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -54.056888-0.000488j
[2025-08-07 14:43:26] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -54.085508-0.004198j
[2025-08-07 14:43:31] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -54.113148+0.000670j
[2025-08-07 14:43:35] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -54.077972+0.000989j
[2025-08-07 14:43:39] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -54.261320+0.002054j
[2025-08-07 14:43:43] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -54.130295-0.002327j
[2025-08-07 14:43:47] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -54.156808+0.000168j
[2025-08-07 14:43:51] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -54.136940-0.000938j
[2025-08-07 14:43:55] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -54.212874+0.000533j
[2025-08-07 14:44:00] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -54.270411+0.003212j
[2025-08-07 14:44:04] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -54.187310-0.001750j
[2025-08-07 14:44:08] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -54.171996-0.001572j
[2025-08-07 14:44:12] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -54.083099+0.003325j
[2025-08-07 14:44:16] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -54.166532-0.001179j
[2025-08-07 14:44:20] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -54.088198+0.002832j
[2025-08-07 14:44:25] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -54.182508+0.002814j
[2025-08-07 14:44:29] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -54.159278-0.003011j
[2025-08-07 14:44:33] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -54.201318-0.003502j
[2025-08-07 14:44:37] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -54.057316+0.000006j
[2025-08-07 14:44:41] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -54.114816-0.001361j
[2025-08-07 14:44:45] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -54.229024-0.003874j
[2025-08-07 14:44:50] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -54.237409-0.002923j
[2025-08-07 14:44:54] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -54.193371-0.002355j
[2025-08-07 14:44:58] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -54.106431+0.000893j
[2025-08-07 14:45:02] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -54.175911+0.001681j
[2025-08-07 14:45:06] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -54.192390-0.000438j
[2025-08-07 14:45:10] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -54.289514+0.004264j
[2025-08-07 14:45:15] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -54.331240+0.005188j
[2025-08-07 14:45:19] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -54.201049-0.000636j
[2025-08-07 14:45:23] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -54.145129-0.003033j
[2025-08-07 14:45:27] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -54.121870+0.008906j
[2025-08-07 14:45:31] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -54.158410-0.000813j
[2025-08-07 14:45:35] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -54.200723-0.007397j
[2025-08-07 14:45:39] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -54.167456-0.000494j
[2025-08-07 14:45:44] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -54.238544+0.000145j
[2025-08-07 14:45:48] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -54.178696+0.001607j
[2025-08-07 14:45:52] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -54.154244+0.003299j
[2025-08-07 14:45:56] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -54.242661+0.000325j
[2025-08-07 14:46:00] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -54.299887+0.000226j
[2025-08-07 14:46:04] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -54.332152-0.004812j
[2025-08-07 14:46:09] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -54.442403+0.001420j
[2025-08-07 14:46:13] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -54.285814+0.003587j
[2025-08-07 14:46:17] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -54.302124-0.003371j
[2025-08-07 14:46:21] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -54.265594+0.003586j
[2025-08-07 14:46:25] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -54.226442-0.003771j
[2025-08-07 14:46:29] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -54.276227-0.006111j
[2025-08-07 14:46:34] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -54.168383-0.000469j
[2025-08-07 14:46:38] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -54.185821+0.001723j
[2025-08-07 14:46:42] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -54.213592-0.005088j
[2025-08-07 14:46:46] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -54.212530-0.001263j
[2025-08-07 14:46:50] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -54.222639-0.003479j
[2025-08-07 14:46:54] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -54.243941-0.001761j
[2025-08-07 14:46:58] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -54.200457-0.001987j
[2025-08-07 14:47:03] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -54.147158+0.001918j
[2025-08-07 14:47:07] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -54.111292-0.000086j
[2025-08-07 14:47:11] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -54.100199+0.001821j
[2025-08-07 14:47:15] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -54.131807+0.005739j
[2025-08-07 14:47:19] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -54.279332-0.000092j
[2025-08-07 14:47:23] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -54.404485+0.001869j
[2025-08-07 14:47:27] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -54.223785+0.000351j
[2025-08-07 14:47:32] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -54.173147+0.002751j
[2025-08-07 14:47:36] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -54.225752-0.000002j
[2025-08-07 14:47:40] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -54.179196-0.007259j
[2025-08-07 14:47:44] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -54.077979-0.004423j
[2025-08-07 14:47:48] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -54.129730+0.003365j
[2025-08-07 14:47:52] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -54.169167-0.000753j
[2025-08-07 14:47:56] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -54.211018-0.000039j
[2025-08-07 14:48:01] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -54.273053-0.001476j
[2025-08-07 14:48:05] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -54.225839+0.001712j
[2025-08-07 14:48:09] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -54.191936+0.003403j
[2025-08-07 14:48:13] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -54.263262-0.001304j
[2025-08-07 14:48:17] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -54.288761-0.001765j
[2025-08-07 14:48:21] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -54.243805-0.001083j
[2025-08-07 14:48:25] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -54.342418-0.003351j
[2025-08-07 14:48:30] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -54.212305-0.000063j
[2025-08-07 14:48:34] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -54.275033-0.000195j
[2025-08-07 14:48:38] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -54.218447+0.000432j
[2025-08-07 14:48:42] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -54.171588+0.006279j
[2025-08-07 14:48:46] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -54.212409+0.003464j
[2025-08-07 14:48:50] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -54.164192+0.001921j
[2025-08-07 14:48:55] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -54.327436+0.004312j
[2025-08-07 14:48:59] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -54.223307-0.002527j
[2025-08-07 14:49:03] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -54.328225+0.000793j
[2025-08-07 14:49:07] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -54.197992+0.002702j
[2025-08-07 14:49:11] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -54.202514+0.001613j
[2025-08-07 14:49:15] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -54.169543-0.001511j
[2025-08-07 14:49:19] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -54.215766+0.001335j
[2025-08-07 14:49:19] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-07 14:49:24] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -54.274325+0.003725j
[2025-08-07 14:49:28] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -54.286999+0.001629j
[2025-08-07 14:49:32] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -54.275357-0.000674j
[2025-08-07 14:50:41] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -54.220899+0.005005j
[2025-08-07 14:50:45] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -54.230958-0.003096j
[2025-08-07 14:50:49] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -54.202056+0.001733j
[2025-08-07 14:50:54] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -54.237016-0.007424j
[2025-08-07 14:50:58] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -54.237798-0.004159j
[2025-08-07 14:51:02] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -54.295748-0.001380j
[2025-08-07 14:51:06] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -54.171278+0.000613j
[2025-08-07 14:51:10] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -54.184678-0.000586j
[2025-08-07 14:51:14] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -54.240921+0.006113j
[2025-08-07 14:51:18] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -54.140779-0.000296j
[2025-08-07 14:51:23] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -54.066865-0.001862j
[2025-08-07 14:51:27] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -54.261360+0.000829j
[2025-08-07 14:51:31] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -54.293618+0.001558j
[2025-08-07 14:51:35] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -54.231657-0.002693j
[2025-08-07 14:51:39] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -54.221966-0.003286j
[2025-08-07 14:51:43] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -54.355875-0.005596j
[2025-08-07 14:51:48] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -54.317905+0.001320j
[2025-08-07 14:51:52] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -54.218675+0.000070j
[2025-08-07 14:51:56] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -54.265900+0.002203j
[2025-08-07 14:52:00] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -54.296317-0.000392j
[2025-08-07 14:52:04] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -54.342331-0.000769j
[2025-08-07 14:52:08] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -54.238727-0.004278j
[2025-08-07 14:52:12] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -54.267926-0.006382j
[2025-08-07 14:52:17] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -54.350628-0.003606j
[2025-08-07 14:52:21] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -54.209120-0.000684j
[2025-08-07 14:52:25] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -54.245060+0.004221j
[2025-08-07 14:52:29] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -54.285876+0.001329j
[2025-08-07 14:52:33] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -54.358316+0.002503j
[2025-08-07 14:52:37] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -54.202057-0.000770j
[2025-08-07 14:52:42] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -54.203616+0.003775j
[2025-08-07 14:52:46] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -54.132506-0.001491j
[2025-08-07 14:52:50] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -54.223974-0.002770j
[2025-08-07 14:52:54] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -54.216730-0.002840j
[2025-08-07 14:52:58] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -54.166033-0.003833j
[2025-08-07 14:53:02] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -54.093831-0.001644j
[2025-08-07 14:53:06] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -54.177098-0.001664j
[2025-08-07 14:53:11] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -54.198915-0.000542j
[2025-08-07 14:53:15] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -54.287955+0.003503j
[2025-08-07 14:53:19] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -54.223438-0.004647j
[2025-08-07 14:53:23] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -54.206943+0.000226j
[2025-08-07 14:53:27] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -54.172861-0.000244j
[2025-08-07 14:53:31] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -54.195288+0.004571j
[2025-08-07 14:53:36] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -54.233906+0.002644j
[2025-08-07 14:53:40] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -54.327733+0.001432j
[2025-08-07 14:53:44] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -54.302009+0.003548j
[2025-08-07 14:53:48] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -54.313138-0.003638j
[2025-08-07 14:53:52] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -54.341629+0.006359j
[2025-08-07 14:53:56] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -54.283352-0.000485j
[2025-08-07 14:54:00] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -54.217087+0.002084j
[2025-08-07 14:54:05] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -54.228109-0.001931j
[2025-08-07 14:54:09] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -54.296232+0.003877j
[2025-08-07 14:54:13] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -54.364679+0.000627j
[2025-08-07 14:54:17] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -54.219866+0.003221j
[2025-08-07 14:54:21] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -54.225548+0.002810j
[2025-08-07 14:54:25] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -54.132196+0.005141j
[2025-08-07 14:54:30] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -54.067558-0.000373j
[2025-08-07 14:54:34] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -54.111082-0.007295j
[2025-08-07 14:54:38] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -54.110628-0.000742j
[2025-08-07 14:54:42] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -54.155513+0.003228j
[2025-08-07 14:54:46] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -54.165212-0.010040j
[2025-08-07 14:54:50] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -54.151188-0.001747j
[2025-08-07 14:54:54] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -54.106981-0.002154j
[2025-08-07 14:54:59] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -54.174543+0.002624j
[2025-08-07 14:55:03] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -54.154213-0.004297j
[2025-08-07 14:55:07] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -54.135616-0.006120j
[2025-08-07 14:55:11] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -54.107241-0.001726j
[2025-08-07 14:55:15] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -54.187715-0.001648j
[2025-08-07 14:55:19] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -54.152235+0.001672j
[2025-08-07 14:55:23] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -54.143720-0.000385j
[2025-08-07 14:55:28] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -54.130711+0.001684j
[2025-08-07 14:55:32] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -54.207581-0.003296j
[2025-08-07 14:55:36] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -54.201978-0.000315j
[2025-08-07 14:55:40] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -54.294696+0.003899j
[2025-08-07 14:55:44] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -54.217570-0.000370j
[2025-08-07 14:55:48] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -54.209419+0.001789j
[2025-08-07 14:55:53] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -54.303105-0.001616j
[2025-08-07 14:55:57] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -54.245711-0.001954j
[2025-08-07 14:56:01] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -54.129149-0.001874j
[2025-08-07 14:56:05] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -54.137788+0.000797j
[2025-08-07 14:56:09] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -54.111562-0.001320j
[2025-08-07 14:56:13] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -54.092493+0.000853j
[2025-08-07 14:56:17] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -54.239978+0.001482j
[2025-08-07 14:56:22] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -54.221593+0.001727j
[2025-08-07 14:56:26] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -54.172499+0.003133j
[2025-08-07 14:56:30] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -54.205786+0.001925j
[2025-08-07 14:56:34] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -54.145777+0.001997j
[2025-08-07 14:56:38] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -54.226073-0.003165j
[2025-08-07 14:56:42] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -54.380507+0.001985j
[2025-08-07 14:56:46] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -54.331764+0.004250j
[2025-08-07 14:56:51] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -54.261867-0.002411j
[2025-08-07 14:56:55] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -54.260126+0.005016j
[2025-08-07 14:56:59] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -54.181788+0.002185j
[2025-08-07 14:57:03] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -54.100080+0.004608j
[2025-08-07 14:57:07] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -54.122333+0.000999j
[2025-08-07 14:57:11] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -54.164081+0.001330j
[2025-08-07 14:57:15] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -54.124237-0.002596j
[2025-08-07 14:57:20] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -54.167131-0.000918j
[2025-08-07 14:57:20] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-07 14:57:24] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -54.122423+0.004401j
[2025-08-07 14:57:28] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -54.087982+0.003666j
[2025-08-07 14:57:32] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -54.241973-0.001229j
[2025-08-07 14:57:36] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -54.170215+0.000499j
[2025-08-07 14:57:40] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -54.152567+0.000721j
[2025-08-07 14:57:45] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -54.161113-0.004323j
[2025-08-07 14:57:49] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -54.104317-0.006622j
[2025-08-07 14:57:53] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -54.249799+0.002573j
[2025-08-07 14:57:57] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -54.156427-0.005347j
[2025-08-07 14:58:01] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -54.182509+0.000101j
[2025-08-07 14:58:05] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -54.127805-0.001290j
[2025-08-07 14:58:09] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -54.138923-0.001939j
[2025-08-07 14:58:14] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -54.236049-0.004571j
[2025-08-07 14:58:18] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -54.190217+0.000630j
[2025-08-07 14:58:22] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -54.194906+0.001947j
[2025-08-07 14:58:26] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -54.183536+0.005448j
[2025-08-07 14:58:30] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -54.230289+0.000032j
[2025-08-07 14:58:34] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -54.049876+0.004900j
[2025-08-07 14:58:39] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -54.218553-0.002931j
[2025-08-07 14:58:43] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -54.154802+0.003735j
[2025-08-07 14:58:47] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -54.142976+0.004201j
[2025-08-07 14:58:51] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -54.134630-0.001247j
[2025-08-07 14:58:55] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -54.151870+0.001201j
[2025-08-07 14:58:59] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -54.121124+0.000821j
[2025-08-07 14:59:04] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -54.174633-0.000875j
[2025-08-07 14:59:08] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -54.283201-0.000439j
[2025-08-07 14:59:12] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -54.239507+0.002493j
[2025-08-07 14:59:16] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -54.150223+0.006797j
[2025-08-07 14:59:20] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -54.254892-0.003372j
[2025-08-07 14:59:24] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -54.129420-0.003623j
[2025-08-07 14:59:28] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -54.234243-0.000776j
[2025-08-07 14:59:33] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -54.220565-0.000880j
[2025-08-07 14:59:37] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -54.221542+0.002315j
[2025-08-07 14:59:41] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -54.288477+0.000114j
[2025-08-07 14:59:45] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -54.257963+0.001847j
[2025-08-07 14:59:49] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -54.314602-0.003138j
[2025-08-07 14:59:53] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -54.260790+0.000302j
[2025-08-07 14:59:57] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -54.237684-0.001524j
[2025-08-07 15:00:02] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -54.253806-0.000852j
[2025-08-07 15:00:06] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -54.303224+0.003113j
[2025-08-07 15:00:10] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -54.351419-0.004207j
[2025-08-07 15:00:14] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -54.235929-0.000228j
[2025-08-07 15:00:18] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -54.375361-0.001680j
[2025-08-07 15:00:22] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -54.271475+0.000284j
[2025-08-07 15:00:26] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -54.272487+0.002327j
[2025-08-07 15:00:31] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -54.325198+0.001406j
[2025-08-07 15:00:35] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -54.292300+0.000426j
[2025-08-07 15:00:39] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -54.282956-0.003429j
[2025-08-07 15:00:43] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -54.227054-0.006706j
[2025-08-07 15:00:47] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -54.200114-0.001771j
[2025-08-07 15:00:47] ✅ Training completed | Restarts: 2
[2025-08-07 15:00:47] ============================================================
[2025-08-07 15:00:47] Training completed | Runtime: 4468.2s
[2025-08-07 15:01:01] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-07 15:01:01] ============================================================
