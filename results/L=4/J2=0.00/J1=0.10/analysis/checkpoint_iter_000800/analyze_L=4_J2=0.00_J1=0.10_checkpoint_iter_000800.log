[2025-08-23 14:48:57] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.10/training/checkpoints/checkpoint_iter_000800.pkl
[2025-08-23 14:49:07] ✓ 从checkpoint加载参数: 800
[2025-08-23 14:49:07]   - 能量: -56.934478+0.000349j ± 0.082522
[2025-08-23 14:49:07] ================================================================================
[2025-08-23 14:49:07] 加载量子态: L=4, J2=0.00, J1=0.10, checkpoint=checkpoint_iter_000800
[2025-08-23 14:49:07] 设置样本数为: 1048576
[2025-08-23 14:49:07] 开始生成共享样本集...
[2025-08-23 14:50:30] 样本生成完成,耗时: 82.793 秒
[2025-08-23 14:50:30] ================================================================================
[2025-08-23 14:50:30] 开始计算自旋结构因子...
[2025-08-23 14:50:30] 初始化操作符缓存...
[2025-08-23 14:50:30] 预构建所有自旋相关操作符...
[2025-08-23 14:50:30] 开始计算自旋相关函数...
[2025-08-23 14:50:38] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.690s
[2025-08-23 14:50:47] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.943s
[2025-08-23 14:50:51] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.252s
[2025-08-23 14:50:56] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.273s
[2025-08-23 14:51:00] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.284s
[2025-08-23 14:51:04] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.271s
[2025-08-23 14:51:08] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.250s
[2025-08-23 14:51:13] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.286s
[2025-08-23 14:51:17] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.251s
[2025-08-23 14:51:21] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.287s
[2025-08-23 14:51:25] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.256s
[2025-08-23 14:51:30] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.287s
[2025-08-23 14:51:34] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.253s
[2025-08-23 14:51:38] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.271s
[2025-08-23 14:51:43] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.285s
[2025-08-23 14:51:47] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.254s
[2025-08-23 14:51:51] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.273s
[2025-08-23 14:51:55] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.286s
[2025-08-23 14:52:00] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.253s
[2025-08-23 14:52:04] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.287s
[2025-08-23 14:52:08] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.271s
[2025-08-23 14:52:12] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.286s
[2025-08-23 14:52:17] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.253s
[2025-08-23 14:52:21] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.271s
[2025-08-23 14:52:25] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.255s
[2025-08-23 14:52:30] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.272s
[2025-08-23 14:52:34] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.253s
[2025-08-23 14:52:38] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.287s
[2025-08-23 14:52:42] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.256s
[2025-08-23 14:52:47] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.252s
[2025-08-23 14:52:51] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.254s
[2025-08-23 14:52:55] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.272s
[2025-08-23 14:52:59] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.264s
[2025-08-23 14:53:04] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.269s
[2025-08-23 14:53:08] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.254s
[2025-08-23 14:53:12] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.271s
[2025-08-23 14:53:16] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.254s
[2025-08-23 14:53:21] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.271s
[2025-08-23 14:53:25] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.253s
[2025-08-23 14:53:29] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.286s
[2025-08-23 14:53:34] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.253s
[2025-08-23 14:53:38] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.255s
[2025-08-23 14:53:42] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.254s
[2025-08-23 14:53:46] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.272s
[2025-08-23 14:53:51] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.253s
[2025-08-23 14:53:55] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.253s
[2025-08-23 14:53:59] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.286s
[2025-08-23 14:54:03] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.253s
[2025-08-23 14:54:08] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.285s
[2025-08-23 14:54:12] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.286s
[2025-08-23 14:54:16] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.252s
[2025-08-23 14:54:21] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.286s
[2025-08-23 14:54:25] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.254s
[2025-08-23 14:54:29] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.272s
[2025-08-23 14:54:33] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.251s
[2025-08-23 14:54:38] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.287s
[2025-08-23 14:54:42] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.253s
[2025-08-23 14:54:46] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.251s
[2025-08-23 14:54:50] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.252s
[2025-08-23 14:54:55] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.286s
[2025-08-23 14:54:59] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.255s
[2025-08-23 14:55:03] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.252s
[2025-08-23 14:55:08] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.286s
[2025-08-23 14:55:12] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.254s
[2025-08-23 14:55:12] 自旋相关函数计算完成,总耗时 281.44 秒
[2025-08-23 14:55:12] 计算傅里叶变换...
[2025-08-23 14:55:13] 自旋结构因子计算完成
[2025-08-23 14:55:14] 自旋相关函数平均误差: 0.000645
