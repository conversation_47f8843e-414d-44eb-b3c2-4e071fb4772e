[2025-08-23 15:08:18] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.10/training/checkpoints/final_GCNN.pkl
[2025-08-23 15:08:29] ✓ 从checkpoint加载参数: final
[2025-08-23 15:08:29]   - 能量: -56.823404+0.000366j ± 0.083657
[2025-08-23 15:08:29] ================================================================================
[2025-08-23 15:08:29] 加载量子态: L=4, J2=0.00, J1=0.10, checkpoint=final_GCNN
[2025-08-23 15:08:29] 设置样本数为: 1048576
[2025-08-23 15:08:29] 开始生成共享样本集...
[2025-08-23 15:09:52] 样本生成完成,耗时: 82.713 秒
[2025-08-23 15:09:52] ================================================================================
[2025-08-23 15:09:52] 开始计算自旋结构因子...
[2025-08-23 15:09:52] 初始化操作符缓存...
[2025-08-23 15:09:52] 预构建所有自旋相关操作符...
[2025-08-23 15:09:52] 开始计算自旋相关函数...
[2025-08-23 15:09:59] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.620s
[2025-08-23 15:10:08] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.985s
[2025-08-23 15:10:12] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.250s
[2025-08-23 15:10:17] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.273s
[2025-08-23 15:10:22] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.282s
[2025-08-23 15:10:26] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.270s
[2025-08-23 15:10:30] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.251s
[2025-08-23 15:10:35] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.281s
[2025-08-23 15:10:39] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.250s
[2025-08-23 15:10:43] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.283s
[2025-08-23 15:10:47] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.256s
[2025-08-23 15:10:52] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.282s
[2025-08-23 15:10:56] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.250s
[2025-08-23 15:11:00] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.272s
[2025-08-23 15:11:04] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.282s
[2025-08-23 15:11:09] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.251s
[2025-08-23 15:11:13] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.270s
[2025-08-23 15:11:17] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.282s
[2025-08-23 15:11:22] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.250s
[2025-08-23 15:11:26] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.283s
[2025-08-23 15:11:30] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.271s
[2025-08-23 15:11:34] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.281s
[2025-08-23 15:11:39] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.250s
[2025-08-23 15:11:43] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.271s
[2025-08-23 15:11:47] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.252s
[2025-08-23 15:11:54] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.271s
[2025-08-23 15:11:58] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.250s
[2025-08-23 15:12:03] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.283s
[2025-08-23 15:12:07] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.254s
[2025-08-23 15:12:11] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.250s
[2025-08-23 15:12:15] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.250s
[2025-08-23 15:12:20] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.269s
[2025-08-23 15:12:24] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.265s
[2025-08-23 15:12:28] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.268s
[2025-08-23 15:12:32] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.253s
[2025-08-23 15:12:37] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.268s
[2025-08-23 15:12:41] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.252s
[2025-08-23 15:12:45] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.268s
[2025-08-23 15:12:50] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.250s
[2025-08-23 15:12:54] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.283s
[2025-08-23 15:12:58] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.250s
[2025-08-23 15:13:02] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.253s
[2025-08-23 15:13:07] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.250s
[2025-08-23 15:13:11] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.272s
[2025-08-23 15:13:15] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.251s
[2025-08-23 15:13:19] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.251s
[2025-08-23 15:13:24] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.282s
[2025-08-23 15:13:28] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.251s
[2025-08-23 15:13:32] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.282s
[2025-08-23 15:13:37] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.281s
[2025-08-23 15:13:41] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.250s
[2025-08-23 15:13:45] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.281s
[2025-08-23 15:13:49] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.252s
[2025-08-23 15:13:54] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.272s
[2025-08-23 15:13:58] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.249s
[2025-08-23 15:14:02] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.282s
[2025-08-23 15:14:06] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.250s
[2025-08-23 15:14:11] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.249s
[2025-08-23 15:14:15] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.249s
[2025-08-23 15:14:19] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.281s
[2025-08-23 15:14:23] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.250s
[2025-08-23 15:14:28] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.250s
[2025-08-23 15:14:32] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.281s
[2025-08-23 15:14:36] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.251s
[2025-08-23 15:14:36] 自旋相关函数计算完成,总耗时 284.63 秒
[2025-08-23 15:14:36] 计算傅里叶变换...
[2025-08-23 15:14:37] 自旋结构因子计算完成
[2025-08-23 15:14:38] 自旋相关函数平均误差: 0.000647
