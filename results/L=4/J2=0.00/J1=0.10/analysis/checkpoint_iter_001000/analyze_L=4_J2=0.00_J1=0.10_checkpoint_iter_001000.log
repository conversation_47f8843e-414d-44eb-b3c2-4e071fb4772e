[2025-08-23 15:01:52] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.10/training/checkpoints/checkpoint_iter_001000.pkl
[2025-08-23 15:02:02] ✓ 从checkpoint加载参数: 1000
[2025-08-23 15:02:02]   - 能量: -57.054952-0.002419j ± 0.083538
[2025-08-23 15:02:02] ================================================================================
[2025-08-23 15:02:02] 加载量子态: L=4, J2=0.00, J1=0.10, checkpoint=checkpoint_iter_001000
[2025-08-23 15:02:02] 设置样本数为: 1048576
[2025-08-23 15:02:02] 开始生成共享样本集...
[2025-08-23 15:03:25] 样本生成完成,耗时: 82.767 秒
[2025-08-23 15:03:25] ================================================================================
[2025-08-23 15:03:25] 开始计算自旋结构因子...
[2025-08-23 15:03:25] 初始化操作符缓存...
[2025-08-23 15:03:25] 预构建所有自旋相关操作符...
[2025-08-23 15:03:25] 开始计算自旋相关函数...
[2025-08-23 15:03:33] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.673s
[2025-08-23 15:03:42] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.964s
[2025-08-23 15:03:46] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.251s
[2025-08-23 15:03:50] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.272s
[2025-08-23 15:03:55] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.284s
[2025-08-23 15:03:59] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.272s
[2025-08-23 15:04:03] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.250s
[2025-08-23 15:04:08] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.288s
[2025-08-23 15:04:12] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.250s
[2025-08-23 15:04:16] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.288s
[2025-08-23 15:04:20] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.258s
[2025-08-23 15:04:25] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.286s
[2025-08-23 15:04:29] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.253s
[2025-08-23 15:04:33] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.273s
[2025-08-23 15:04:38] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.284s
[2025-08-23 15:04:42] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.256s
[2025-08-23 15:04:46] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.273s
[2025-08-23 15:04:50] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.288s
[2025-08-23 15:04:55] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.255s
[2025-08-23 15:04:59] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.290s
[2025-08-23 15:05:03] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.272s
[2025-08-23 15:05:07] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.288s
[2025-08-23 15:05:12] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.253s
[2025-08-23 15:05:16] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.272s
[2025-08-23 15:05:20] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.257s
[2025-08-23 15:05:25] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.273s
[2025-08-23 15:05:29] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.255s
[2025-08-23 15:05:33] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.290s
[2025-08-23 15:05:37] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.257s
[2025-08-23 15:05:42] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.252s
[2025-08-23 15:05:46] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.253s
[2025-08-23 15:05:50] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.274s
[2025-08-23 15:05:54] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.265s
[2025-08-23 15:05:59] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.269s
[2025-08-23 15:06:03] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.256s
[2025-08-23 15:06:07] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.271s
[2025-08-23 15:06:12] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.254s
[2025-08-23 15:06:16] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.271s
[2025-08-23 15:06:20] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.254s
[2025-08-23 15:06:24] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.288s
[2025-08-23 15:06:29] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.254s
[2025-08-23 15:06:33] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.257s
[2025-08-23 15:06:37] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.253s
[2025-08-23 15:06:41] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.273s
[2025-08-23 15:06:46] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.253s
[2025-08-23 15:06:50] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.252s
[2025-08-23 15:06:54] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.289s
[2025-08-23 15:06:58] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.255s
[2025-08-23 15:07:03] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.288s
[2025-08-23 15:07:07] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.290s
[2025-08-23 15:07:11] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.252s
[2025-08-23 15:07:16] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.288s
[2025-08-23 15:07:20] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.256s
[2025-08-23 15:07:24] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.273s
[2025-08-23 15:07:28] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.252s
[2025-08-23 15:07:33] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.290s
[2025-08-23 15:07:37] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.252s
[2025-08-23 15:07:43] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.252s
[2025-08-23 15:07:48] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.253s
[2025-08-23 15:07:52] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.288s
[2025-08-23 15:07:56] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.256s
[2025-08-23 15:08:01] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.252s
[2025-08-23 15:08:05] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.288s
[2025-08-23 15:08:09] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.256s
[2025-08-23 15:08:09] 自旋相关函数计算完成,总耗时 283.80 秒
[2025-08-23 15:08:09] 计算傅里叶变换...
[2025-08-23 15:08:10] 自旋结构因子计算完成
[2025-08-23 15:08:11] 自旋相关函数平均误差: 0.000644
