[2025-08-06 02:17:06] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.04/training/checkpoints/checkpoint_iter_003500.pkl
[2025-08-06 02:17:14] ✓ 从checkpoint加载参数: 3500
[2025-08-06 02:17:14]   - 能量: -54.605040-0.000448j ± 0.043363
[2025-08-06 02:17:14] ================================================================================
[2025-08-06 02:17:14] 加载量子态: L=4, J2=0.00, J1=0.04, checkpoint=checkpoint_iter_003500
[2025-08-06 02:17:14] 设置样本数为: 1048576
[2025-08-06 02:17:14] 开始生成共享样本集...
[2025-08-06 02:19:47] 样本生成完成,耗时: 152.134 秒
[2025-08-06 02:19:47] ================================================================================
[2025-08-06 02:19:47] 开始计算自旋结构因子...
[2025-08-06 02:19:47] 初始化操作符缓存...
[2025-08-06 02:19:47] 预构建所有自旋相关操作符...
[2025-08-06 02:19:47] 开始计算自旋相关函数...
[2025-08-06 02:19:57] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.530s
[2025-08-06 02:20:11] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.531s
[2025-08-06 02:20:19] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.211s
[2025-08-06 02:20:27] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.170s
[2025-08-06 02:20:35] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.210s
[2025-08-06 02:20:43] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.199s
[2025-08-06 02:20:52] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.171s
[2025-08-06 02:21:00] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.213s
[2025-08-06 02:21:08] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.172s
[2025-08-06 02:21:16] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.170s
[2025-08-06 02:21:24] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.211s
[2025-08-06 02:21:33] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.170s
[2025-08-06 02:21:41] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.208s
[2025-08-06 02:21:49] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.171s
[2025-08-06 02:21:57] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.212s
[2025-08-06 02:22:05] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.172s
[2025-08-06 02:22:14] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.210s
[2025-08-06 02:22:22] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.202s
[2025-08-06 02:22:30] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.172s
[2025-08-06 02:22:38] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.208s
[2025-08-06 02:22:46] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.182s
[2025-08-06 02:22:55] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.172s
[2025-08-06 02:23:03] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.212s
[2025-08-06 02:23:11] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.173s
[2025-08-06 02:23:19] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.195s
[2025-08-06 02:23:27] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.211s
[2025-08-06 02:23:36] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.171s
[2025-08-06 02:23:44] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.213s
[2025-08-06 02:23:52] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.171s
[2025-08-06 02:24:00] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.170s
[2025-08-06 02:24:08] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.210s
[2025-08-06 02:24:16] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.211s
[2025-08-06 02:24:25] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.172s
[2025-08-06 02:24:33] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.172s
[2025-08-06 02:24:41] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.212s
[2025-08-06 02:24:49] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.171s
[2025-08-06 02:24:57] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.211s
[2025-08-06 02:25:06] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.211s
[2025-08-06 02:25:14] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.170s
[2025-08-06 02:25:22] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.208s
[2025-08-06 02:25:30] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.170s
[2025-08-06 02:25:38] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.171s
[2025-08-06 02:25:47] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.212s
[2025-08-06 02:25:55] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.172s
[2025-08-06 02:26:03] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.210s
[2025-08-06 02:26:11] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.209s
[2025-08-06 02:26:19] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.171s
[2025-08-06 02:26:28] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.208s
[2025-08-06 02:26:36] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.170s
[2025-08-06 02:26:44] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.172s
[2025-08-06 02:26:52] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.202s
[2025-08-06 02:27:00] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.172s
[2025-08-06 02:27:08] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.172s
[2025-08-06 02:27:17] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.170s
[2025-08-06 02:27:25] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.212s
[2025-08-06 02:27:33] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.170s
[2025-08-06 02:27:41] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.208s
[2025-08-06 02:27:49] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.209s
[2025-08-06 02:27:58] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.173s
[2025-08-06 02:28:06] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.212s
[2025-08-06 02:28:14] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.171s
[2025-08-06 02:28:22] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.171s
[2025-08-06 02:28:30] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.210s
[2025-08-06 02:28:39] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.170s
[2025-08-06 02:28:39] 自旋相关函数计算完成,总耗时 531.91 秒
[2025-08-06 02:28:39] 计算傅里叶变换...
[2025-08-06 02:28:39] 自旋结构因子计算完成
[2025-08-06 02:28:40] 自旋相关函数平均误差: 0.000673
