[2025-08-26 16:45:58] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.81/training/checkpoints/checkpoint_iter_000800.pkl
[2025-08-26 16:46:09] ✓ 从checkpoint加载参数: 800
[2025-08-26 16:46:09]   - 能量: -29.170053-0.002079j ± 0.006917
[2025-08-26 16:46:09] ================================================================================
[2025-08-26 16:46:09] 加载量子态: L=4, J2=1.00, J1=0.81, checkpoint=checkpoint_iter_000800
[2025-08-26 16:46:09] 设置样本数为: 1048576
[2025-08-26 16:46:09] 开始生成共享样本集...
[2025-08-26 16:47:32] 样本生成完成,耗时: 82.642 秒
[2025-08-26 16:47:32] ================================================================================
[2025-08-26 16:47:32] 开始计算自旋结构因子...
[2025-08-26 16:47:32] 初始化操作符缓存...
[2025-08-26 16:47:32] 预构建所有自旋相关操作符...
[2025-08-26 16:47:32] 开始计算自旋相关函数...
[2025-08-26 16:47:39] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.498s
[2025-08-26 16:47:48] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.901s
[2025-08-26 16:47:52] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.242s
[2025-08-26 16:47:57] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.263s
[2025-08-26 16:48:01] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.272s
[2025-08-26 16:48:05] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.261s
[2025-08-26 16:48:10] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.245s
[2025-08-26 16:48:14] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.285s
[2025-08-26 16:48:18] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.240s
[2025-08-26 16:48:22] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.286s
[2025-08-26 16:48:27] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.253s
[2025-08-26 16:48:31] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.271s
[2025-08-26 16:48:35] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.244s
[2025-08-26 16:48:39] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.262s
[2025-08-26 16:48:44] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.270s
[2025-08-26 16:48:48] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.249s
[2025-08-26 16:48:52] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.260s
[2025-08-26 16:48:56] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.284s
[2025-08-26 16:49:01] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.244s
[2025-08-26 16:49:05] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.284s
[2025-08-26 16:49:09] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.262s
[2025-08-26 16:49:14] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.275s
[2025-08-26 16:49:18] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.245s
[2025-08-26 16:49:22] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.261s
[2025-08-26 16:49:26] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.252s
[2025-08-26 16:49:31] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.262s
[2025-08-26 16:49:35] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.244s
[2025-08-26 16:49:39] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.286s
[2025-08-26 16:49:43] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.252s
[2025-08-26 16:49:48] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.243s
[2025-08-26 16:49:52] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.242s
[2025-08-26 16:49:56] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.261s
[2025-08-26 16:50:00] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.260s
[2025-08-26 16:50:05] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.261s
[2025-08-26 16:50:09] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.252s
[2025-08-26 16:50:13] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.262s
[2025-08-26 16:50:17] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.245s
[2025-08-26 16:50:22] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.262s
[2025-08-26 16:50:26] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.244s
[2025-08-26 16:50:30] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.277s
[2025-08-26 16:50:34] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.243s
[2025-08-26 16:50:39] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.252s
[2025-08-26 16:50:43] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.243s
[2025-08-26 16:50:47] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.263s
[2025-08-26 16:50:51] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.243s
[2025-08-26 16:50:56] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.242s
[2025-08-26 16:51:00] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.282s
[2025-08-26 16:51:04] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.243s
[2025-08-26 16:51:09] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.278s
[2025-08-26 16:51:13] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.285s
[2025-08-26 16:51:17] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.241s
[2025-08-26 16:51:21] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.285s
[2025-08-26 16:51:26] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.250s
[2025-08-26 16:51:30] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.262s
[2025-08-26 16:51:34] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.241s
[2025-08-26 16:51:38] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.286s
[2025-08-26 16:51:43] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.242s
[2025-08-26 16:51:47] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.242s
[2025-08-26 16:51:51] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.243s
[2025-08-26 16:51:55] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.285s
[2025-08-26 16:52:00] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.248s
[2025-08-26 16:52:04] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.242s
[2025-08-26 16:52:08] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.283s
[2025-08-26 16:52:13] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.249s
[2025-08-26 16:52:13] 自旋相关函数计算完成,总耗时 280.71 秒
[2025-08-26 16:52:13] 计算傅里叶变换...
[2025-08-26 16:52:13] 自旋结构因子计算完成
[2025-08-26 16:52:14] 自旋相关函数平均误差: 0.000544
