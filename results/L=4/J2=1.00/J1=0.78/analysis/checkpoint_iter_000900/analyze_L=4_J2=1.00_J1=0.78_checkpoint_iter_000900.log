[2025-08-26 13:25:51] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.78/training/checkpoints/checkpoint_iter_000900.pkl
[2025-08-26 13:26:02] ✓ 从checkpoint加载参数: 900
[2025-08-26 13:26:02]   - 能量: -27.938932-0.002996j ± 0.007757
[2025-08-26 13:26:02] ================================================================================
[2025-08-26 13:26:02] 加载量子态: L=4, J2=1.00, J1=0.78, checkpoint=checkpoint_iter_000900
[2025-08-26 13:26:02] 设置样本数为: 1048576
[2025-08-26 13:26:02] 开始生成共享样本集...
[2025-08-26 13:27:24] 样本生成完成,耗时: 82.737 秒
[2025-08-26 13:27:24] ================================================================================
[2025-08-26 13:27:24] 开始计算自旋结构因子...
[2025-08-26 13:27:24] 初始化操作符缓存...
[2025-08-26 13:27:24] 预构建所有自旋相关操作符...
[2025-08-26 13:27:24] 开始计算自旋相关函数...
[2025-08-26 13:27:32] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.614s
[2025-08-26 13:27:41] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.879s
[2025-08-26 13:27:45] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.244s
[2025-08-26 13:27:49] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.262s
[2025-08-26 13:27:54] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.279s
[2025-08-26 13:27:58] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.261s
[2025-08-26 13:28:02] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.242s
[2025-08-26 13:28:06] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.281s
[2025-08-26 13:28:11] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.242s
[2025-08-26 13:28:15] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.283s
[2025-08-26 13:28:19] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.256s
[2025-08-26 13:28:24] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.280s
[2025-08-26 13:28:28] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.242s
[2025-08-26 13:28:32] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.261s
[2025-08-26 13:28:36] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.278s
[2025-08-26 13:28:41] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.247s
[2025-08-26 13:28:45] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.259s
[2025-08-26 13:28:49] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.281s
[2025-08-26 13:28:53] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.243s
[2025-08-26 13:28:58] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.282s
[2025-08-26 13:29:02] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.260s
[2025-08-26 13:29:06] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.280s
[2025-08-26 13:29:10] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.243s
[2025-08-26 13:29:15] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.261s
[2025-08-26 13:29:19] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.250s
[2025-08-26 13:29:23] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.262s
[2025-08-26 13:29:28] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.243s
[2025-08-26 13:29:32] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.283s
[2025-08-26 13:29:36] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.252s
[2025-08-26 13:29:40] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.242s
[2025-08-26 13:29:45] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.243s
[2025-08-26 13:29:49] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.259s
[2025-08-26 13:29:53] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.258s
[2025-08-26 13:29:57] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.260s
[2025-08-26 13:30:02] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.250s
[2025-08-26 13:30:06] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.261s
[2025-08-26 13:30:10] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.244s
[2025-08-26 13:30:14] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.261s
[2025-08-26 13:30:19] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.244s
[2025-08-26 13:30:23] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.280s
[2025-08-26 13:30:27] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.243s
[2025-08-26 13:30:31] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.250s
[2025-08-26 13:30:36] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.243s
[2025-08-26 13:30:40] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.263s
[2025-08-26 13:30:44] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.243s
[2025-08-26 13:30:48] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.243s
[2025-08-26 13:30:53] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.280s
[2025-08-26 13:30:57] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.243s
[2025-08-26 13:31:01] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.279s
[2025-08-26 13:31:05] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.281s
[2025-08-26 13:31:10] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.242s
[2025-08-26 13:31:14] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.282s
[2025-08-26 13:31:18] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.249s
[2025-08-26 13:31:23] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.262s
[2025-08-26 13:31:27] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.242s
[2025-08-26 13:31:31] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.282s
[2025-08-26 13:31:35] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.241s
[2025-08-26 13:31:41] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.242s
[2025-08-26 13:31:48] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.242s
[2025-08-26 13:31:52] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.282s
[2025-08-26 13:31:57] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.248s
[2025-08-26 13:32:02] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.242s
[2025-08-26 13:32:06] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.280s
[2025-08-26 13:32:10] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.247s
[2025-08-26 13:32:11] 自旋相关函数计算完成,总耗时 286.17 秒
[2025-08-26 13:32:11] 计算傅里叶变换...
[2025-08-26 13:32:13] 自旋结构因子计算完成
[2025-08-26 13:32:23] 自旋相关函数平均误差: 0.000558
