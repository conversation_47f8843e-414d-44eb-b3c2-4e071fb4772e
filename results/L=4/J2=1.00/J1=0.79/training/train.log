[2025-08-25 18:24:36] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-08-25 18:24:36]   - 迭代次数: final
[2025-08-25 18:24:36]   - 能量: -28.760945-0.000622j ± 0.003109
[2025-08-25 18:24:36]   - 时间戳: 2025-08-24T12:59:13.349942+08:00
[2025-08-25 18:24:46] ✓ 变分状态参数已从checkpoint恢复
[2025-08-25 18:24:46] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-25 18:24:46] ==================================================
[2025-08-25 18:24:46] GCNN for Shastry-Sutherland Model
[2025-08-25 18:24:46] ==================================================
[2025-08-25 18:24:46] System parameters:
[2025-08-25 18:24:46]   - System size: L=4, N=64
[2025-08-25 18:24:46]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-08-25 18:24:46] --------------------------------------------------
[2025-08-25 18:24:46] Model parameters:
[2025-08-25 18:24:46]   - Number of layers = 4
[2025-08-25 18:24:46]   - Number of features = 4
[2025-08-25 18:24:46]   - Total parameters = 12572
[2025-08-25 18:24:46] --------------------------------------------------
[2025-08-25 18:24:46] Training parameters:
[2025-08-25 18:24:46]   - Learning rate: 0.01
[2025-08-25 18:24:46]   - Total iterations: 1050
[2025-08-25 18:24:46]   - Annealing cycles: 3
[2025-08-25 18:24:46]   - Initial period: 150
[2025-08-25 18:24:46]   - Period multiplier: 2.0
[2025-08-25 18:24:46]   - Temperature range: 0.0-1.0
[2025-08-25 18:24:46]   - Samples: 4096
[2025-08-25 18:24:46]   - Discarded samples: 0
[2025-08-25 18:24:46]   - Chunk size: 2048
[2025-08-25 18:24:46]   - Diagonal shift: 0.2
[2025-08-25 18:24:46]   - Gradient clipping: 1.0
[2025-08-25 18:24:46]   - Checkpoint enabled: interval=100
[2025-08-25 18:24:46]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.79/training/checkpoints
[2025-08-25 18:24:46] --------------------------------------------------
[2025-08-25 18:24:46] Device status:
[2025-08-25 18:24:46]   - Devices model: NVIDIA H200 NVL
[2025-08-25 18:24:46]   - Number of devices: 1
[2025-08-25 18:24:46]   - Sharding: True
[2025-08-25 18:24:46] ============================================================
[2025-08-25 18:25:25] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -28.339626+0.000924j
[2025-08-25 18:25:48] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -28.344296-0.003802j
[2025-08-25 18:25:53] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -28.345864-0.002338j
[2025-08-25 18:25:59] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -28.354239-0.001436j
[2025-08-25 18:26:05] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -28.345598+0.001396j
[2025-08-25 18:26:11] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -28.349933-0.002498j
[2025-08-25 18:26:17] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -28.339165+0.001043j
[2025-08-25 18:26:23] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -28.349317+0.002375j
[2025-08-25 18:26:28] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -28.351751-0.002920j
[2025-08-25 18:26:34] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -28.345118-0.001818j
[2025-08-25 18:26:40] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -28.355297+0.000732j
[2025-08-25 18:26:46] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -28.343147+0.000771j
[2025-08-25 18:26:52] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -28.346959-0.003097j
[2025-08-25 18:26:58] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -28.348234+0.000668j
[2025-08-25 18:27:03] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -28.352618+0.001801j
[2025-08-25 18:27:09] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -28.348271-0.001476j
[2025-08-25 18:27:15] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -28.332888-0.002638j
[2025-08-25 18:27:21] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -28.352093-0.002581j
[2025-08-25 18:27:27] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -28.350106-0.002613j
[2025-08-25 18:27:33] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -28.351389+0.001348j
[2025-08-25 18:27:38] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -28.349532-0.000422j
[2025-08-25 18:27:44] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -28.345631+0.002659j
[2025-08-25 18:27:50] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -28.342171+0.001102j
[2025-08-25 18:27:56] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -28.346111-0.001507j
[2025-08-25 18:28:02] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -28.350586+0.001327j
[2025-08-25 18:28:08] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -28.349263+0.000484j
[2025-08-25 18:28:13] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -28.345995-0.002986j
[2025-08-25 18:28:19] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -28.345023-0.001891j
[2025-08-25 18:28:25] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -28.348557+0.000718j
[2025-08-25 18:28:31] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -28.353763-0.001207j
[2025-08-25 18:28:37] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -28.349994-0.000777j
[2025-08-25 18:28:43] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -28.346731+0.002729j
[2025-08-25 18:28:48] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -28.348221-0.001617j
[2025-08-25 18:28:54] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -28.342482-0.000220j
[2025-08-25 18:29:00] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -28.348918+0.002849j
[2025-08-25 18:29:06] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -28.347884+0.002139j
[2025-08-25 18:29:12] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -28.334903-0.002181j
[2025-08-25 18:29:18] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -28.342420-0.002615j
[2025-08-25 18:29:23] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -28.348579+0.000833j
[2025-08-25 18:29:29] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -28.356365+0.000438j
[2025-08-25 18:29:35] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -28.349423+0.006359j
[2025-08-25 18:29:41] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -28.348929-0.001639j
[2025-08-25 18:29:47] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -28.342463+0.009258j
[2025-08-25 18:29:53] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -28.352253+0.002026j
[2025-08-25 18:29:58] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -28.345018+0.000604j
[2025-08-25 18:30:04] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -28.351024+0.003274j
[2025-08-25 18:30:10] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -28.343811-0.000458j
[2025-08-25 18:30:16] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -28.350634+0.003948j
[2025-08-25 18:30:22] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -28.344233+0.002088j
[2025-08-25 18:30:27] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -28.357822-0.002175j
[2025-08-25 18:30:33] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -28.348224+0.002246j
[2025-08-25 18:30:39] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -28.357600+0.000063j
[2025-08-25 18:30:45] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -28.351417-0.000709j
[2025-08-25 18:30:51] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -28.340668-0.000147j
[2025-08-25 18:30:57] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -28.337557+0.000106j
[2025-08-25 18:31:02] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -28.334868-0.000404j
[2025-08-25 18:31:08] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -28.349334+0.002847j
[2025-08-25 18:31:14] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -28.341424-0.000271j
[2025-08-25 18:31:20] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -28.352473-0.003973j
[2025-08-25 18:31:26] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -28.357679-0.002268j
[2025-08-25 18:31:32] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -28.345019+0.000239j
[2025-08-25 18:31:37] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -28.346761-0.000531j
[2025-08-25 18:31:43] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -28.360087+0.004032j
[2025-08-25 18:31:49] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -28.338456-0.001518j
[2025-08-25 18:31:55] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -28.355311-0.000064j
[2025-08-25 18:32:01] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -28.347386-0.000559j
[2025-08-25 18:32:07] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -28.336877+0.003341j
[2025-08-25 18:32:13] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -28.354366-0.001376j
[2025-08-25 18:32:18] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -28.337822+0.000125j
[2025-08-25 18:32:24] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -28.337847-0.000466j
[2025-08-25 18:32:30] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -28.342457-0.000333j
[2025-08-25 18:32:36] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -28.353230-0.002710j
[2025-08-25 18:32:42] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -28.349121+0.003626j
[2025-08-25 18:32:48] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -28.348669-0.000434j
[2025-08-25 18:32:54] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -28.343477-0.002092j
[2025-08-25 18:32:59] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -28.353716+0.000853j
[2025-08-25 18:33:05] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -28.343627+0.000968j
[2025-08-25 18:33:11] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -28.355676-0.002484j
[2025-08-25 18:33:17] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -28.348356+0.002370j
[2025-08-25 18:33:23] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -28.359496-0.002601j
[2025-08-25 18:33:29] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -28.346764-0.001325j
[2025-08-25 18:33:35] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -28.343471-0.000378j
[2025-08-25 18:33:40] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -28.350790-0.005494j
[2025-08-25 18:33:46] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -28.351849+0.003035j
[2025-08-25 18:33:52] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -28.363545+0.000406j
[2025-08-25 18:33:58] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -28.368451+0.000283j
[2025-08-25 18:34:04] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -28.336541+0.000624j
[2025-08-25 18:34:10] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -28.342627-0.000611j
[2025-08-25 18:34:16] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -28.338258+0.000021j
[2025-08-25 18:34:21] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -28.355118-0.003183j
[2025-08-25 18:34:27] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -28.344729-0.001695j
[2025-08-25 18:34:33] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -28.357499-0.000758j
[2025-08-25 18:34:39] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -28.344248-0.001118j
[2025-08-25 18:34:45] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -28.344692-0.002400j
[2025-08-25 18:34:51] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -28.339418-0.000229j
[2025-08-25 18:34:56] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -28.349617+0.002775j
[2025-08-25 18:35:02] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -28.344971-0.000764j
[2025-08-25 18:35:08] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -28.341789-0.000725j
[2025-08-25 18:35:14] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -28.351546-0.002414j
[2025-08-25 18:35:20] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -28.348370-0.007906j
[2025-08-25 18:35:20] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-25 18:35:26] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -28.344170+0.001175j
[2025-08-25 18:35:32] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -28.350668+0.000816j
[2025-08-25 18:35:37] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -28.337351-0.002964j
[2025-08-25 18:35:43] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -28.358286+0.001471j
[2025-08-25 18:35:49] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -28.355343+0.000155j
[2025-08-25 18:35:55] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -28.360116+0.000456j
[2025-08-25 18:36:01] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -28.343007-0.001065j
[2025-08-25 18:36:07] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -28.356433-0.000631j
[2025-08-25 18:36:12] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -28.350729+0.002578j
[2025-08-25 18:36:18] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -28.345435+0.001987j
[2025-08-25 18:36:24] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -28.340460+0.001629j
[2025-08-25 18:36:30] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -28.351632+0.000605j
[2025-08-25 18:36:36] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -28.349266-0.002659j
[2025-08-25 18:36:42] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -28.353820+0.002381j
[2025-08-25 18:36:48] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -28.339963+0.001400j
[2025-08-25 18:36:53] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -28.334913+0.003236j
[2025-08-25 18:36:59] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -28.353378-0.001577j
[2025-08-25 18:37:05] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -28.346209-0.002251j
[2025-08-25 18:37:11] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -28.349097+0.000599j
[2025-08-25 18:37:17] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -28.350303+0.002438j
[2025-08-25 18:37:23] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -28.336415+0.000677j
[2025-08-25 18:37:29] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -28.359180+0.000533j
[2025-08-25 18:37:34] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -28.338629-0.002780j
[2025-08-25 18:37:40] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -28.351671-0.004269j
[2025-08-25 18:37:46] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -28.339983+0.005650j
[2025-08-25 18:37:52] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -28.344076+0.002543j
[2025-08-25 18:37:58] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -28.347210-0.001422j
[2025-08-25 18:38:04] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -28.345301+0.000277j
[2025-08-25 18:38:09] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -28.337928-0.000494j
[2025-08-25 18:38:15] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -28.347868+0.001580j
[2025-08-25 18:38:21] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -28.343287-0.002695j
[2025-08-25 18:38:27] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -28.349353+0.003091j
[2025-08-25 18:38:33] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -28.348503-0.004270j
[2025-08-25 18:38:39] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -28.344676-0.004683j
[2025-08-25 18:38:45] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -28.349798-0.001486j
[2025-08-25 18:38:50] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -28.352576-0.003798j
[2025-08-25 18:38:56] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -28.353378+0.001845j
[2025-08-25 18:39:02] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -28.359247+0.004441j
[2025-08-25 18:39:08] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -28.353446+0.001885j
[2025-08-25 18:39:14] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -28.340364+0.001567j
[2025-08-25 18:39:20] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -28.348876+0.001001j
[2025-08-25 18:39:25] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -28.345408-0.004627j
[2025-08-25 18:39:31] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -28.352158+0.001334j
[2025-08-25 18:39:37] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -28.351207-0.000452j
[2025-08-25 18:39:43] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -28.350457-0.000750j
[2025-08-25 18:39:49] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -28.350272+0.002513j
[2025-08-25 18:39:55] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -28.352630+0.000491j
[2025-08-25 18:40:01] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -28.352384+0.000132j
[2025-08-25 18:40:06] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -28.354081+0.006401j
[2025-08-25 18:40:12] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -28.359102-0.001754j
[2025-08-25 18:40:12] RESTART #1 | Period: 300
[2025-08-25 18:40:18] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -28.342223+0.004919j
[2025-08-25 18:40:24] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -28.357088+0.000336j
[2025-08-25 18:40:30] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -28.341613-0.001298j
[2025-08-25 18:40:36] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -28.342823+0.000728j
[2025-08-25 18:40:42] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -28.341577-0.000103j
[2025-08-25 18:40:48] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -28.345578-0.002886j
[2025-08-25 18:40:53] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -28.354170-0.005287j
[2025-08-25 18:40:59] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -28.345203+0.005349j
[2025-08-25 18:41:05] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -28.347505-0.003780j
[2025-08-25 18:41:11] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -28.348150+0.000254j
[2025-08-25 18:41:17] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -28.345252+0.000517j
[2025-08-25 18:41:23] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -28.352769+0.000244j
[2025-08-25 18:41:28] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -28.349071+0.000962j
[2025-08-25 18:41:34] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -28.352802-0.002957j
[2025-08-25 18:41:40] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -28.345601+0.001979j
[2025-08-25 18:41:46] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -28.348829+0.000143j
[2025-08-25 18:41:52] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -28.348463-0.001249j
[2025-08-25 18:41:58] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -28.350880+0.001172j
[2025-08-25 18:42:04] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -28.355419+0.000797j
[2025-08-25 18:42:09] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -28.341020+0.001240j
[2025-08-25 18:42:15] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -28.340104-0.001809j
[2025-08-25 18:42:21] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -28.351993+0.004190j
[2025-08-25 18:42:27] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -28.356275-0.004442j
[2025-08-25 18:42:33] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -28.345105-0.000394j
[2025-08-25 18:42:39] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -28.348251-0.001781j
[2025-08-25 18:42:45] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -28.345541+0.003237j
[2025-08-25 18:42:50] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -28.351741-0.003252j
[2025-08-25 18:42:56] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -28.347297+0.000719j
[2025-08-25 18:43:02] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -28.344130+0.003589j
[2025-08-25 18:43:08] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -28.349009+0.002544j
[2025-08-25 18:43:14] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -28.344371+0.001034j
[2025-08-25 18:43:20] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -28.350018+0.001311j
[2025-08-25 18:43:26] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -28.351345+0.000424j
[2025-08-25 18:43:31] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -28.345131-0.000824j
[2025-08-25 18:43:37] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -28.366733+0.001280j
[2025-08-25 18:43:43] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -28.354501+0.004480j
[2025-08-25 18:43:49] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -28.353810+0.002586j
[2025-08-25 18:43:55] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -28.350933+0.000578j
[2025-08-25 18:44:01] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -28.343294+0.001193j
[2025-08-25 18:44:07] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -28.341820+0.008611j
[2025-08-25 18:44:12] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -28.355910+0.001118j
[2025-08-25 18:44:18] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -28.347871-0.000905j
[2025-08-25 18:44:24] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -28.349990+0.000297j
[2025-08-25 18:44:30] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -28.344600+0.004496j
[2025-08-25 18:44:36] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -28.337717+0.000087j
[2025-08-25 18:44:42] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -28.353465+0.000081j
[2025-08-25 18:44:48] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -28.351248-0.000542j
[2025-08-25 18:44:53] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -28.362330-0.005554j
[2025-08-25 18:44:59] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -28.358077-0.003172j
[2025-08-25 18:45:05] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -28.346821-0.002862j
[2025-08-25 18:45:05] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-25 18:45:11] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -28.336331-0.001175j
[2025-08-25 18:45:17] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -28.355519-0.002080j
[2025-08-25 18:45:23] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -28.353982+0.000755j
[2025-08-25 18:45:29] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -28.372207+0.001350j
[2025-08-25 18:45:34] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -28.342699-0.001953j
[2025-08-25 18:45:40] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -28.346774-0.005284j
[2025-08-25 18:45:46] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -28.357977+0.001829j
[2025-08-25 18:45:52] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -28.350376+0.000710j
[2025-08-25 18:45:58] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -28.353993+0.000914j
[2025-08-25 18:46:04] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -28.355782-0.000125j
[2025-08-25 18:46:10] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -28.347653-0.001298j
[2025-08-25 18:46:15] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -28.349432-0.002891j
[2025-08-25 18:46:21] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -28.354958+0.000767j
[2025-08-25 18:46:27] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -28.360282-0.000969j
[2025-08-25 18:46:33] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -28.357827+0.000676j
[2025-08-25 18:46:39] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -28.338754-0.003203j
[2025-08-25 18:46:45] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -28.341903-0.001905j
[2025-08-25 18:46:51] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -28.353996-0.001937j
[2025-08-25 18:46:56] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -28.354506+0.001216j
[2025-08-25 18:47:02] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -28.358160-0.001113j
[2025-08-25 18:47:08] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -28.347225-0.001900j
[2025-08-25 18:47:14] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -28.358195-0.002671j
[2025-08-25 18:47:20] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -28.356284+0.005100j
[2025-08-25 18:47:26] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -28.351858+0.000325j
[2025-08-25 18:47:32] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -28.349766-0.000068j
[2025-08-25 18:47:37] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -28.337309-0.000427j
[2025-08-25 18:47:43] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -28.349014-0.002378j
[2025-08-25 18:47:49] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -28.347318-0.000375j
[2025-08-25 18:47:55] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -28.350277+0.003007j
[2025-08-25 18:48:01] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -28.350827+0.001001j
[2025-08-25 18:48:07] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -28.348919+0.001364j
[2025-08-25 18:48:12] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -28.350906-0.002380j
[2025-08-25 18:48:18] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -28.340698+0.001105j
[2025-08-25 18:48:24] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -28.349072-0.000514j
[2025-08-25 18:48:30] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -28.344420-0.003729j
[2025-08-25 18:48:36] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -28.347999+0.001461j
[2025-08-25 18:48:42] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -28.360722-0.000936j
[2025-08-25 18:48:48] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -28.339964-0.000997j
[2025-08-25 18:48:53] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -28.348304+0.001331j
[2025-08-25 18:48:59] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -28.344250+0.000259j
[2025-08-25 18:49:05] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -28.343746-0.000601j
[2025-08-25 18:49:11] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -28.346514-0.004003j
[2025-08-25 18:49:17] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -28.360183-0.004882j
[2025-08-25 18:49:23] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -28.343712-0.003004j
[2025-08-25 18:49:29] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -28.336600+0.001258j
[2025-08-25 18:49:34] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -28.347984-0.002659j
[2025-08-25 18:49:40] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -28.352384-0.001034j
[2025-08-25 18:49:46] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -28.344728-0.001833j
[2025-08-25 18:49:52] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -28.354010+0.003771j
[2025-08-25 18:49:58] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -28.341700-0.000597j
[2025-08-25 18:50:04] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -28.341187-0.000005j
[2025-08-25 18:50:10] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -28.337216+0.000565j
[2025-08-25 18:50:15] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -28.336958+0.000527j
[2025-08-25 18:50:21] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -28.354820+0.001482j
[2025-08-25 18:50:27] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -28.353033-0.000015j
[2025-08-25 18:50:33] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -28.341975-0.001529j
[2025-08-25 18:50:39] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -28.345213-0.001335j
[2025-08-25 18:50:45] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -28.347264-0.000016j
[2025-08-25 18:50:50] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -28.349504-0.000551j
[2025-08-25 18:50:56] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -28.357002-0.002511j
[2025-08-25 18:51:02] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -28.351407+0.000833j
[2025-08-25 18:51:08] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -28.348900-0.000140j
[2025-08-25 18:51:14] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -28.350662-0.001079j
[2025-08-25 18:51:20] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -28.352322+0.000652j
[2025-08-25 18:51:26] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -28.335216+0.001980j
[2025-08-25 18:51:31] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -28.346660+0.003141j
[2025-08-25 18:51:37] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -28.350973+0.001939j
[2025-08-25 18:51:43] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -28.347835-0.002090j
[2025-08-25 18:51:49] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -28.343228-0.001056j
[2025-08-25 18:51:55] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -28.350999-0.000731j
[2025-08-25 18:52:01] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -28.355061+0.002661j
[2025-08-25 18:52:07] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -28.351615-0.000017j
[2025-08-25 18:52:12] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -28.348495+0.000276j
[2025-08-25 18:52:18] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -28.346284-0.004342j
[2025-08-25 18:52:24] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -28.337841-0.000560j
[2025-08-25 18:52:30] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -28.353232-0.004791j
[2025-08-25 18:52:36] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -28.349480-0.001773j
[2025-08-25 18:52:42] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -28.357366-0.004109j
[2025-08-25 18:52:47] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -28.343915+0.003107j
[2025-08-25 18:52:53] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -28.342996+0.005625j
[2025-08-25 18:52:59] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -28.351976-0.000641j
[2025-08-25 18:53:05] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -28.351109+0.001507j
[2025-08-25 18:53:11] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -28.364184-0.005455j
[2025-08-25 18:53:17] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -28.338091+0.001472j
[2025-08-25 18:53:23] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -28.346477+0.001981j
[2025-08-25 18:53:28] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -28.346383-0.001127j
[2025-08-25 18:53:34] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -28.356725+0.001024j
[2025-08-25 18:53:40] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -28.350384+0.002329j
[2025-08-25 18:53:46] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -28.344140-0.003286j
[2025-08-25 18:53:52] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -28.353186-0.003641j
[2025-08-25 18:53:58] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -28.348031+0.000929j
[2025-08-25 18:54:03] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -28.348930-0.002105j
[2025-08-25 18:54:09] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -28.341581-0.001513j
[2025-08-25 18:54:15] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -28.348900+0.000588j
[2025-08-25 18:54:21] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -28.357447+0.001421j
[2025-08-25 18:54:27] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -28.360823+0.002087j
[2025-08-25 18:54:33] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -28.351854-0.001733j
[2025-08-25 18:54:39] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -28.345488+0.001472j
[2025-08-25 18:54:44] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -28.340135-0.002627j
[2025-08-25 18:54:50] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -28.346966-0.002941j
[2025-08-25 18:54:50] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-25 18:54:56] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -28.348495+0.005880j
[2025-08-25 18:55:02] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -28.337156-0.001921j
[2025-08-25 18:55:08] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -28.354164+0.000959j
[2025-08-25 18:55:14] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -28.343254-0.000208j
[2025-08-25 18:55:19] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -28.350032-0.002308j
[2025-08-25 18:55:25] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -28.347228+0.003719j
[2025-08-25 18:55:31] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -28.346125-0.001565j
[2025-08-25 18:55:37] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -28.344697-0.000263j
[2025-08-25 18:55:43] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -28.345705+0.000528j
[2025-08-25 18:55:49] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -28.346800+0.004699j
[2025-08-25 18:55:55] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -28.346696+0.002093j
[2025-08-25 18:56:00] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -28.350523+0.000057j
[2025-08-25 18:56:06] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -28.348349+0.000361j
[2025-08-25 18:56:12] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -28.351963-0.003153j
[2025-08-25 18:56:18] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -28.342603-0.000940j
[2025-08-25 18:56:24] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -28.343718+0.002641j
[2025-08-25 18:56:30] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -28.344372+0.002663j
[2025-08-25 18:56:36] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -28.344965-0.001079j
[2025-08-25 18:56:41] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -28.334902+0.003423j
[2025-08-25 18:56:47] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -28.355452+0.001856j
[2025-08-25 18:56:53] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -28.346037+0.002380j
[2025-08-25 18:56:59] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -28.351564+0.005074j
[2025-08-25 18:57:05] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -28.356936+0.003219j
[2025-08-25 18:57:11] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -28.344693+0.001887j
[2025-08-25 18:57:17] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -28.345827-0.002893j
[2025-08-25 18:57:22] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -28.359992+0.000094j
[2025-08-25 18:57:28] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -28.348388+0.000214j
[2025-08-25 18:57:34] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -28.349127+0.002299j
[2025-08-25 18:57:40] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -28.346713+0.002994j
[2025-08-25 18:57:46] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -28.361830+0.001243j
[2025-08-25 18:57:52] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -28.346090-0.004785j
[2025-08-25 18:57:57] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -28.350894+0.003713j
[2025-08-25 18:58:03] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -28.347216-0.006063j
[2025-08-25 18:58:09] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -28.354809-0.000690j
[2025-08-25 18:58:15] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -28.345202+0.002242j
[2025-08-25 18:58:21] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -28.339157+0.004232j
[2025-08-25 18:58:27] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -28.348326-0.000113j
[2025-08-25 18:58:33] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -28.351210-0.000789j
[2025-08-25 18:58:38] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -28.342204-0.002183j
[2025-08-25 18:58:44] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -28.342257-0.003968j
[2025-08-25 18:58:50] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -28.351552+0.000576j
[2025-08-25 18:58:56] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -28.349867-0.003448j
[2025-08-25 18:59:02] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -28.345140-0.001125j
[2025-08-25 18:59:08] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -28.345925+0.002669j
[2025-08-25 18:59:13] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -28.339978+0.001520j
[2025-08-25 18:59:19] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -28.356920-0.000507j
[2025-08-25 18:59:25] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -28.348483-0.000901j
[2025-08-25 18:59:31] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -28.360105-0.001108j
[2025-08-25 18:59:37] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -28.353737+0.000845j
[2025-08-25 18:59:43] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -28.346747+0.002118j
[2025-08-25 18:59:49] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -28.349248-0.003741j
[2025-08-25 18:59:54] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -28.357064+0.002536j
[2025-08-25 19:00:00] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -28.340100-0.000734j
[2025-08-25 19:00:06] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -28.342966+0.000087j
[2025-08-25 19:00:12] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -28.346120+0.001821j
[2025-08-25 19:00:18] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -28.342276-0.000631j
[2025-08-25 19:00:24] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -28.352282+0.000609j
[2025-08-25 19:00:30] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -28.347127+0.003379j
[2025-08-25 19:00:35] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -28.335829+0.003332j
[2025-08-25 19:00:41] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -28.342170-0.001899j
[2025-08-25 19:00:47] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -28.360906-0.001986j
[2025-08-25 19:00:53] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -28.337193+0.002185j
[2025-08-25 19:00:59] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -28.348742+0.000365j
[2025-08-25 19:01:05] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -28.339775+0.000018j
[2025-08-25 19:01:10] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -28.358233+0.002485j
[2025-08-25 19:01:16] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -28.349813-0.003210j
[2025-08-25 19:01:22] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -28.344028+0.003168j
[2025-08-25 19:01:28] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -28.354286+0.001297j
[2025-08-25 19:01:34] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -28.348372+0.000582j
[2025-08-25 19:01:40] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -28.348341+0.001892j
[2025-08-25 19:01:46] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -28.353264+0.001445j
[2025-08-25 19:01:51] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -28.352810-0.000862j
[2025-08-25 19:01:57] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -28.356452+0.001376j
[2025-08-25 19:02:03] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -28.349656+0.001352j
[2025-08-25 19:02:09] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -28.351217+0.001473j
[2025-08-25 19:02:15] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -28.352750+0.000312j
[2025-08-25 19:02:21] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -28.344044+0.000513j
[2025-08-25 19:02:26] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -28.335967-0.001128j
[2025-08-25 19:02:32] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -28.348094-0.000254j
[2025-08-25 19:02:38] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -28.344066+0.002745j
[2025-08-25 19:02:44] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -28.340592+0.002520j
[2025-08-25 19:02:50] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -28.347374+0.001954j
[2025-08-25 19:02:56] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -28.346623+0.000852j
[2025-08-25 19:03:02] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -28.330104-0.000916j
[2025-08-25 19:03:07] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -28.356547-0.000084j
[2025-08-25 19:03:13] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -28.356901+0.001677j
[2025-08-25 19:03:19] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -28.352193-0.000224j
[2025-08-25 19:03:25] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -28.346667-0.001473j
[2025-08-25 19:03:31] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -28.346262+0.000197j
[2025-08-25 19:03:37] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -28.339698+0.000749j
[2025-08-25 19:03:43] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -28.345077+0.003396j
[2025-08-25 19:03:48] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -28.350687-0.004198j
[2025-08-25 19:03:54] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -28.345413-0.000076j
[2025-08-25 19:04:00] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -28.345100+0.001202j
[2025-08-25 19:04:06] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -28.345743+0.003374j
[2025-08-25 19:04:12] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -28.340162+0.001993j
[2025-08-25 19:04:18] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -28.345103-0.000956j
[2025-08-25 19:04:24] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -28.346030-0.005127j
[2025-08-25 19:04:29] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -28.352179-0.000996j
[2025-08-25 19:04:35] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -28.341086-0.001302j
[2025-08-25 19:04:35] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-25 19:04:41] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -28.348885+0.001151j
[2025-08-25 19:04:47] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -28.342650+0.000215j
[2025-08-25 19:04:53] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -28.352999-0.004597j
[2025-08-25 19:04:59] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -28.354280-0.000582j
[2025-08-25 19:05:05] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -28.354840-0.002211j
[2025-08-25 19:05:10] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -28.351549+0.000744j
[2025-08-25 19:05:16] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -28.353616-0.003324j
[2025-08-25 19:05:22] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -28.355558-0.000020j
[2025-08-25 19:05:28] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -28.348547+0.002247j
[2025-08-25 19:05:34] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -28.343556+0.001887j
[2025-08-25 19:05:40] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -28.339259-0.002602j
[2025-08-25 19:05:46] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -28.347932+0.002539j
[2025-08-25 19:05:51] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -28.344199+0.001422j
[2025-08-25 19:05:57] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -28.351337+0.001656j
[2025-08-25 19:06:03] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -28.345831-0.001167j
[2025-08-25 19:06:09] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -28.341916-0.003243j
[2025-08-25 19:06:15] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -28.351345+0.000442j
[2025-08-25 19:06:21] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -28.346484-0.001628j
[2025-08-25 19:06:26] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -28.351930+0.000140j
[2025-08-25 19:06:32] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -28.355440+0.001368j
[2025-08-25 19:06:38] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -28.351940+0.001284j
[2025-08-25 19:06:44] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -28.343290-0.000621j
[2025-08-25 19:06:50] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -28.354933+0.000969j
[2025-08-25 19:06:56] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -28.346059-0.002300j
[2025-08-25 19:07:02] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -28.358573+0.003669j
[2025-08-25 19:07:07] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -28.354893+0.004225j
[2025-08-25 19:07:13] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -28.351411+0.001243j
[2025-08-25 19:07:19] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -28.356302+0.003378j
[2025-08-25 19:07:25] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -28.347602-0.001241j
[2025-08-25 19:07:31] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -28.356347+0.003436j
[2025-08-25 19:07:37] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -28.345653+0.000063j
[2025-08-25 19:07:43] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -28.351711+0.002887j
[2025-08-25 19:07:48] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -28.345824-0.000574j
[2025-08-25 19:07:54] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -28.354532+0.000537j
[2025-08-25 19:08:00] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -28.339488-0.000480j
[2025-08-25 19:08:06] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -28.353524+0.001786j
[2025-08-25 19:08:12] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -28.354939+0.001571j
[2025-08-25 19:08:18] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -28.344462-0.000794j
[2025-08-25 19:08:24] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -28.345604+0.000151j
[2025-08-25 19:08:29] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -28.350402-0.000709j
[2025-08-25 19:08:35] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -28.344393+0.000394j
[2025-08-25 19:08:41] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -28.345282+0.001091j
[2025-08-25 19:08:47] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -28.348425-0.000878j
[2025-08-25 19:08:53] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -28.350962+0.000936j
[2025-08-25 19:08:59] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -28.355444-0.002458j
[2025-08-25 19:09:04] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -28.359574-0.000589j
[2025-08-25 19:09:10] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -28.344419-0.001457j
[2025-08-25 19:09:16] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -28.343725-0.003476j
[2025-08-25 19:09:22] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -28.352656+0.000776j
[2025-08-25 19:09:28] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -28.364889+0.001783j
[2025-08-25 19:09:28] RESTART #2 | Period: 600
[2025-08-25 19:09:34] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -28.353044-0.003819j
[2025-08-25 19:09:40] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -28.348354+0.003036j
[2025-08-25 19:09:45] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -28.351715-0.002702j
[2025-08-25 19:09:51] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -28.342473-0.004163j
[2025-08-25 19:09:57] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -28.350591+0.002320j
[2025-08-25 19:10:03] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -28.342166-0.000815j
[2025-08-25 19:10:09] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -28.337091+0.000013j
[2025-08-25 19:10:15] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -28.344752-0.000105j
[2025-08-25 19:10:21] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -28.355502-0.000542j
[2025-08-25 19:10:26] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -28.346289-0.003853j
[2025-08-25 19:10:32] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -28.343274-0.001835j
[2025-08-25 19:10:38] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -28.349160-0.001938j
[2025-08-25 19:10:44] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -28.348896+0.000249j
[2025-08-25 19:10:50] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -28.358779-0.002709j
[2025-08-25 19:10:56] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -28.348325+0.000932j
[2025-08-25 19:11:02] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -28.344575+0.003686j
[2025-08-25 19:11:07] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -28.341600+0.000443j
[2025-08-25 19:11:13] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -28.359153-0.000508j
[2025-08-25 19:11:19] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -28.352857-0.000659j
[2025-08-25 19:11:25] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -28.363272+0.001046j
[2025-08-25 19:11:31] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -28.349394-0.002640j
[2025-08-25 19:11:37] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -28.351715+0.000244j
[2025-08-25 19:11:43] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -28.352995+0.002597j
[2025-08-25 19:11:48] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -28.332307-0.000378j
[2025-08-25 19:11:54] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -28.336972-0.002328j
[2025-08-25 19:12:00] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -28.357500-0.003559j
[2025-08-25 19:12:06] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -28.350733+0.002396j
[2025-08-25 19:12:12] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -28.351214-0.003445j
[2025-08-25 19:12:18] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -28.344300+0.001929j
[2025-08-25 19:12:23] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -28.353643+0.005278j
[2025-08-25 19:12:29] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -28.344031+0.005492j
[2025-08-25 19:12:35] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -28.357205+0.002132j
[2025-08-25 19:12:41] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -28.343990-0.001239j
[2025-08-25 19:12:47] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -28.350524-0.003802j
[2025-08-25 19:12:53] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -28.342747-0.000527j
[2025-08-25 19:12:59] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -28.346624+0.000268j
[2025-08-25 19:13:04] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -28.350930-0.002255j
[2025-08-25 19:13:10] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -28.342882+0.000454j
[2025-08-25 19:13:16] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -28.349644+0.000911j
[2025-08-25 19:13:22] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -28.363375+0.001235j
[2025-08-25 19:13:28] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -28.351940+0.001530j
[2025-08-25 19:13:34] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -28.354298-0.000293j
[2025-08-25 19:13:39] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -28.344922-0.001867j
[2025-08-25 19:13:45] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -28.344494-0.000057j
[2025-08-25 19:13:51] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -28.344977-0.001742j
[2025-08-25 19:13:57] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -28.345024-0.003356j
[2025-08-25 19:14:03] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -28.350887-0.001372j
[2025-08-25 19:14:09] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -28.357812+0.000134j
[2025-08-25 19:14:15] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -28.347511-0.005751j
[2025-08-25 19:14:20] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -28.349194-0.005071j
[2025-08-25 19:14:20] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-25 19:14:26] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -28.349401+0.000781j
[2025-08-25 19:14:32] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -28.351963+0.002065j
[2025-08-25 19:14:38] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -28.345997+0.000224j
[2025-08-25 19:14:44] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -28.347009+0.003680j
[2025-08-25 19:14:50] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -28.347944+0.001417j
[2025-08-25 19:14:56] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -28.357942+0.001036j
[2025-08-25 19:15:01] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -28.334335+0.004492j
[2025-08-25 19:15:07] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -28.333917+0.000067j
[2025-08-25 19:15:13] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -28.358293+0.002832j
[2025-08-25 19:15:19] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -28.355461-0.001190j
[2025-08-25 19:15:25] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -28.345103+0.001699j
[2025-08-25 19:15:31] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -28.351692+0.000724j
[2025-08-25 19:15:37] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -28.352408-0.003792j
[2025-08-25 19:15:42] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -28.347754-0.000526j
[2025-08-25 19:15:48] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -28.352356-0.000867j
[2025-08-25 19:15:54] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -28.358245-0.001394j
[2025-08-25 19:16:00] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -28.345768-0.001805j
[2025-08-25 19:16:06] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -28.354656-0.000937j
[2025-08-25 19:16:12] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -28.347791-0.001748j
[2025-08-25 19:16:18] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -28.345645+0.002617j
[2025-08-25 19:16:23] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -28.349868+0.003905j
[2025-08-25 19:16:29] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -28.344308-0.000905j
[2025-08-25 19:16:35] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -28.350770+0.001319j
[2025-08-25 19:16:41] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -28.349649-0.000013j
[2025-08-25 19:16:47] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -28.349012-0.003743j
[2025-08-25 19:16:53] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -28.355243-0.003546j
[2025-08-25 19:16:59] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -28.343590+0.003127j
[2025-08-25 19:17:04] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -28.352186-0.004136j
[2025-08-25 19:17:10] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -28.347181+0.000043j
[2025-08-25 19:17:16] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -28.353877-0.000022j
[2025-08-25 19:17:22] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -28.348786+0.000783j
[2025-08-25 19:17:28] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -28.351950+0.002164j
[2025-08-25 19:17:34] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -28.356363-0.005233j
[2025-08-25 19:17:40] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -28.345171+0.003630j
[2025-08-25 19:17:46] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -28.346721-0.002101j
[2025-08-25 19:17:52] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -28.345538-0.000686j
[2025-08-25 19:17:58] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -28.349412-0.000794j
[2025-08-25 19:18:04] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -28.357823+0.000904j
[2025-08-25 19:18:09] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -28.335497+0.005209j
[2025-08-25 19:18:15] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -28.356391-0.000309j
[2025-08-25 19:18:21] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -28.351512+0.002261j
[2025-08-25 19:18:27] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -28.342527+0.002142j
[2025-08-25 19:18:33] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -28.342114-0.000697j
[2025-08-25 19:18:39] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -28.346666-0.000191j
[2025-08-25 19:18:44] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -28.358374-0.001456j
[2025-08-25 19:18:50] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -28.351122+0.001172j
[2025-08-25 19:18:56] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -28.353082-0.000535j
[2025-08-25 19:19:02] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -28.347998-0.001695j
[2025-08-25 19:19:08] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -28.353468+0.000649j
[2025-08-25 19:19:14] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -28.344824+0.001037j
[2025-08-25 19:19:19] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -28.344726-0.004602j
[2025-08-25 19:19:25] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -28.341998-0.000225j
[2025-08-25 19:19:31] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -28.342014-0.007220j
[2025-08-25 19:19:37] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -28.346585-0.002165j
[2025-08-25 19:19:43] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -28.344736+0.006077j
[2025-08-25 19:19:49] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -28.354051-0.000298j
[2025-08-25 19:19:55] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -28.350892-0.000086j
[2025-08-25 19:20:00] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -28.333221-0.001191j
[2025-08-25 19:20:06] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -28.350662-0.000379j
[2025-08-25 19:20:12] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -28.356612-0.001153j
[2025-08-25 19:20:18] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -28.338755-0.001940j
[2025-08-25 19:20:24] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -28.367293-0.001454j
[2025-08-25 19:20:30] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -28.349509+0.001678j
[2025-08-25 19:20:36] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -28.344829-0.002095j
[2025-08-25 19:20:41] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -28.352411+0.000121j
[2025-08-25 19:20:47] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -28.351608-0.000279j
[2025-08-25 19:20:53] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -28.353430+0.000575j
[2025-08-25 19:20:59] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -28.350075-0.001332j
[2025-08-25 19:21:05] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -28.331340+0.006401j
[2025-08-25 19:21:11] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -28.348975+0.000766j
[2025-08-25 19:21:17] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -28.344289-0.000956j
[2025-08-25 19:21:23] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -28.344716+0.001004j
[2025-08-25 19:21:28] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -28.349649+0.001402j
[2025-08-25 19:21:34] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -28.345499+0.000962j
[2025-08-25 19:21:40] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -28.344374-0.002840j
[2025-08-25 19:21:46] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -28.337880+0.001093j
[2025-08-25 19:21:52] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -28.344524+0.002179j
[2025-08-25 19:21:58] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -28.346258-0.001819j
[2025-08-25 19:22:04] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -28.350324-0.003642j
[2025-08-25 19:22:09] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -28.356857+0.003120j
[2025-08-25 19:22:15] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -28.354838-0.000428j
[2025-08-25 19:22:21] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -28.346483+0.001822j
[2025-08-25 19:22:27] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -28.354514-0.001481j
[2025-08-25 19:22:33] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -28.340811+0.002399j
[2025-08-25 19:22:39] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -28.342477-0.000631j
[2025-08-25 19:22:45] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -28.359367+0.000092j
[2025-08-25 19:22:51] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -28.343413+0.000626j
[2025-08-25 19:22:56] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -28.348657-0.002265j
[2025-08-25 19:23:02] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -28.352179-0.004405j
[2025-08-25 19:23:08] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -28.362856+0.002285j
[2025-08-25 19:23:14] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -28.346956+0.003844j
[2025-08-25 19:23:20] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -28.348379+0.001997j
[2025-08-25 19:23:26] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -28.351143-0.000879j
[2025-08-25 19:23:32] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -28.353903-0.000129j
[2025-08-25 19:23:37] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -28.355888+0.003363j
[2025-08-25 19:23:43] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -28.344971-0.000870j
[2025-08-25 19:23:49] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -28.340803-0.001286j
[2025-08-25 19:23:55] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -28.345956-0.001311j
[2025-08-25 19:24:01] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -28.346080+0.001469j
[2025-08-25 19:24:07] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -28.356193+0.000036j
[2025-08-25 19:24:07] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-25 19:24:13] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -28.360810-0.002986j
[2025-08-25 19:24:19] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -28.343639+0.000583j
[2025-08-25 19:24:24] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -28.350909-0.000499j
[2025-08-25 19:24:30] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -28.354662+0.000544j
[2025-08-25 19:24:36] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -28.339961+0.000788j
[2025-08-25 19:24:42] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -28.355544-0.002345j
[2025-08-25 19:24:48] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -28.347224-0.000429j
[2025-08-25 19:24:54] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -28.341691-0.000945j
[2025-08-25 19:25:00] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -28.347799+0.000517j
[2025-08-25 19:25:05] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -28.349514+0.001005j
[2025-08-25 19:25:11] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -28.352580-0.000299j
[2025-08-25 19:25:17] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -28.340692+0.000517j
[2025-08-25 19:25:23] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -28.352309-0.000737j
[2025-08-25 19:25:29] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -28.357012+0.000343j
[2025-08-25 19:25:35] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -28.347277+0.000959j
[2025-08-25 19:25:41] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -28.350917+0.000974j
[2025-08-25 19:25:46] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -28.349069-0.000983j
[2025-08-25 19:25:52] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -28.360111-0.002283j
[2025-08-25 19:25:58] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -28.353064-0.000601j
[2025-08-25 19:26:04] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -28.350113-0.002016j
[2025-08-25 19:26:10] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -28.346986-0.000818j
[2025-08-25 19:26:16] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -28.350232-0.002647j
[2025-08-25 19:26:22] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -28.348547+0.000057j
[2025-08-25 19:26:28] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -28.341643+0.000092j
[2025-08-25 19:26:34] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -28.347425+0.000480j
[2025-08-25 19:26:40] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -28.351924-0.000361j
[2025-08-25 19:26:46] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -28.351684-0.003396j
[2025-08-25 19:26:52] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -28.342011-0.003201j
[2025-08-25 19:27:00] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -28.355900+0.001386j
[2025-08-25 19:27:05] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -28.346910+0.002885j
[2025-08-25 19:27:12] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -28.354456-0.000025j
[2025-08-25 19:27:18] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -28.345969+0.000121j
[2025-08-25 19:27:24] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -28.354458-0.001004j
[2025-08-25 19:27:30] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -28.349568-0.001076j
[2025-08-25 19:27:36] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -28.364825+0.003632j
[2025-08-25 19:27:41] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -28.349386+0.002331j
[2025-08-25 19:27:47] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -28.348940-0.002237j
[2025-08-25 19:27:53] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -28.351822+0.000036j
[2025-08-25 19:27:59] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -28.346493+0.002321j
[2025-08-25 19:28:05] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -28.338830-0.002491j
[2025-08-25 19:28:11] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -28.354919-0.003252j
[2025-08-25 19:28:17] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -28.352792-0.002235j
[2025-08-25 19:28:22] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -28.346402+0.005258j
[2025-08-25 19:28:28] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -28.356262+0.000152j
[2025-08-25 19:28:34] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -28.350468+0.003255j
[2025-08-25 19:28:40] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -28.360664+0.007343j
[2025-08-25 19:28:46] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -28.345428+0.001885j
[2025-08-25 19:28:52] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -28.347024+0.001392j
[2025-08-25 19:28:58] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -28.350143+0.001646j
[2025-08-25 19:29:03] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -28.350535+0.001262j
[2025-08-25 19:29:09] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -28.345685+0.002199j
[2025-08-25 19:29:15] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -28.353563-0.001658j
[2025-08-25 19:29:21] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -28.348618-0.005387j
[2025-08-25 19:29:27] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -28.348915+0.000951j
[2025-08-25 19:29:33] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -28.349510+0.002134j
[2025-08-25 19:29:39] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -28.344752+0.000046j
[2025-08-25 19:29:44] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -28.346616+0.000993j
[2025-08-25 19:29:50] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -28.339570+0.000458j
[2025-08-25 19:29:56] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -28.344989-0.000941j
[2025-08-25 19:30:02] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -28.352915-0.001608j
[2025-08-25 19:30:08] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -28.339524-0.001400j
[2025-08-25 19:30:14] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -28.350776+0.004093j
[2025-08-25 19:30:20] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -28.354638-0.002173j
[2025-08-25 19:30:26] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -28.348029+0.004195j
[2025-08-25 19:30:31] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -28.344582-0.001982j
[2025-08-25 19:30:37] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -28.351530-0.003433j
[2025-08-25 19:30:43] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -28.349297-0.000989j
[2025-08-25 19:30:49] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -28.339012+0.000549j
[2025-08-25 19:30:55] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -28.350636+0.000612j
[2025-08-25 19:31:01] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -28.350076+0.000290j
[2025-08-25 19:31:07] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -28.358410-0.000579j
[2025-08-25 19:31:12] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -28.346075+0.001381j
[2025-08-25 19:31:18] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -28.355497-0.001637j
[2025-08-25 19:31:24] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -28.346499-0.000050j
[2025-08-25 19:31:30] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -28.344503+0.000344j
[2025-08-25 19:31:36] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -28.356002+0.006641j
[2025-08-25 19:31:42] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -28.345607-0.002888j
[2025-08-25 19:31:48] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -28.350832+0.000528j
[2025-08-25 19:31:53] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -28.333583+0.000112j
[2025-08-25 19:31:59] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -28.354649+0.001154j
[2025-08-25 19:32:05] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -28.351193+0.002915j
[2025-08-25 19:32:11] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -28.341320+0.000118j
[2025-08-25 19:32:17] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -28.346914+0.004154j
[2025-08-25 19:32:23] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -28.354495+0.000299j
[2025-08-25 19:32:29] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -28.361400-0.000004j
[2025-08-25 19:32:35] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -28.353052+0.004162j
[2025-08-25 19:32:40] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -28.357047+0.000250j
[2025-08-25 19:32:46] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -28.344218+0.001573j
[2025-08-25 19:32:52] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -28.347484-0.003084j
[2025-08-25 19:32:58] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -28.352845-0.001103j
[2025-08-25 19:33:04] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -28.345832-0.002402j
[2025-08-25 19:33:10] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -28.336514+0.001402j
[2025-08-25 19:33:16] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -28.356418-0.000673j
[2025-08-25 19:33:21] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -28.353951+0.003410j
[2025-08-25 19:33:27] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -28.349825+0.000334j
[2025-08-25 19:33:33] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -28.349529-0.001987j
[2025-08-25 19:33:39] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -28.355943-0.000028j
[2025-08-25 19:33:45] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -28.344862+0.000612j
[2025-08-25 19:33:51] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -28.353179-0.000846j
[2025-08-25 19:33:57] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -28.351557+0.000406j
[2025-08-25 19:33:57] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-25 19:34:02] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -28.351186-0.000097j
[2025-08-25 19:34:08] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -28.355688+0.001327j
[2025-08-25 19:34:14] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -28.343458-0.002706j
[2025-08-25 19:34:20] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -28.359560+0.000340j
[2025-08-25 19:34:26] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -28.352806+0.000261j
[2025-08-25 19:34:32] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -28.354343-0.000373j
[2025-08-25 19:34:38] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -28.333446-0.002397j
[2025-08-25 19:34:44] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -28.340398+0.002796j
[2025-08-25 19:34:49] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -28.341442+0.002359j
[2025-08-25 19:34:55] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -28.353661-0.002012j
[2025-08-25 19:35:01] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -28.338688+0.000454j
[2025-08-25 19:35:07] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -28.358925-0.001665j
[2025-08-25 19:35:13] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -28.348921-0.002485j
[2025-08-25 19:35:19] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -28.343195+0.001049j
[2025-08-25 19:35:25] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -28.353268+0.002569j
[2025-08-25 19:35:30] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -28.344128-0.001939j
[2025-08-25 19:35:36] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -28.353657-0.001844j
[2025-08-25 19:35:42] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -28.350980-0.003618j
[2025-08-25 19:35:48] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -28.351310+0.000089j
[2025-08-25 19:35:54] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -28.353975+0.003719j
[2025-08-25 19:36:00] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -28.349979+0.004578j
[2025-08-25 19:36:06] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -28.353162+0.000630j
[2025-08-25 19:36:11] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -28.345652+0.002214j
[2025-08-25 19:36:17] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -28.352075-0.000997j
[2025-08-25 19:36:23] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -28.350883-0.000320j
[2025-08-25 19:36:29] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -28.352582+0.001633j
[2025-08-25 19:36:35] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -28.347775-0.000992j
[2025-08-25 19:36:41] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -28.345294+0.000092j
[2025-08-25 19:36:47] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -28.335602+0.001573j
[2025-08-25 19:36:53] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -28.348890+0.002097j
[2025-08-25 19:36:58] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -28.350959-0.002415j
[2025-08-25 19:37:04] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -28.345750+0.001351j
[2025-08-25 19:37:10] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -28.351563-0.001848j
[2025-08-25 19:37:16] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -28.345949+0.000589j
[2025-08-25 19:37:22] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -28.349038+0.000841j
[2025-08-25 19:37:28] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -28.349143+0.001755j
[2025-08-25 19:37:34] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -28.349162-0.000183j
[2025-08-25 19:37:39] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -28.347198+0.000574j
[2025-08-25 19:37:45] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -28.346216+0.000002j
[2025-08-25 19:37:51] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -28.352263+0.002284j
[2025-08-25 19:37:57] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -28.344067+0.003312j
[2025-08-25 19:38:03] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -28.349562-0.001137j
[2025-08-25 19:38:09] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -28.348716-0.000118j
[2025-08-25 19:38:15] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -28.347803-0.000261j
[2025-08-25 19:38:20] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -28.334824+0.001701j
[2025-08-25 19:38:26] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -28.345162-0.001947j
[2025-08-25 19:38:32] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -28.351653-0.001039j
[2025-08-25 19:38:38] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -28.348139+0.001473j
[2025-08-25 19:38:44] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -28.346979-0.003011j
[2025-08-25 19:38:50] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -28.352261+0.002253j
[2025-08-25 19:38:56] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -28.350546-0.000490j
[2025-08-25 19:39:02] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -28.349797-0.000735j
[2025-08-25 19:39:07] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -28.338375+0.003667j
[2025-08-25 19:39:13] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -28.349190+0.000245j
[2025-08-25 19:39:19] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -28.349925-0.003171j
[2025-08-25 19:39:25] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -28.350032-0.004369j
[2025-08-25 19:39:31] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -28.357991+0.002940j
[2025-08-25 19:39:37] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -28.346913+0.002628j
[2025-08-25 19:39:43] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -28.344403-0.001223j
[2025-08-25 19:39:48] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -28.344087-0.001124j
[2025-08-25 19:39:54] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -28.352707-0.003073j
[2025-08-25 19:40:00] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -28.343659+0.003699j
[2025-08-25 19:40:06] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -28.342885+0.000550j
[2025-08-25 19:40:12] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -28.346366-0.000210j
[2025-08-25 19:40:18] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -28.355363+0.002887j
[2025-08-25 19:40:24] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -28.344292+0.002539j
[2025-08-25 19:40:29] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -28.339308-0.004108j
[2025-08-25 19:40:35] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -28.354973+0.000932j
[2025-08-25 19:40:41] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -28.344284+0.001824j
[2025-08-25 19:40:47] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -28.342367-0.000150j
[2025-08-25 19:40:53] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -28.347538-0.000998j
[2025-08-25 19:40:59] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -28.348434-0.001559j
[2025-08-25 19:41:05] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -28.350678+0.002378j
[2025-08-25 19:41:11] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -28.349215-0.002307j
[2025-08-25 19:41:16] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -28.349312+0.002573j
[2025-08-25 19:41:22] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -28.348329-0.001364j
[2025-08-25 19:41:28] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -28.344008+0.004819j
[2025-08-25 19:41:34] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -28.349251-0.003948j
[2025-08-25 19:41:40] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -28.350739-0.001400j
[2025-08-25 19:41:46] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -28.344365+0.001148j
[2025-08-25 19:41:52] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -28.356848-0.002410j
[2025-08-25 19:41:57] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -28.349774+0.003336j
[2025-08-25 19:42:03] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -28.354719+0.000181j
[2025-08-25 19:42:09] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -28.351242+0.003817j
[2025-08-25 19:42:15] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -28.348583+0.000725j
[2025-08-25 19:42:21] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -28.340199-0.001650j
[2025-08-25 19:42:27] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -28.356584-0.002521j
[2025-08-25 19:42:33] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -28.349408-0.001688j
[2025-08-25 19:42:38] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -28.340859+0.001579j
[2025-08-25 19:42:44] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -28.357134+0.000201j
[2025-08-25 19:42:50] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -28.349367+0.000773j
[2025-08-25 19:42:56] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -28.348568+0.002226j
[2025-08-25 19:43:02] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -28.347036-0.000099j
[2025-08-25 19:43:08] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -28.347930-0.002194j
[2025-08-25 19:43:14] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -28.342571-0.000981j
[2025-08-25 19:43:20] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -28.354193-0.000261j
[2025-08-25 19:43:25] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -28.345668+0.000283j
[2025-08-25 19:43:31] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -28.350611+0.000064j
[2025-08-25 19:43:37] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -28.356588-0.003273j
[2025-08-25 19:43:43] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -28.353970-0.002370j
[2025-08-25 19:43:43] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-25 19:43:49] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -28.344901-0.001559j
[2025-08-25 19:43:55] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -28.345275-0.001876j
[2025-08-25 19:44:01] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -28.333330+0.001283j
[2025-08-25 19:44:07] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -28.352950+0.001608j
[2025-08-25 19:44:12] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -28.347318+0.000627j
[2025-08-25 19:44:18] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -28.348137+0.000337j
[2025-08-25 19:44:24] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -28.355736+0.003946j
[2025-08-25 19:44:30] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -28.339399-0.001227j
[2025-08-25 19:44:36] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -28.345640+0.005830j
[2025-08-25 19:44:42] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -28.345961-0.002992j
[2025-08-25 19:44:48] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -28.354068+0.002176j
[2025-08-25 19:44:53] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -28.343694+0.005212j
[2025-08-25 19:44:59] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -28.358394+0.003204j
[2025-08-25 19:45:05] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -28.349540-0.003204j
[2025-08-25 19:45:11] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -28.353146-0.001834j
[2025-08-25 19:45:17] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -28.349395+0.000157j
[2025-08-25 19:45:23] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -28.353372-0.001643j
[2025-08-25 19:45:29] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -28.342317-0.001634j
[2025-08-25 19:45:34] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -28.348279-0.000627j
[2025-08-25 19:45:40] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -28.352570+0.000749j
[2025-08-25 19:45:46] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -28.346362+0.001549j
[2025-08-25 19:45:52] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -28.338378+0.002141j
[2025-08-25 19:45:58] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -28.350263-0.003006j
[2025-08-25 19:46:04] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -28.356705-0.000404j
[2025-08-25 19:46:10] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -28.355681-0.001514j
[2025-08-25 19:46:16] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -28.335113+0.000284j
[2025-08-25 19:46:22] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -28.359246-0.001262j
[2025-08-25 19:46:27] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -28.347836-0.000096j
[2025-08-25 19:46:33] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -28.351197-0.000715j
[2025-08-25 19:46:39] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -28.350272-0.001086j
[2025-08-25 19:46:45] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -28.343989-0.002080j
[2025-08-25 19:46:51] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -28.356958-0.004688j
[2025-08-25 19:46:57] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -28.343068+0.000694j
[2025-08-25 19:47:03] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -28.354263+0.000772j
[2025-08-25 19:47:09] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -28.343249+0.005847j
[2025-08-25 19:47:14] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -28.354842-0.001666j
[2025-08-25 19:47:20] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -28.340448-0.000638j
[2025-08-25 19:47:26] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -28.348978-0.002544j
[2025-08-25 19:47:32] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -28.352302+0.001510j
[2025-08-25 19:47:38] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -28.349823+0.006097j
[2025-08-25 19:47:44] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -28.350303+0.001766j
[2025-08-25 19:47:50] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -28.353935+0.000487j
[2025-08-25 19:47:55] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -28.342290-0.000170j
[2025-08-25 19:48:01] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -28.343312+0.009868j
[2025-08-25 19:48:07] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -28.359196+0.001098j
[2025-08-25 19:48:13] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -28.344170-0.006032j
[2025-08-25 19:48:19] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -28.349203+0.000822j
[2025-08-25 19:48:25] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -28.356877-0.001067j
[2025-08-25 19:48:31] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -28.344437-0.000531j
[2025-08-25 19:48:37] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -28.346079+0.001835j
[2025-08-25 19:48:42] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -28.356312-0.001473j
[2025-08-25 19:48:48] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -28.344113+0.002063j
[2025-08-25 19:48:54] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -28.358781+0.005495j
[2025-08-25 19:49:00] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -28.350877-0.004373j
[2025-08-25 19:49:06] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -28.351064-0.001158j
[2025-08-25 19:49:12] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -28.355945+0.002953j
[2025-08-25 19:49:18] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -28.338619+0.003014j
[2025-08-25 19:49:23] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -28.355414-0.002590j
[2025-08-25 19:49:29] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -28.346340+0.003617j
[2025-08-25 19:49:35] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -28.351869+0.000229j
[2025-08-25 19:49:41] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -28.348225+0.004327j
[2025-08-25 19:49:47] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -28.342217-0.000137j
[2025-08-25 19:49:53] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -28.333624+0.003764j
[2025-08-25 19:49:59] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -28.357901+0.000685j
[2025-08-25 19:50:04] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -28.353841-0.002450j
[2025-08-25 19:50:10] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -28.351550+0.003259j
[2025-08-25 19:50:16] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -28.352960+0.001960j
[2025-08-25 19:50:22] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -28.347115-0.001247j
[2025-08-25 19:50:28] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -28.351260+0.001806j
[2025-08-25 19:50:34] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -28.350710+0.000963j
[2025-08-25 19:50:40] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -28.354300+0.001350j
[2025-08-25 19:50:46] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -28.341965+0.000652j
[2025-08-25 19:50:51] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -28.354733+0.001636j
[2025-08-25 19:50:57] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -28.357259+0.000172j
[2025-08-25 19:51:03] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -28.353051-0.004222j
[2025-08-25 19:51:09] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -28.350789+0.001393j
[2025-08-25 19:51:15] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -28.360167-0.002088j
[2025-08-25 19:51:21] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -28.360217+0.001073j
[2025-08-25 19:51:27] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -28.351089-0.001959j
[2025-08-25 19:51:32] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -28.349485-0.002729j
[2025-08-25 19:51:38] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -28.342880-0.000923j
[2025-08-25 19:51:44] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -28.355283+0.003379j
[2025-08-25 19:51:50] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -28.356545+0.000253j
[2025-08-25 19:51:56] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -28.349426-0.000767j
[2025-08-25 19:52:02] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -28.346239-0.002166j
[2025-08-25 19:52:08] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -28.346066+0.000313j
[2025-08-25 19:52:13] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -28.339542+0.000436j
[2025-08-25 19:52:19] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -28.349268+0.000888j
[2025-08-25 19:52:25] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -28.348698-0.000959j
[2025-08-25 19:52:31] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -28.351785-0.001933j
[2025-08-25 19:52:37] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -28.338214+0.000448j
[2025-08-25 19:52:43] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -28.338658-0.000473j
[2025-08-25 19:52:49] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -28.349645-0.005187j
[2025-08-25 19:52:55] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -28.351517+0.004231j
[2025-08-25 19:53:00] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -28.346355+0.000592j
[2025-08-25 19:53:06] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -28.344046+0.002604j
[2025-08-25 19:53:12] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -28.355070-0.001959j
[2025-08-25 19:53:18] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -28.356083+0.001847j
[2025-08-25 19:53:24] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -28.346497+0.000878j
[2025-08-25 19:53:30] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -28.337808+0.002901j
[2025-08-25 19:53:30] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-25 19:53:36] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -28.338965-0.000136j
[2025-08-25 19:53:42] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -28.351749-0.000935j
[2025-08-25 19:53:47] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -28.353769+0.000331j
[2025-08-25 19:53:53] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -28.346535+0.000338j
[2025-08-25 19:53:59] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -28.345549+0.001331j
[2025-08-25 19:54:05] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -28.348807-0.000985j
[2025-08-25 19:54:11] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -28.351110+0.003113j
[2025-08-25 19:54:17] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -28.347500+0.002093j
[2025-08-25 19:54:23] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -28.347787-0.002425j
[2025-08-25 19:54:28] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -28.359213+0.000135j
[2025-08-25 19:54:34] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -28.343966+0.002037j
[2025-08-25 19:54:40] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -28.354774+0.000086j
[2025-08-25 19:54:46] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -28.335766-0.000257j
[2025-08-25 19:54:52] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -28.345563-0.001451j
[2025-08-25 19:54:58] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -28.354972+0.002167j
[2025-08-25 19:55:04] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -28.344787+0.001578j
[2025-08-25 19:55:10] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -28.351998-0.000242j
[2025-08-25 19:55:15] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -28.344413+0.004710j
[2025-08-25 19:55:21] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -28.343923-0.001253j
[2025-08-25 19:55:27] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -28.342501-0.001113j
[2025-08-25 19:55:33] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -28.356528+0.002356j
[2025-08-25 19:55:39] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -28.347984+0.002399j
[2025-08-25 19:55:45] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -28.361246+0.002450j
[2025-08-25 19:55:51] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -28.344655-0.002910j
[2025-08-25 19:55:57] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -28.345404+0.003085j
[2025-08-25 19:56:02] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -28.348357+0.004513j
[2025-08-25 19:56:08] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -28.356175+0.000345j
[2025-08-25 19:56:14] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -28.343500+0.001230j
[2025-08-25 19:56:20] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -28.346174-0.002614j
[2025-08-25 19:56:26] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -28.354708-0.001407j
[2025-08-25 19:56:32] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -28.355489-0.003534j
[2025-08-25 19:56:38] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -28.352128-0.001051j
[2025-08-25 19:56:43] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -28.344075-0.002669j
[2025-08-25 19:56:49] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -28.344143-0.000130j
[2025-08-25 19:56:55] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -28.343813+0.001071j
[2025-08-25 19:57:01] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -28.347084-0.002657j
[2025-08-25 19:57:07] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -28.351593+0.002336j
[2025-08-25 19:57:13] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -28.350250+0.003092j
[2025-08-25 19:57:19] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -28.352873+0.000663j
[2025-08-25 19:57:24] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -28.343803+0.000117j
[2025-08-25 19:57:30] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -28.342807-0.002357j
[2025-08-25 19:57:36] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -28.349828-0.002023j
[2025-08-25 19:57:42] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -28.345757-0.002560j
[2025-08-25 19:57:48] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -28.351268-0.001793j
[2025-08-25 19:57:54] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -28.354813+0.000552j
[2025-08-25 19:58:00] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -28.352004+0.001144j
[2025-08-25 19:58:05] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -28.353988-0.001200j
[2025-08-25 19:58:11] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -28.351480+0.002461j
[2025-08-25 19:58:17] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -28.354293-0.006190j
[2025-08-25 19:58:23] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -28.352894-0.001152j
[2025-08-25 19:58:29] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -28.351915-0.000561j
[2025-08-25 19:58:35] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -28.352461-0.001179j
[2025-08-25 19:58:41] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -28.346830-0.001537j
[2025-08-25 19:58:47] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -28.344237+0.003234j
[2025-08-25 19:58:52] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -28.334121-0.003375j
[2025-08-25 19:58:58] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -28.346894+0.000142j
[2025-08-25 19:59:04] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -28.349696+0.003695j
[2025-08-25 19:59:10] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -28.355722+0.001975j
[2025-08-25 19:59:16] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -28.352229-0.004989j
[2025-08-25 19:59:22] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -28.350761-0.000824j
[2025-08-25 19:59:28] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -28.336361-0.003770j
[2025-08-25 19:59:33] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -28.345489-0.001259j
[2025-08-25 19:59:39] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -28.352429-0.002685j
[2025-08-25 19:59:45] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -28.356871+0.003143j
[2025-08-25 19:59:51] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -28.352273+0.000382j
[2025-08-25 19:59:57] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -28.348877-0.000940j
[2025-08-25 20:00:03] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -28.346659-0.000527j
[2025-08-25 20:00:09] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -28.352761+0.000824j
[2025-08-25 20:00:15] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -28.350418+0.001212j
[2025-08-25 20:00:20] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -28.349417+0.001219j
[2025-08-25 20:00:26] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -28.364083-0.002384j
[2025-08-25 20:00:32] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -28.350833-0.003377j
[2025-08-25 20:00:38] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -28.355978+0.002931j
[2025-08-25 20:00:44] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -28.346750+0.005449j
[2025-08-25 20:00:50] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -28.349629+0.000871j
[2025-08-25 20:00:56] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -28.347813+0.000458j
[2025-08-25 20:01:01] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -28.354460+0.001230j
[2025-08-25 20:01:07] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -28.352240+0.002013j
[2025-08-25 20:01:13] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -28.352518-0.003454j
[2025-08-25 20:01:19] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -28.342240-0.002664j
[2025-08-25 20:01:25] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -28.351145-0.002037j
[2025-08-25 20:01:31] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -28.359992+0.002420j
[2025-08-25 20:01:37] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -28.353598+0.003338j
[2025-08-25 20:01:43] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -28.355776-0.001408j
[2025-08-25 20:01:48] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -28.350852-0.003850j
[2025-08-25 20:01:54] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -28.340542+0.000705j
[2025-08-25 20:02:00] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -28.350429+0.001321j
[2025-08-25 20:02:06] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -28.350246+0.001174j
[2025-08-25 20:02:12] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -28.359153+0.002374j
[2025-08-25 20:02:18] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -28.340261-0.002066j
[2025-08-25 20:02:24] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -28.344152-0.001535j
[2025-08-25 20:02:29] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -28.337992-0.000688j
[2025-08-25 20:02:35] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -28.357096+0.000928j
[2025-08-25 20:02:41] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -28.360533+0.002063j
[2025-08-25 20:02:47] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -28.353452+0.001278j
[2025-08-25 20:02:53] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -28.352523-0.000025j
[2025-08-25 20:02:59] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -28.353450+0.002613j
[2025-08-25 20:03:05] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -28.353504-0.002263j
[2025-08-25 20:03:10] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -28.340861+0.000926j
[2025-08-25 20:03:16] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -28.349210+0.002064j
[2025-08-25 20:03:16] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-25 20:03:22] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -28.359993-0.000071j
[2025-08-25 20:03:28] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -28.353034-0.002348j
[2025-08-25 20:03:34] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -28.346749-0.000920j
[2025-08-25 20:03:40] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -28.354637-0.001016j
[2025-08-25 20:03:46] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -28.357617-0.000805j
[2025-08-25 20:03:52] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -28.340940+0.001482j
[2025-08-25 20:03:57] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -28.354538+0.002557j
[2025-08-25 20:04:03] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -28.349887+0.000363j
[2025-08-25 20:04:09] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -28.340285-0.001657j
[2025-08-25 20:04:15] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -28.354962-0.002284j
[2025-08-25 20:04:21] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -28.352830+0.000068j
[2025-08-25 20:04:27] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -28.344300-0.003305j
[2025-08-25 20:04:33] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -28.340993+0.004330j
[2025-08-25 20:04:38] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -28.343498-0.003491j
[2025-08-25 20:04:44] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -28.348931-0.000370j
[2025-08-25 20:04:50] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -28.344419+0.002164j
[2025-08-25 20:04:56] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -28.347744-0.001907j
[2025-08-25 20:05:02] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -28.334420+0.000090j
[2025-08-25 20:05:08] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -28.341750-0.000139j
[2025-08-25 20:05:14] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -28.353310+0.003742j
[2025-08-25 20:05:20] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -28.343060+0.001643j
[2025-08-25 20:05:25] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -28.348728+0.000503j
[2025-08-25 20:05:31] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -28.350908+0.005266j
[2025-08-25 20:05:37] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -28.341999-0.000700j
[2025-08-25 20:05:43] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -28.345553-0.002882j
[2025-08-25 20:05:49] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -28.352318+0.001179j
[2025-08-25 20:05:55] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -28.355742-0.000473j
[2025-08-25 20:06:01] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -28.354265-0.000852j
[2025-08-25 20:06:06] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -28.342089-0.001241j
[2025-08-25 20:06:12] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -28.358044-0.003079j
[2025-08-25 20:06:18] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -28.351696+0.004520j
[2025-08-25 20:06:24] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -28.350745+0.001712j
[2025-08-25 20:06:30] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -28.350410+0.004293j
[2025-08-25 20:06:36] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -28.343571+0.000432j
[2025-08-25 20:06:42] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -28.351183+0.000126j
[2025-08-25 20:06:44] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -28.351986+0.001350j
[2025-08-25 20:06:47] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -28.342959-0.001566j
[2025-08-25 20:06:49] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -28.346374+0.000267j
[2025-08-25 20:06:52] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -28.348546+0.001234j
[2025-08-25 20:06:55] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -28.347774-0.002160j
[2025-08-25 20:06:57] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -28.353147+0.000868j
[2025-08-25 20:07:00] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -28.358476+0.000916j
[2025-08-25 20:07:03] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -28.353681+0.000244j
[2025-08-25 20:07:05] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -28.355910-0.000930j
[2025-08-25 20:07:08] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -28.355658-0.000553j
[2025-08-25 20:07:11] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -28.345503+0.002897j
[2025-08-25 20:07:13] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -28.355925+0.000581j
[2025-08-25 20:07:16] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -28.343438+0.002003j
[2025-08-25 20:07:18] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -28.347229+0.000904j
[2025-08-25 20:07:21] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -28.355260+0.002725j
[2025-08-25 20:07:21] ✅ Training completed | Restarts: 2
[2025-08-25 20:07:21] ============================================================
[2025-08-25 20:07:21] Training completed | Runtime: 6155.2s
[2025-08-25 20:07:22] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-25 20:07:22] ============================================================
