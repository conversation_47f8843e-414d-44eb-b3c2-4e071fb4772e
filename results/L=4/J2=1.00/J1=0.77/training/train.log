[2025-08-25 21:50:12] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.78/training/checkpoints/final_GCNN.pkl
[2025-08-25 21:50:12]   - 迭代次数: final
[2025-08-25 21:50:12]   - 能量: -27.948288+0.003479j ± 0.006468
[2025-08-25 21:50:12]   - 时间戳: 2025-08-25T21:49:47.594420+08:00
[2025-08-25 21:50:24] ✓ 变分状态参数已从checkpoint恢复
[2025-08-25 21:50:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-25 21:50:24] ==================================================
[2025-08-25 21:50:24] GCNN for Shastry-Sutherland Model
[2025-08-25 21:50:24] ==================================================
[2025-08-25 21:50:24] System parameters:
[2025-08-25 21:50:24]   - System size: L=4, N=64
[2025-08-25 21:50:24]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-08-25 21:50:24] --------------------------------------------------
[2025-08-25 21:50:24] Model parameters:
[2025-08-25 21:50:24]   - Number of layers = 4
[2025-08-25 21:50:24]   - Number of features = 4
[2025-08-25 21:50:24]   - Total parameters = 12572
[2025-08-25 21:50:24] --------------------------------------------------
[2025-08-25 21:50:24] Training parameters:
[2025-08-25 21:50:24]   - Learning rate: 0.01
[2025-08-25 21:50:24]   - Total iterations: 1050
[2025-08-25 21:50:24]   - Annealing cycles: 3
[2025-08-25 21:50:24]   - Initial period: 150
[2025-08-25 21:50:24]   - Period multiplier: 2.0
[2025-08-25 21:50:24]   - Temperature range: 0.0-1.0
[2025-08-25 21:50:24]   - Samples: 4096
[2025-08-25 21:50:24]   - Discarded samples: 0
[2025-08-25 21:50:24]   - Chunk size: 2048
[2025-08-25 21:50:24]   - Diagonal shift: 0.2
[2025-08-25 21:50:24]   - Gradient clipping: 1.0
[2025-08-25 21:50:24]   - Checkpoint enabled: interval=100
[2025-08-25 21:50:24]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.77/training/checkpoints
[2025-08-25 21:50:24] --------------------------------------------------
[2025-08-25 21:50:24] Device status:
[2025-08-25 21:50:24]   - Devices model: NVIDIA H200 NVL
[2025-08-25 21:50:24]   - Number of devices: 1
[2025-08-25 21:50:24]   - Sharding: True
[2025-08-25 21:50:24] ============================================================
[2025-08-25 21:51:04] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -27.537490+0.001398j
[2025-08-25 21:51:28] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -27.526337+0.003314j
[2025-08-25 21:51:34] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -27.537563+0.003315j
[2025-08-25 21:51:39] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -27.531315+0.001466j
[2025-08-25 21:51:45] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -27.543506+0.005764j
[2025-08-25 21:51:51] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -27.527693-0.003569j
[2025-08-25 21:51:57] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -27.528966-0.000873j
[2025-08-25 21:52:03] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -27.541735-0.000315j
[2025-08-25 21:52:08] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -27.532316-0.000596j
[2025-08-25 21:52:14] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -27.537605-0.001374j
[2025-08-25 21:52:20] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -27.529853+0.002442j
[2025-08-25 21:52:26] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -27.525491+0.002068j
[2025-08-25 21:52:31] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -27.537977+0.001095j
[2025-08-25 21:52:37] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -27.536604-0.002275j
[2025-08-25 21:52:43] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -27.541603-0.000822j
[2025-08-25 21:52:49] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -27.536344-0.002332j
[2025-08-25 21:52:55] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -27.541020+0.002016j
[2025-08-25 21:53:00] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -27.527344-0.000247j
[2025-08-25 21:53:06] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -27.533538+0.000885j
[2025-08-25 21:53:12] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -27.531824+0.001716j
[2025-08-25 21:53:18] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -27.545566-0.001364j
[2025-08-25 21:53:23] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -27.527096+0.001293j
[2025-08-25 21:53:29] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -27.541220-0.002835j
[2025-08-25 21:53:35] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -27.533241+0.001597j
[2025-08-25 21:53:41] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -27.537108+0.000282j
[2025-08-25 21:53:46] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -27.531391+0.000494j
[2025-08-25 21:53:52] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -27.537625+0.001325j
[2025-08-25 21:53:58] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -27.527586+0.001084j
[2025-08-25 21:54:04] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -27.547497+0.000723j
[2025-08-25 21:54:10] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -27.548230-0.000578j
[2025-08-25 21:54:15] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -27.543893-0.001467j
[2025-08-25 21:54:21] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -27.536362-0.002291j
[2025-08-25 21:54:27] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -27.535865+0.002387j
[2025-08-25 21:54:33] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -27.536601-0.000275j
[2025-08-25 21:54:38] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -27.534164-0.000546j
[2025-08-25 21:54:44] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -27.534323+0.000770j
[2025-08-25 21:54:50] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -27.538836+0.000979j
[2025-08-25 21:54:56] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -27.538923-0.001287j
[2025-08-25 21:55:02] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -27.532116-0.001715j
[2025-08-25 21:55:07] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -27.536072+0.000847j
[2025-08-25 21:55:13] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -27.535545+0.000684j
[2025-08-25 21:55:19] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -27.533727-0.000475j
[2025-08-25 21:55:25] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -27.539068-0.002237j
[2025-08-25 21:55:30] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -27.542320-0.003030j
[2025-08-25 21:55:36] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -27.536858+0.000410j
[2025-08-25 21:55:42] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -27.537850+0.001421j
[2025-08-25 21:55:48] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -27.531478-0.002592j
[2025-08-25 21:55:53] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -27.539635+0.001780j
[2025-08-25 21:55:59] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -27.530331+0.000327j
[2025-08-25 21:56:05] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -27.526948-0.003936j
[2025-08-25 21:56:11] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -27.534329-0.000272j
[2025-08-25 21:56:17] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -27.528868+0.000201j
[2025-08-25 21:56:22] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -27.533700+0.000225j
[2025-08-25 21:56:28] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -27.538406-0.003359j
[2025-08-25 21:56:34] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -27.532585-0.000750j
[2025-08-25 21:56:40] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -27.526741-0.004096j
[2025-08-25 21:56:46] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -27.535445+0.002164j
[2025-08-25 21:56:51] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -27.547348+0.005209j
[2025-08-25 21:56:57] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -27.533474-0.005676j
[2025-08-25 21:57:03] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -27.530221-0.003960j
[2025-08-25 21:57:09] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -27.531760-0.003213j
[2025-08-25 21:57:14] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -27.530051+0.002086j
[2025-08-25 21:57:20] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -27.533829-0.001060j
[2025-08-25 21:57:26] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -27.547786-0.002173j
[2025-08-25 21:57:32] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -27.528508+0.000661j
[2025-08-25 21:57:38] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -27.540888+0.001416j
[2025-08-25 21:57:43] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -27.539527-0.002969j
[2025-08-25 21:57:49] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -27.529276-0.000465j
[2025-08-25 21:57:55] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -27.530685+0.002098j
[2025-08-25 21:58:01] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -27.535704-0.001801j
[2025-08-25 21:58:06] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -27.536688+0.003680j
[2025-08-25 21:58:12] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -27.526998+0.002902j
[2025-08-25 21:58:18] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -27.530181+0.004039j
[2025-08-25 21:58:24] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -27.533509+0.002749j
[2025-08-25 21:58:30] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -27.529428+0.002213j
[2025-08-25 21:58:35] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -27.539438+0.000757j
[2025-08-25 21:58:41] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -27.540590+0.002843j
[2025-08-25 21:58:46] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -27.526787-0.000104j
[2025-08-25 21:58:52] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -27.529871-0.000914j
[2025-08-25 21:58:58] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -27.528077+0.000272j
[2025-08-25 21:59:04] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -27.543095+0.000231j
[2025-08-25 21:59:10] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -27.531109+0.000820j
[2025-08-25 21:59:16] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -27.537668+0.001861j
[2025-08-25 21:59:21] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -27.526944-0.001547j
[2025-08-25 21:59:27] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -27.532017-0.000620j
[2025-08-25 21:59:33] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -27.540761-0.001894j
[2025-08-25 21:59:39] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -27.534825-0.001407j
[2025-08-25 21:59:45] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -27.534465-0.003387j
[2025-08-25 21:59:50] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -27.541517-0.004669j
[2025-08-25 21:59:56] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -27.538411-0.002938j
[2025-08-25 22:00:02] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -27.536098+0.002503j
[2025-08-25 22:00:08] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -27.539417+0.001843j
[2025-08-25 22:00:13] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -27.532981-0.001582j
[2025-08-25 22:00:19] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -27.536388+0.000520j
[2025-08-25 22:00:25] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -27.532933-0.000153j
[2025-08-25 22:00:31] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -27.530707+0.002694j
[2025-08-25 22:00:37] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -27.541337-0.001512j
[2025-08-25 22:00:42] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -27.534899+0.001325j
[2025-08-25 22:00:48] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -27.540874+0.001204j
[2025-08-25 22:00:54] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -27.541910-0.002561j
[2025-08-25 22:00:54] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-25 22:01:00] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -27.539079-0.001697j
[2025-08-25 22:01:05] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -27.529944-0.002829j
[2025-08-25 22:01:11] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -27.536744-0.001060j
[2025-08-25 22:01:17] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -27.533182-0.001063j
[2025-08-25 22:01:23] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -27.535318+0.001111j
[2025-08-25 22:01:29] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -27.529555+0.000053j
[2025-08-25 22:01:34] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -27.542389+0.001014j
[2025-08-25 22:01:40] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -27.529361+0.000683j
[2025-08-25 22:01:46] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -27.543442+0.004566j
[2025-08-25 22:01:52] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -27.539522-0.001625j
[2025-08-25 22:01:57] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -27.532103-0.004005j
[2025-08-25 22:02:03] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -27.532121-0.001068j
[2025-08-25 22:02:09] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -27.544081-0.001094j
[2025-08-25 22:02:15] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -27.526491-0.001390j
[2025-08-25 22:02:21] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -27.545088-0.000184j
[2025-08-25 22:02:26] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -27.543416+0.000998j
[2025-08-25 22:02:32] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -27.530079+0.000981j
[2025-08-25 22:02:38] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -27.530967-0.000149j
[2025-08-25 22:02:44] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -27.531935+0.003336j
[2025-08-25 22:02:49] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -27.547196+0.000871j
[2025-08-25 22:02:55] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -27.533818+0.000125j
[2025-08-25 22:03:01] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -27.537957-0.000489j
[2025-08-25 22:03:07] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -27.540194-0.002823j
[2025-08-25 22:03:13] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -27.523959+0.001899j
[2025-08-25 22:03:18] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -27.534037+0.000617j
[2025-08-25 22:03:24] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -27.541251+0.003095j
[2025-08-25 22:03:30] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -27.532789-0.000330j
[2025-08-25 22:03:35] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -27.532039+0.000974j
[2025-08-25 22:03:41] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -27.533957-0.002622j
[2025-08-25 22:03:47] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -27.530220-0.004730j
[2025-08-25 22:03:53] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -27.534245+0.001903j
[2025-08-25 22:03:59] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -27.536947+0.000715j
[2025-08-25 22:04:05] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -27.532220-0.001283j
[2025-08-25 22:04:10] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -27.545605+0.001017j
[2025-08-25 22:04:16] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -27.534523-0.001173j
[2025-08-25 22:04:22] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -27.530582+0.000513j
[2025-08-25 22:04:28] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -27.520480-0.000240j
[2025-08-25 22:04:34] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -27.517858-0.000802j
[2025-08-25 22:04:39] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -27.531919+0.001723j
[2025-08-25 22:04:45] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -27.527103-0.003536j
[2025-08-25 22:04:51] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -27.525788+0.000655j
[2025-08-25 22:04:57] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -27.524465-0.000806j
[2025-08-25 22:05:02] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -27.533053-0.000890j
[2025-08-25 22:05:08] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -27.545661+0.004807j
[2025-08-25 22:05:14] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -27.537978-0.001013j
[2025-08-25 22:05:20] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -27.528946-0.002776j
[2025-08-25 22:05:25] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -27.543186-0.000365j
[2025-08-25 22:05:31] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -27.542669+0.006375j
[2025-08-25 22:05:37] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -27.527930+0.000692j
[2025-08-25 22:05:43] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -27.526619+0.001529j
[2025-08-25 22:05:43] RESTART #1 | Period: 300
[2025-08-25 22:05:49] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -27.538376-0.000772j
[2025-08-25 22:05:54] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -27.529430-0.000636j
[2025-08-25 22:06:00] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -27.532609-0.002327j
[2025-08-25 22:06:06] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -27.534614-0.000285j
[2025-08-25 22:06:12] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -27.543900+0.003865j
[2025-08-25 22:06:17] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -27.543881+0.000990j
[2025-08-25 22:06:23] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -27.531766+0.002910j
[2025-08-25 22:06:29] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -27.526750+0.000320j
[2025-08-25 22:06:35] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -27.547369+0.000971j
[2025-08-25 22:06:40] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -27.540684-0.002011j
[2025-08-25 22:06:46] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -27.538962-0.004474j
[2025-08-25 22:06:52] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -27.532689+0.000654j
[2025-08-25 22:06:58] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -27.542315-0.004892j
[2025-08-25 22:07:03] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -27.540122-0.003525j
[2025-08-25 22:07:09] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -27.535162+0.003469j
[2025-08-25 22:07:15] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -27.535920+0.001929j
[2025-08-25 22:07:21] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -27.534669+0.002209j
[2025-08-25 22:07:27] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -27.536771-0.001032j
[2025-08-25 22:07:32] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -27.536329-0.000895j
[2025-08-25 22:07:38] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -27.534033+0.001602j
[2025-08-25 22:07:44] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -27.543091-0.001087j
[2025-08-25 22:07:50] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -27.535123+0.002544j
[2025-08-25 22:07:55] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -27.524720+0.005819j
[2025-08-25 22:08:01] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -27.542625+0.000182j
[2025-08-25 22:08:07] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -27.532948+0.001355j
[2025-08-25 22:08:13] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -27.523944-0.003134j
[2025-08-25 22:08:19] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -27.539822-0.000158j
[2025-08-25 22:08:24] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -27.535413-0.002281j
[2025-08-25 22:08:30] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -27.529968-0.001581j
[2025-08-25 22:08:36] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -27.530972+0.000149j
[2025-08-25 22:08:42] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -27.525366-0.001722j
[2025-08-25 22:08:47] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -27.530029+0.000032j
[2025-08-25 22:08:53] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -27.535658-0.000473j
[2025-08-25 22:08:59] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -27.528368+0.003873j
[2025-08-25 22:09:05] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -27.537388-0.000915j
[2025-08-25 22:09:10] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -27.532607-0.001843j
[2025-08-25 22:09:16] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -27.538262-0.003001j
[2025-08-25 22:09:22] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -27.535868-0.002212j
[2025-08-25 22:09:28] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -27.536988+0.002249j
[2025-08-25 22:09:34] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -27.526161+0.001240j
[2025-08-25 22:09:39] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -27.535318+0.000100j
[2025-08-25 22:09:45] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -27.532655+0.001535j
[2025-08-25 22:09:51] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -27.535115-0.001322j
[2025-08-25 22:09:57] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -27.540312-0.000150j
[2025-08-25 22:10:02] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -27.537247-0.002403j
[2025-08-25 22:10:08] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -27.534097-0.005898j
[2025-08-25 22:10:14] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -27.543647-0.003915j
[2025-08-25 22:10:20] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -27.535908+0.003065j
[2025-08-25 22:10:26] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -27.531281-0.003577j
[2025-08-25 22:10:31] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -27.535556+0.001627j
[2025-08-25 22:10:31] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-25 22:10:37] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -27.530322-0.002360j
[2025-08-25 22:10:43] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -27.537466+0.003019j
[2025-08-25 22:10:49] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -27.526356+0.001065j
[2025-08-25 22:10:54] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -27.532995+0.000403j
[2025-08-25 22:11:00] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -27.533795-0.001359j
[2025-08-25 22:11:06] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -27.540677-0.001171j
[2025-08-25 22:11:12] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -27.530828-0.004486j
[2025-08-25 22:11:18] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -27.541341-0.002623j
[2025-08-25 22:11:23] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -27.525765-0.002458j
[2025-08-25 22:11:29] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -27.531695+0.001801j
[2025-08-25 22:11:35] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -27.541109+0.003285j
[2025-08-25 22:11:41] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -27.547202+0.002172j
[2025-08-25 22:11:47] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -27.518072-0.000907j
[2025-08-25 22:11:52] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -27.537200-0.001286j
[2025-08-25 22:11:58] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -27.532314+0.002595j
[2025-08-25 22:12:04] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -27.538908+0.002531j
[2025-08-25 22:12:10] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -27.538053+0.002520j
[2025-08-25 22:12:15] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -27.542376+0.002081j
[2025-08-25 22:12:21] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -27.537988+0.002507j
[2025-08-25 22:12:27] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -27.544670-0.001095j
[2025-08-25 22:12:33] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -27.540326-0.001787j
[2025-08-25 22:12:38] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -27.535236-0.001889j
[2025-08-25 22:12:44] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -27.526236+0.002261j
[2025-08-25 22:12:50] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -27.531870-0.000072j
[2025-08-25 22:12:56] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -27.529140-0.000616j
[2025-08-25 22:13:02] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -27.532028+0.002189j
[2025-08-25 22:13:07] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -27.520443-0.000527j
[2025-08-25 22:13:13] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -27.532887+0.000917j
[2025-08-25 22:13:19] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -27.529025+0.001397j
[2025-08-25 22:13:25] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -27.532449+0.000054j
[2025-08-25 22:13:30] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -27.530678+0.000749j
[2025-08-25 22:13:36] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -27.533230-0.000215j
[2025-08-25 22:13:42] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -27.546624+0.000863j
[2025-08-25 22:13:48] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -27.539414-0.001063j
[2025-08-25 22:13:54] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -27.543483+0.003332j
[2025-08-25 22:13:59] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -27.534514+0.000521j
[2025-08-25 22:14:05] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -27.535239+0.001981j
[2025-08-25 22:14:11] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -27.537409-0.002861j
[2025-08-25 22:14:17] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -27.532226+0.001014j
[2025-08-25 22:14:22] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -27.539310-0.002806j
[2025-08-25 22:14:28] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -27.538125+0.005530j
[2025-08-25 22:14:34] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -27.528311+0.000098j
[2025-08-25 22:14:40] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -27.545192-0.001680j
[2025-08-25 22:14:46] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -27.533260-0.001021j
[2025-08-25 22:14:51] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -27.529286-0.000225j
[2025-08-25 22:14:57] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -27.533126+0.000078j
[2025-08-25 22:15:03] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -27.531777+0.002390j
[2025-08-25 22:15:09] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -27.542888-0.000112j
[2025-08-25 22:15:14] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -27.536502+0.000116j
[2025-08-25 22:15:20] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -27.526947+0.003899j
[2025-08-25 22:15:26] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -27.540415-0.000732j
[2025-08-25 22:15:32] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -27.537623+0.000937j
[2025-08-25 22:15:37] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -27.533616+0.001427j
[2025-08-25 22:15:43] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -27.533062+0.001177j
[2025-08-25 22:15:49] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -27.534159+0.004381j
[2025-08-25 22:15:55] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -27.529126-0.004127j
[2025-08-25 22:16:00] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -27.535981+0.002056j
[2025-08-25 22:16:06] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -27.539649+0.000635j
[2025-08-25 22:16:12] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -27.538748+0.000258j
[2025-08-25 22:16:18] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -27.533788+0.000530j
[2025-08-25 22:16:24] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -27.532486+0.004542j
[2025-08-25 22:16:29] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -27.532017-0.003333j
[2025-08-25 22:16:35] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -27.534196+0.000261j
[2025-08-25 22:16:41] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -27.545462-0.001067j
[2025-08-25 22:16:47] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -27.544029-0.008170j
[2025-08-25 22:16:52] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -27.527265+0.002654j
[2025-08-25 22:16:58] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -27.533983+0.001187j
[2025-08-25 22:17:04] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -27.544136-0.006223j
[2025-08-25 22:17:10] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -27.535709+0.000548j
[2025-08-25 22:17:16] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -27.542142-0.000807j
[2025-08-25 22:17:21] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -27.522934+0.001768j
[2025-08-25 22:17:27] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -27.536355-0.004173j
[2025-08-25 22:17:33] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -27.531077-0.000620j
[2025-08-25 22:17:39] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -27.529280-0.002165j
[2025-08-25 22:17:44] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -27.538515+0.001101j
[2025-08-25 22:17:50] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -27.537661+0.002048j
[2025-08-25 22:17:56] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -27.539608+0.001572j
[2025-08-25 22:18:02] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -27.532435-0.000941j
[2025-08-25 22:18:08] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -27.537871+0.000670j
[2025-08-25 22:18:13] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -27.538013+0.002081j
[2025-08-25 22:18:19] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -27.535160+0.002435j
[2025-08-25 22:18:25] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -27.531533-0.000667j
[2025-08-25 22:18:31] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -27.533877-0.000933j
[2025-08-25 22:18:36] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -27.538181+0.002458j
[2025-08-25 22:18:42] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -27.525474-0.002759j
[2025-08-25 22:18:48] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -27.530213-0.000647j
[2025-08-25 22:18:54] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -27.534722-0.000997j
[2025-08-25 22:19:00] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -27.530379-0.003003j
[2025-08-25 22:19:05] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -27.536012-0.001203j
[2025-08-25 22:19:11] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -27.534487-0.001948j
[2025-08-25 22:19:17] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -27.526678+0.000317j
[2025-08-25 22:19:23] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -27.531098-0.001505j
[2025-08-25 22:19:28] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -27.540172-0.000825j
[2025-08-25 22:19:34] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -27.538969+0.002754j
[2025-08-25 22:19:40] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -27.544077+0.001719j
[2025-08-25 22:19:46] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -27.551178-0.000656j
[2025-08-25 22:19:51] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -27.531267-0.002117j
[2025-08-25 22:19:57] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -27.528851-0.001598j
[2025-08-25 22:20:03] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -27.530917+0.000455j
[2025-08-25 22:20:09] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -27.528431+0.001600j
[2025-08-25 22:20:09] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-25 22:20:15] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -27.538605-0.002259j
[2025-08-25 22:20:20] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -27.520888-0.003938j
[2025-08-25 22:20:26] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -27.535507-0.000445j
[2025-08-25 22:20:32] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -27.529397-0.003387j
[2025-08-25 22:20:38] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -27.537383-0.001462j
[2025-08-25 22:20:43] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -27.535722+0.001175j
[2025-08-25 22:20:49] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -27.531020+0.001589j
[2025-08-25 22:20:55] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -27.541059-0.001065j
[2025-08-25 22:21:01] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -27.538474+0.001746j
[2025-08-25 22:21:06] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -27.537988+0.002024j
[2025-08-25 22:21:12] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -27.532031+0.003644j
[2025-08-25 22:21:18] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -27.536692+0.001488j
[2025-08-25 22:21:24] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -27.525934+0.000261j
[2025-08-25 22:21:29] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -27.534732+0.003882j
[2025-08-25 22:21:35] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -27.532729+0.004800j
[2025-08-25 22:21:41] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -27.537471+0.001357j
[2025-08-25 22:21:47] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -27.536456-0.002899j
[2025-08-25 22:21:53] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -27.540118+0.000791j
[2025-08-25 22:21:58] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -27.535753+0.004804j
[2025-08-25 22:22:04] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -27.541466+0.001063j
[2025-08-25 22:22:10] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -27.531254+0.002612j
[2025-08-25 22:22:16] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -27.532453-0.001069j
[2025-08-25 22:22:21] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -27.530357-0.001295j
[2025-08-25 22:22:27] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -27.544489-0.001593j
[2025-08-25 22:22:33] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -27.540071+0.000596j
[2025-08-25 22:22:39] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -27.532393-0.000089j
[2025-08-25 22:22:44] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -27.544707+0.000935j
[2025-08-25 22:22:50] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -27.536180+0.002653j
[2025-08-25 22:22:56] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -27.530978+0.002062j
[2025-08-25 22:23:02] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -27.533629-0.001555j
[2025-08-25 22:23:08] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -27.550417+0.001211j
[2025-08-25 22:23:13] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -27.539072-0.002003j
[2025-08-25 22:23:19] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -27.532035-0.003193j
[2025-08-25 22:23:25] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -27.533940+0.004971j
[2025-08-25 22:23:31] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -27.534387+0.000655j
[2025-08-25 22:23:36] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -27.533444+0.003829j
[2025-08-25 22:23:42] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -27.519521+0.008230j
[2025-08-25 22:23:48] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -27.530868-0.000656j
[2025-08-25 22:23:54] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -27.533450+0.001952j
[2025-08-25 22:23:59] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -27.545744+0.002200j
[2025-08-25 22:24:05] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -27.532237-0.005822j
[2025-08-25 22:24:11] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -27.535811+0.001712j
[2025-08-25 22:24:17] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -27.527987-0.000851j
[2025-08-25 22:24:23] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -27.535916+0.002552j
[2025-08-25 22:24:28] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -27.532536+0.007277j
[2025-08-25 22:24:34] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -27.539689-0.002532j
[2025-08-25 22:24:40] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -27.529363-0.001048j
[2025-08-25 22:24:46] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -27.555313-0.002092j
[2025-08-25 22:24:51] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -27.535343-0.001668j
[2025-08-25 22:24:57] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -27.523464-0.002640j
[2025-08-25 22:25:03] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -27.536151-0.002971j
[2025-08-25 22:25:09] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -27.537446-0.001693j
[2025-08-25 22:25:15] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -27.533807+0.001567j
[2025-08-25 22:25:20] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -27.546434+0.001332j
[2025-08-25 22:25:26] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -27.529879-0.004078j
[2025-08-25 22:25:32] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -27.541810-0.001328j
[2025-08-25 22:25:38] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -27.534554+0.002837j
[2025-08-25 22:25:43] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -27.527399-0.000058j
[2025-08-25 22:25:49] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -27.526732-0.002520j
[2025-08-25 22:25:55] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -27.534500-0.003178j
[2025-08-25 22:26:01] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -27.525435+0.001900j
[2025-08-25 22:26:07] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -27.543209-0.001461j
[2025-08-25 22:26:12] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -27.535708+0.001033j
[2025-08-25 22:26:18] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -27.538244-0.003040j
[2025-08-25 22:26:24] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -27.536009+0.003474j
[2025-08-25 22:26:30] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -27.531225+0.000318j
[2025-08-25 22:26:35] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -27.538772-0.001080j
[2025-08-25 22:26:41] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -27.539670-0.000936j
[2025-08-25 22:26:47] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -27.526384+0.002662j
[2025-08-25 22:26:53] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -27.530039-0.002555j
[2025-08-25 22:26:59] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -27.529495+0.004710j
[2025-08-25 22:27:04] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -27.537034-0.002578j
[2025-08-25 22:27:10] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -27.534211-0.002470j
[2025-08-25 22:27:16] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -27.538759-0.002145j
[2025-08-25 22:27:22] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -27.536689+0.000394j
[2025-08-25 22:27:27] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -27.534268-0.005979j
[2025-08-25 22:27:33] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -27.528594+0.001837j
[2025-08-25 22:27:39] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -27.541170-0.001129j
[2025-08-25 22:27:45] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -27.533334-0.001473j
[2025-08-25 22:27:50] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -27.532327-0.000923j
[2025-08-25 22:27:56] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -27.530940-0.000225j
[2025-08-25 22:28:02] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -27.545318-0.001495j
[2025-08-25 22:28:08] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -27.533642+0.001683j
[2025-08-25 22:28:14] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -27.541241+0.004760j
[2025-08-25 22:28:19] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -27.537441-0.009552j
[2025-08-25 22:28:25] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -27.535716+0.000871j
[2025-08-25 22:28:31] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -27.534279-0.002659j
[2025-08-25 22:28:37] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -27.533522+0.002384j
[2025-08-25 22:28:42] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -27.542502-0.002666j
[2025-08-25 22:28:48] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -27.537681+0.002714j
[2025-08-25 22:28:54] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -27.537808+0.002040j
[2025-08-25 22:29:00] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -27.535854-0.002497j
[2025-08-25 22:29:06] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -27.537540+0.001134j
[2025-08-25 22:29:11] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -27.538500+0.001626j
[2025-08-25 22:29:17] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -27.540627-0.006969j
[2025-08-25 22:29:23] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -27.547824+0.000211j
[2025-08-25 22:29:29] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -27.533541-0.001358j
[2025-08-25 22:29:34] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -27.527723-0.003293j
[2025-08-25 22:29:40] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -27.546874-0.000106j
[2025-08-25 22:29:46] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -27.532662+0.001791j
[2025-08-25 22:29:46] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-25 22:29:52] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -27.534909+0.001049j
[2025-08-25 22:29:58] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -27.531895-0.001926j
[2025-08-25 22:30:03] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -27.528437+0.000241j
[2025-08-25 22:30:09] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -27.538875-0.002842j
[2025-08-25 22:30:15] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -27.537706+0.001467j
[2025-08-25 22:30:21] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -27.530291-0.005711j
[2025-08-25 22:30:26] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -27.533502+0.000093j
[2025-08-25 22:30:32] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -27.524372+0.004654j
[2025-08-25 22:30:38] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -27.539533+0.001927j
[2025-08-25 22:30:44] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -27.534411+0.000723j
[2025-08-25 22:30:50] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -27.531221+0.001546j
[2025-08-25 22:30:55] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -27.542753+0.001323j
[2025-08-25 22:31:01] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -27.540352+0.003248j
[2025-08-25 22:31:07] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -27.523495+0.000277j
[2025-08-25 22:31:13] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -27.522841-0.000633j
[2025-08-25 22:31:18] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -27.531148-0.000746j
[2025-08-25 22:31:24] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -27.539422+0.002909j
[2025-08-25 22:31:30] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -27.526876+0.002935j
[2025-08-25 22:31:36] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -27.535220+0.000152j
[2025-08-25 22:31:42] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -27.544465-0.001746j
[2025-08-25 22:31:47] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -27.538990+0.003195j
[2025-08-25 22:31:53] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -27.533858+0.001741j
[2025-08-25 22:31:59] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -27.536275-0.001960j
[2025-08-25 22:32:05] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -27.533886-0.000948j
[2025-08-25 22:32:10] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -27.531983+0.004212j
[2025-08-25 22:32:16] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -27.536792-0.001374j
[2025-08-25 22:32:22] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -27.539181+0.000844j
[2025-08-25 22:32:28] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -27.536426+0.000548j
[2025-08-25 22:32:34] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -27.537055-0.003643j
[2025-08-25 22:32:39] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -27.533430-0.000806j
[2025-08-25 22:32:45] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -27.545114-0.004797j
[2025-08-25 22:32:51] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -27.545057-0.002057j
[2025-08-25 22:32:57] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -27.536195-0.002133j
[2025-08-25 22:33:02] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -27.527135+0.001006j
[2025-08-25 22:33:08] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -27.540620+0.000767j
[2025-08-25 22:33:14] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -27.537774+0.002698j
[2025-08-25 22:33:20] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -27.542645-0.003588j
[2025-08-25 22:33:26] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -27.527394-0.001635j
[2025-08-25 22:33:31] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -27.536890-0.001411j
[2025-08-25 22:33:37] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -27.536651-0.000294j
[2025-08-25 22:33:43] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -27.536367-0.001066j
[2025-08-25 22:33:49] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -27.525856+0.001653j
[2025-08-25 22:33:55] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -27.537875+0.001092j
[2025-08-25 22:34:00] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -27.534943+0.002265j
[2025-08-25 22:34:06] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -27.557249-0.004419j
[2025-08-25 22:34:12] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -27.536172+0.001697j
[2025-08-25 22:34:18] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -27.529203+0.000289j
[2025-08-25 22:34:23] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -27.534916-0.003117j
[2025-08-25 22:34:29] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -27.532336+0.004726j
[2025-08-25 22:34:35] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -27.541227-0.005057j
[2025-08-25 22:34:35] RESTART #2 | Period: 600
[2025-08-25 22:34:41] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -27.537855+0.004569j
[2025-08-25 22:34:47] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -27.536810-0.002933j
[2025-08-25 22:34:52] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -27.535560+0.001938j
[2025-08-25 22:34:58] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -27.540700+0.003166j
[2025-08-25 22:35:04] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -27.537691+0.002038j
[2025-08-25 22:35:10] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -27.535896-0.003208j
[2025-08-25 22:35:15] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -27.532764+0.005061j
[2025-08-25 22:35:21] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -27.534445+0.001183j
[2025-08-25 22:35:27] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -27.540763+0.000118j
[2025-08-25 22:35:33] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -27.529458-0.000757j
[2025-08-25 22:35:39] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -27.532652+0.003363j
[2025-08-25 22:35:44] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -27.527574-0.004148j
[2025-08-25 22:35:50] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -27.531470-0.000305j
[2025-08-25 22:35:56] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -27.538627-0.003677j
[2025-08-25 22:36:02] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -27.535752+0.000689j
[2025-08-25 22:36:07] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -27.532251-0.002271j
[2025-08-25 22:36:13] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -27.539838-0.001803j
[2025-08-25 22:36:19] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -27.543482-0.001282j
[2025-08-25 22:36:25] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -27.526826-0.001019j
[2025-08-25 22:36:30] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -27.538052-0.000401j
[2025-08-25 22:36:36] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -27.539400+0.003262j
[2025-08-25 22:36:42] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -27.526555-0.002183j
[2025-08-25 22:36:48] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -27.537069-0.001009j
[2025-08-25 22:36:54] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -27.540298+0.000145j
[2025-08-25 22:36:59] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -27.523194-0.002301j
[2025-08-25 22:37:05] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -27.540342+0.003192j
[2025-08-25 22:37:11] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -27.538840+0.002153j
[2025-08-25 22:37:17] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -27.534744-0.003462j
[2025-08-25 22:37:22] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -27.530919-0.003175j
[2025-08-25 22:37:28] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -27.532777+0.005294j
[2025-08-25 22:37:34] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -27.530730+0.000416j
[2025-08-25 22:37:40] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -27.537945+0.001600j
[2025-08-25 22:37:46] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -27.535788-0.000896j
[2025-08-25 22:37:51] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -27.545809+0.001434j
[2025-08-25 22:37:57] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -27.546639+0.000732j
[2025-08-25 22:38:03] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -27.527187+0.001144j
[2025-08-25 22:38:09] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -27.536816-0.001090j
[2025-08-25 22:38:14] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -27.532153+0.001168j
[2025-08-25 22:38:20] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -27.532476+0.001745j
[2025-08-25 22:38:26] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -27.543354-0.003213j
[2025-08-25 22:38:32] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -27.541735+0.002036j
[2025-08-25 22:38:37] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -27.532418+0.000139j
[2025-08-25 22:38:43] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -27.527760+0.000665j
[2025-08-25 22:38:49] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -27.539324-0.000951j
[2025-08-25 22:38:55] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -27.533831-0.002204j
[2025-08-25 22:39:01] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -27.536067-0.002969j
[2025-08-25 22:39:06] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -27.549223+0.001338j
[2025-08-25 22:39:12] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -27.535647-0.002175j
[2025-08-25 22:39:18] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -27.546710-0.000321j
[2025-08-25 22:39:24] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -27.544758-0.001925j
[2025-08-25 22:39:24] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-25 22:39:29] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -27.529435-0.000253j
[2025-08-25 22:39:35] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -27.540565+0.002276j
[2025-08-25 22:39:41] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -27.543581+0.000881j
[2025-08-25 22:39:47] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -27.538787+0.005021j
[2025-08-25 22:39:52] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -27.532037-0.002164j
[2025-08-25 22:39:58] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -27.555694-0.002041j
[2025-08-25 22:40:04] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -27.540004+0.001331j
[2025-08-25 22:40:10] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -27.527078-0.001527j
[2025-08-25 22:40:16] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -27.543392-0.001355j
[2025-08-25 22:40:21] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -27.538268+0.001376j
[2025-08-25 22:40:27] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -27.542630-0.000703j
[2025-08-25 22:40:33] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -27.548751-0.002297j
[2025-08-25 22:40:39] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -27.550812+0.001610j
[2025-08-25 22:40:44] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -27.537734-0.000640j
[2025-08-25 22:40:50] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -27.531719-0.000461j
[2025-08-25 22:40:56] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -27.536319+0.000786j
[2025-08-25 22:41:02] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -27.534428+0.000955j
[2025-08-25 22:41:07] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -27.534406+0.002161j
[2025-08-25 22:41:13] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -27.544700-0.001102j
[2025-08-25 22:41:19] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -27.541972+0.001302j
[2025-08-25 22:41:25] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -27.535127-0.000448j
[2025-08-25 22:41:31] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -27.537175+0.000626j
[2025-08-25 22:41:36] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -27.538484+0.001076j
[2025-08-25 22:41:42] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -27.531657-0.001548j
[2025-08-25 22:41:48] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -27.531766+0.002013j
[2025-08-25 22:41:54] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -27.532613+0.001824j
[2025-08-25 22:42:00] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -27.522484-0.005405j
[2025-08-25 22:42:05] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -27.537692-0.003222j
[2025-08-25 22:42:11] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -27.540472+0.000775j
[2025-08-25 22:42:17] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -27.537594-0.000967j
[2025-08-25 22:42:23] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -27.542804+0.005557j
[2025-08-25 22:42:28] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -27.536023+0.000439j
[2025-08-25 22:42:34] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -27.545397+0.001423j
[2025-08-25 22:42:40] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -27.538922+0.000806j
[2025-08-25 22:42:46] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -27.545134-0.001188j
[2025-08-25 22:42:52] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -27.533268-0.000981j
[2025-08-25 22:42:57] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -27.543391+0.000023j
[2025-08-25 22:43:03] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -27.538200-0.000762j
[2025-08-25 22:43:09] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -27.546927+0.001376j
[2025-08-25 22:43:15] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -27.533729+0.000223j
[2025-08-25 22:43:20] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -27.529462+0.001287j
[2025-08-25 22:43:26] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -27.538661-0.001422j
[2025-08-25 22:43:32] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -27.532466+0.001010j
[2025-08-25 22:43:38] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -27.540686-0.001687j
[2025-08-25 22:43:44] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -27.532543-0.000980j
[2025-08-25 22:43:49] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -27.531141+0.000818j
[2025-08-25 22:43:55] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -27.536033+0.000821j
[2025-08-25 22:44:01] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -27.541116-0.003141j
[2025-08-25 22:44:07] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -27.539828-0.001350j
[2025-08-25 22:44:12] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -27.540322-0.000813j
[2025-08-25 22:44:18] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -27.535069-0.004143j
[2025-08-25 22:44:24] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -27.533320+0.001764j
[2025-08-25 22:44:30] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -27.526224-0.000001j
[2025-08-25 22:44:35] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -27.529158-0.001639j
[2025-08-25 22:44:41] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -27.528901-0.001702j
[2025-08-25 22:44:47] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -27.530254+0.002112j
[2025-08-25 22:44:53] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -27.540015-0.000271j
[2025-08-25 22:44:59] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -27.529572+0.000414j
[2025-08-25 22:45:04] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -27.522282-0.003686j
[2025-08-25 22:45:10] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -27.518194-0.001649j
[2025-08-25 22:45:16] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -27.539602-0.001091j
[2025-08-25 22:45:22] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -27.525633-0.000500j
[2025-08-25 22:45:27] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -27.534998+0.000710j
[2025-08-25 22:45:33] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -27.535580+0.001188j
[2025-08-25 22:45:39] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -27.540829-0.000484j
[2025-08-25 22:45:45] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -27.545631+0.003873j
[2025-08-25 22:45:51] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -27.542820+0.004810j
[2025-08-25 22:45:56] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -27.535057-0.000445j
[2025-08-25 22:46:02] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -27.542463-0.003788j
[2025-08-25 22:46:08] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -27.535316-0.001487j
[2025-08-25 22:46:14] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -27.530784-0.001757j
[2025-08-25 22:46:19] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -27.535728+0.003239j
[2025-08-25 22:46:25] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -27.545682-0.004257j
[2025-08-25 22:46:31] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -27.526483+0.001001j
[2025-08-25 22:46:37] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -27.542236-0.001075j
[2025-08-25 22:46:43] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -27.525906-0.000650j
[2025-08-25 22:46:48] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -27.538484-0.000255j
[2025-08-25 22:46:54] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -27.543711-0.002358j
[2025-08-25 22:47:00] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -27.534524+0.000669j
[2025-08-25 22:47:06] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -27.539548-0.001639j
[2025-08-25 22:47:11] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -27.531451-0.002953j
[2025-08-25 22:47:17] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -27.537856-0.002829j
[2025-08-25 22:47:23] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -27.540599-0.007243j
[2025-08-25 22:47:29] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -27.544124-0.001673j
[2025-08-25 22:47:35] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -27.533128-0.003729j
[2025-08-25 22:47:40] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -27.539481-0.003481j
[2025-08-25 22:47:46] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -27.525738+0.003124j
[2025-08-25 22:47:52] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -27.539592-0.000554j
[2025-08-25 22:47:58] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -27.544306+0.000601j
[2025-08-25 22:48:03] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -27.534564-0.001372j
[2025-08-25 22:48:09] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -27.543000+0.001168j
[2025-08-25 22:48:15] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -27.541476+0.003387j
[2025-08-25 22:48:21] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -27.536002-0.002441j
[2025-08-25 22:48:26] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -27.542762-0.002909j
[2025-08-25 22:48:32] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -27.541911-0.002481j
[2025-08-25 22:48:38] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -27.529805-0.000339j
[2025-08-25 22:48:44] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -27.534994+0.000120j
[2025-08-25 22:48:50] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -27.531565-0.002403j
[2025-08-25 22:48:55] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -27.533216-0.002359j
[2025-08-25 22:49:01] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -27.541217+0.000489j
[2025-08-25 22:49:01] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-25 22:49:07] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -27.544640-0.003711j
[2025-08-25 22:49:13] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -27.540573-0.001716j
[2025-08-25 22:49:18] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -27.535606+0.000350j
[2025-08-25 22:49:24] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -27.537478-0.001203j
[2025-08-25 22:49:30] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -27.544836+0.001802j
[2025-08-25 22:49:36] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -27.536866-0.001312j
[2025-08-25 22:49:42] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -27.531288+0.002586j
[2025-08-25 22:49:47] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -27.541433+0.001090j
[2025-08-25 22:49:53] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -27.524965+0.001510j
[2025-08-25 22:49:59] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -27.538827-0.002995j
[2025-08-25 22:50:05] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -27.537626+0.002704j
[2025-08-25 22:50:10] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -27.541700-0.001039j
[2025-08-25 22:50:16] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -27.525510+0.000974j
[2025-08-25 22:50:22] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -27.534327+0.001296j
[2025-08-25 22:50:28] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -27.529954-0.002085j
[2025-08-25 22:50:34] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -27.536770-0.001276j
[2025-08-25 22:50:39] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -27.527774-0.000223j
[2025-08-25 22:50:45] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -27.522971+0.004188j
[2025-08-25 22:50:51] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -27.536037-0.000841j
[2025-08-25 22:50:57] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -27.532347+0.001373j
[2025-08-25 22:51:03] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -27.531697-0.001201j
[2025-08-25 22:51:08] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -27.539069+0.002608j
[2025-08-25 22:51:14] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -27.537786-0.001721j
[2025-08-25 22:51:20] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -27.529326-0.003868j
[2025-08-25 22:51:26] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -27.536415+0.000253j
[2025-08-25 22:51:31] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -27.534567-0.001156j
[2025-08-25 22:51:37] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -27.533176+0.007103j
[2025-08-25 22:51:43] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -27.540302-0.001932j
[2025-08-25 22:51:49] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -27.542702-0.002322j
[2025-08-25 22:51:55] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -27.536960+0.002578j
[2025-08-25 22:52:00] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -27.543723-0.005667j
[2025-08-25 22:52:06] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -27.533542+0.000372j
[2025-08-25 22:52:12] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -27.534385+0.001506j
[2025-08-25 22:52:18] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -27.543458+0.000608j
[2025-08-25 22:52:23] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -27.538140-0.001493j
[2025-08-25 22:52:29] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -27.539679-0.001018j
[2025-08-25 22:52:35] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -27.529664+0.001065j
[2025-08-25 22:52:41] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -27.543365+0.002695j
[2025-08-25 22:52:47] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -27.538980-0.002138j
[2025-08-25 22:52:52] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -27.539999+0.004362j
[2025-08-25 22:52:58] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -27.542269-0.000233j
[2025-08-25 22:53:04] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -27.541359+0.001732j
[2025-08-25 22:53:10] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -27.537988+0.000166j
[2025-08-25 22:53:15] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -27.534067-0.000784j
[2025-08-25 22:53:21] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -27.533369-0.001914j
[2025-08-25 22:53:27] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -27.533132-0.002073j
[2025-08-25 22:53:33] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -27.533719+0.002797j
[2025-08-25 22:53:39] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -27.525981+0.000861j
[2025-08-25 22:53:44] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -27.541452+0.002038j
[2025-08-25 22:53:50] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -27.528664-0.000743j
[2025-08-25 22:53:56] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -27.526538-0.003449j
[2025-08-25 22:54:02] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -27.541027+0.002391j
[2025-08-25 22:54:07] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -27.516608+0.000866j
[2025-08-25 22:54:13] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -27.534600-0.002037j
[2025-08-25 22:54:19] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -27.540124+0.002803j
[2025-08-25 22:54:25] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -27.533212-0.000356j
[2025-08-25 22:54:31] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -27.542437-0.000259j
[2025-08-25 22:54:36] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -27.537387-0.000134j
[2025-08-25 22:54:42] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -27.546382+0.001351j
[2025-08-25 22:54:48] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -27.546971-0.001459j
[2025-08-25 22:54:54] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -27.528820+0.000659j
[2025-08-25 22:54:59] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -27.535594+0.002030j
[2025-08-25 22:55:05] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -27.539089-0.000411j
[2025-08-25 22:55:11] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -27.522234+0.000918j
[2025-08-25 22:55:17] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -27.531210-0.001380j
[2025-08-25 22:55:22] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -27.532759-0.000083j
[2025-08-25 22:55:28] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -27.519645-0.000445j
[2025-08-25 22:55:34] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -27.521267+0.000677j
[2025-08-25 22:55:40] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -27.534333+0.000491j
[2025-08-25 22:55:46] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -27.526905+0.001941j
[2025-08-25 22:55:51] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -27.537745+0.000455j
[2025-08-25 22:55:57] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -27.537043-0.001364j
[2025-08-25 22:56:03] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -27.531311+0.002472j
[2025-08-25 22:56:09] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -27.528335-0.000015j
[2025-08-25 22:56:14] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -27.532768-0.000918j
[2025-08-25 22:56:20] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -27.543723-0.002952j
[2025-08-25 22:56:26] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -27.526473+0.000246j
[2025-08-25 22:56:32] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -27.538324-0.000712j
[2025-08-25 22:56:37] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -27.531283-0.000632j
[2025-08-25 22:56:43] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -27.537972+0.000534j
[2025-08-25 22:56:49] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -27.540225-0.002359j
[2025-08-25 22:56:55] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -27.540870-0.001632j
[2025-08-25 22:57:01] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -27.529996+0.004700j
[2025-08-25 22:57:06] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -27.524138+0.001389j
[2025-08-25 22:57:12] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -27.546892-0.002217j
[2025-08-25 22:57:18] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -27.528787-0.002092j
[2025-08-25 22:57:24] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -27.529955+0.000061j
[2025-08-25 22:57:29] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -27.534314+0.001560j
[2025-08-25 22:57:35] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -27.533590-0.000001j
[2025-08-25 22:57:41] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -27.541074+0.002176j
[2025-08-25 22:57:47] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -27.541622+0.002043j
[2025-08-25 22:57:52] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -27.531229-0.001759j
[2025-08-25 22:57:58] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -27.538813-0.002308j
[2025-08-25 22:58:04] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -27.529509+0.000045j
[2025-08-25 22:58:10] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -27.527635+0.001390j
[2025-08-25 22:58:16] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -27.541717-0.000078j
[2025-08-25 22:58:21] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -27.535495-0.000454j
[2025-08-25 22:58:27] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -27.533068-0.001122j
[2025-08-25 22:58:33] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -27.531797-0.001180j
[2025-08-25 22:58:39] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -27.534824-0.000041j
[2025-08-25 22:58:39] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-25 22:58:44] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -27.537122-0.000021j
[2025-08-25 22:58:50] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -27.527776+0.001606j
[2025-08-25 22:58:56] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -27.536801-0.003235j
[2025-08-25 22:59:02] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -27.529271+0.000279j
[2025-08-25 22:59:07] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -27.552890-0.001365j
[2025-08-25 22:59:13] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -27.533750+0.003865j
[2025-08-25 22:59:19] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -27.535863+0.002281j
[2025-08-25 22:59:25] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -27.537261+0.000850j
[2025-08-25 22:59:31] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -27.540583+0.002446j
[2025-08-25 22:59:36] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -27.550249-0.001212j
[2025-08-25 22:59:42] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -27.538924-0.000976j
[2025-08-25 22:59:48] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -27.534179+0.000903j
[2025-08-25 22:59:54] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -27.536638-0.001457j
[2025-08-25 23:00:00] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -27.533925+0.001068j
[2025-08-25 23:00:05] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -27.535070+0.001804j
[2025-08-25 23:00:11] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -27.539297-0.000024j
[2025-08-25 23:00:17] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -27.536037-0.004743j
[2025-08-25 23:00:23] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -27.532805-0.000215j
[2025-08-25 23:00:28] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -27.532191-0.001429j
[2025-08-25 23:00:34] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -27.538010+0.000347j
[2025-08-25 23:00:40] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -27.540701+0.001757j
[2025-08-25 23:00:46] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -27.530676-0.001896j
[2025-08-25 23:00:52] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -27.533192+0.000859j
[2025-08-25 23:00:57] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -27.532248-0.001174j
[2025-08-25 23:01:03] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -27.527791+0.000979j
[2025-08-25 23:01:09] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -27.541100-0.002014j
[2025-08-25 23:01:15] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -27.535442+0.001560j
[2025-08-25 23:01:20] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -27.540447-0.000902j
[2025-08-25 23:01:26] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -27.534131-0.003539j
[2025-08-25 23:01:32] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -27.552674+0.000334j
[2025-08-25 23:01:38] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -27.534678+0.004135j
[2025-08-25 23:01:44] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -27.531869+0.002655j
[2025-08-25 23:01:49] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -27.538206-0.001319j
[2025-08-25 23:01:55] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -27.546507-0.000478j
[2025-08-25 23:02:01] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -27.543385-0.002384j
[2025-08-25 23:02:07] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -27.535494-0.000975j
[2025-08-25 23:02:12] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -27.538060+0.000124j
[2025-08-25 23:02:18] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -27.533435+0.003008j
[2025-08-25 23:02:24] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -27.538281-0.000150j
[2025-08-25 23:02:30] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -27.528957+0.002059j
[2025-08-25 23:02:35] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -27.527277-0.001706j
[2025-08-25 23:02:41] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -27.545722+0.000675j
[2025-08-25 23:02:47] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -27.547988-0.002719j
[2025-08-25 23:02:53] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -27.524388+0.002361j
[2025-08-25 23:02:59] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -27.542063+0.001175j
[2025-08-25 23:03:04] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -27.530022-0.001223j
[2025-08-25 23:03:10] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -27.534036-0.000072j
[2025-08-25 23:03:16] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -27.524733-0.000490j
[2025-08-25 23:03:22] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -27.548468-0.001471j
[2025-08-25 23:03:27] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -27.533780+0.001590j
[2025-08-25 23:03:33] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -27.536697-0.001763j
[2025-08-25 23:03:39] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -27.534466+0.003236j
[2025-08-25 23:03:45] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -27.543858-0.003386j
[2025-08-25 23:03:51] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -27.541236-0.001914j
[2025-08-25 23:03:56] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -27.538796-0.001729j
[2025-08-25 23:04:02] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -27.538934-0.000882j
[2025-08-25 23:04:08] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -27.526698-0.002185j
[2025-08-25 23:04:14] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -27.533671+0.001273j
[2025-08-25 23:04:19] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -27.542898+0.001497j
[2025-08-25 23:04:25] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -27.532454+0.000411j
[2025-08-25 23:04:31] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -27.533672+0.006381j
[2025-08-25 23:04:37] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -27.546275+0.000426j
[2025-08-25 23:04:42] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -27.540421+0.006941j
[2025-08-25 23:04:48] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -27.544508+0.001316j
[2025-08-25 23:04:54] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -27.547623-0.001665j
[2025-08-25 23:05:00] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -27.537509+0.000109j
[2025-08-25 23:05:06] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -27.537211+0.001739j
[2025-08-25 23:05:11] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -27.532501+0.001950j
[2025-08-25 23:05:17] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -27.547162-0.001838j
[2025-08-25 23:05:23] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -27.520526-0.001525j
[2025-08-25 23:05:29] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -27.541292+0.001789j
[2025-08-25 23:05:34] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -27.539149-0.002706j
[2025-08-25 23:05:40] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -27.542255-0.003040j
[2025-08-25 23:05:46] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -27.527896+0.002391j
[2025-08-25 23:05:52] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -27.540255+0.002223j
[2025-08-25 23:05:58] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -27.542321+0.003213j
[2025-08-25 23:06:03] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -27.539173+0.002707j
[2025-08-25 23:06:09] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -27.543908-0.002969j
[2025-08-25 23:06:15] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -27.529712-0.000081j
[2025-08-25 23:06:21] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -27.535938-0.004760j
[2025-08-25 23:06:26] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -27.529912-0.004382j
[2025-08-25 23:06:32] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -27.536242-0.002172j
[2025-08-25 23:06:38] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -27.537244-0.002974j
[2025-08-25 23:06:44] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -27.530255+0.001723j
[2025-08-25 23:06:50] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -27.538080-0.002177j
[2025-08-25 23:06:55] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -27.524128-0.002106j
[2025-08-25 23:07:01] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -27.544545-0.000328j
[2025-08-25 23:07:07] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -27.533104+0.001899j
[2025-08-25 23:07:13] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -27.540372+0.000410j
[2025-08-25 23:07:18] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -27.532520+0.000279j
[2025-08-25 23:07:24] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -27.539883-0.000994j
[2025-08-25 23:07:30] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -27.528564-0.001493j
[2025-08-25 23:07:36] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -27.539831-0.005858j
[2025-08-25 23:07:42] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -27.529354-0.000175j
[2025-08-25 23:07:47] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -27.530295+0.001027j
[2025-08-25 23:07:53] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -27.527330-0.001413j
[2025-08-25 23:07:59] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -27.530127-0.000546j
[2025-08-25 23:08:05] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -27.526776+0.002373j
[2025-08-25 23:08:10] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -27.542598-0.006195j
[2025-08-25 23:08:16] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -27.539886+0.001348j
[2025-08-25 23:08:16] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-25 23:08:22] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -27.534342+0.000868j
[2025-08-25 23:08:28] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -27.544312+0.001759j
[2025-08-25 23:08:34] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -27.534437-0.001782j
[2025-08-25 23:08:39] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -27.539507-0.003601j
[2025-08-25 23:08:45] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -27.531839-0.000918j
[2025-08-25 23:08:51] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -27.533631+0.000723j
[2025-08-25 23:08:57] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -27.543794+0.003844j
[2025-08-25 23:09:02] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -27.532547-0.004033j
[2025-08-25 23:09:08] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -27.533851+0.002157j
[2025-08-25 23:09:14] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -27.533494+0.001404j
[2025-08-25 23:09:20] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -27.538056-0.000137j
[2025-08-25 23:09:26] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -27.533017-0.001022j
[2025-08-25 23:09:31] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -27.534045+0.001444j
[2025-08-25 23:09:37] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -27.538857+0.004188j
[2025-08-25 23:09:43] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -27.533678-0.001288j
[2025-08-25 23:09:49] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -27.537764-0.000945j
[2025-08-25 23:09:54] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -27.540075+0.000661j
[2025-08-25 23:10:00] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -27.543332-0.000142j
[2025-08-25 23:10:06] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -27.537805+0.002640j
[2025-08-25 23:10:12] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -27.546886+0.001563j
[2025-08-25 23:10:18] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -27.536286-0.000616j
[2025-08-25 23:10:23] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -27.537245+0.002888j
[2025-08-25 23:10:29] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -27.543132+0.000699j
[2025-08-25 23:10:35] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -27.537111+0.005462j
[2025-08-25 23:10:41] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -27.545340-0.001859j
[2025-08-25 23:10:46] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -27.541252-0.001658j
[2025-08-25 23:10:52] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -27.534511-0.001055j
[2025-08-25 23:10:58] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -27.532379+0.001442j
[2025-08-25 23:11:04] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -27.529687+0.002365j
[2025-08-25 23:11:09] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -27.530126-0.001468j
[2025-08-25 23:11:15] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -27.531150+0.000646j
[2025-08-25 23:11:21] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -27.542393-0.002031j
[2025-08-25 23:11:27] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -27.538075-0.003002j
[2025-08-25 23:11:33] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -27.532693+0.000402j
[2025-08-25 23:11:38] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -27.531457+0.001790j
[2025-08-25 23:11:44] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -27.534529+0.001697j
[2025-08-25 23:11:50] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -27.539070-0.000450j
[2025-08-25 23:11:56] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -27.540624-0.003722j
[2025-08-25 23:12:01] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -27.533625-0.000923j
[2025-08-25 23:12:07] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -27.549050+0.001176j
[2025-08-25 23:12:13] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -27.538302-0.003494j
[2025-08-25 23:12:19] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -27.535262+0.001447j
[2025-08-25 23:12:25] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -27.539248-0.002851j
[2025-08-25 23:12:30] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -27.535518-0.000461j
[2025-08-25 23:12:36] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -27.536647-0.002272j
[2025-08-25 23:12:42] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -27.530290-0.003384j
[2025-08-25 23:12:48] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -27.539574-0.000190j
[2025-08-25 23:12:53] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -27.536098+0.002175j
[2025-08-25 23:12:59] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -27.537172-0.000581j
[2025-08-25 23:13:05] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -27.534494+0.000133j
[2025-08-25 23:13:11] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -27.526693+0.002507j
[2025-08-25 23:13:16] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -27.537932-0.003119j
[2025-08-25 23:13:22] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -27.536918+0.004294j
[2025-08-25 23:13:28] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -27.548444-0.000952j
[2025-08-25 23:13:34] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -27.527062-0.001685j
[2025-08-25 23:13:39] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -27.528690+0.001255j
[2025-08-25 23:13:45] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -27.535024+0.000382j
[2025-08-25 23:13:51] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -27.536001-0.001168j
[2025-08-25 23:13:57] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -27.537722+0.000463j
[2025-08-25 23:14:03] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -27.534704-0.001773j
[2025-08-25 23:14:08] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -27.530740-0.001305j
[2025-08-25 23:14:14] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -27.540967-0.000584j
[2025-08-25 23:14:20] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -27.519827+0.000507j
[2025-08-25 23:14:26] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -27.538380+0.000230j
[2025-08-25 23:14:31] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -27.529419+0.002305j
[2025-08-25 23:14:37] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -27.543434-0.001214j
[2025-08-25 23:14:43] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -27.541744+0.000731j
[2025-08-25 23:14:49] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -27.546971-0.000920j
[2025-08-25 23:14:55] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -27.542470+0.000093j
[2025-08-25 23:15:00] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -27.539872+0.002067j
[2025-08-25 23:15:06] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -27.545723-0.001347j
[2025-08-25 23:15:12] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -27.532473-0.000233j
[2025-08-25 23:15:18] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -27.542248-0.003877j
[2025-08-25 23:15:23] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -27.530867+0.001168j
[2025-08-25 23:15:29] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -27.544619-0.000221j
[2025-08-25 23:15:35] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -27.536115-0.003008j
[2025-08-25 23:15:41] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -27.536226+0.001886j
[2025-08-25 23:15:47] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -27.529585+0.001766j
[2025-08-25 23:15:52] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -27.541722-0.002600j
[2025-08-25 23:15:58] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -27.538344+0.001320j
[2025-08-25 23:16:04] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -27.538306-0.003212j
[2025-08-25 23:16:10] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -27.533367+0.000962j
[2025-08-25 23:16:15] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -27.548171+0.001757j
[2025-08-25 23:16:21] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -27.539438+0.000814j
[2025-08-25 23:16:27] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -27.534384+0.000374j
[2025-08-25 23:16:33] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -27.530558-0.001884j
[2025-08-25 23:16:39] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -27.552735-0.002462j
[2025-08-25 23:16:44] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -27.534681+0.000776j
[2025-08-25 23:16:50] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -27.537334+0.002166j
[2025-08-25 23:16:56] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -27.543117+0.000979j
[2025-08-25 23:17:02] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -27.536566-0.002686j
[2025-08-25 23:17:08] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -27.539410+0.004364j
[2025-08-25 23:17:13] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -27.528201+0.002032j
[2025-08-25 23:17:19] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -27.533222-0.003052j
[2025-08-25 23:17:25] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -27.538628-0.002003j
[2025-08-25 23:17:31] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -27.539693+0.001355j
[2025-08-25 23:17:36] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -27.537538+0.007166j
[2025-08-25 23:17:42] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -27.532861-0.000603j
[2025-08-25 23:17:48] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -27.539097-0.001456j
[2025-08-25 23:17:54] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -27.537397-0.000745j
[2025-08-25 23:17:54] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-25 23:17:59] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -27.529629+0.000088j
[2025-08-25 23:18:05] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -27.542766-0.001831j
[2025-08-25 23:18:11] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -27.529479-0.001227j
[2025-08-25 23:18:17] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -27.537142-0.001420j
[2025-08-25 23:18:23] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -27.547565-0.009408j
[2025-08-25 23:18:28] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -27.545461-0.000612j
[2025-08-25 23:18:34] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -27.534374-0.000689j
[2025-08-25 23:18:40] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -27.534024+0.002578j
[2025-08-25 23:18:46] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -27.540514-0.002494j
[2025-08-25 23:18:51] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -27.532971+0.001548j
[2025-08-25 23:18:57] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -27.534912-0.000502j
[2025-08-25 23:19:03] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -27.530205-0.000914j
[2025-08-25 23:19:09] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -27.538258+0.001070j
[2025-08-25 23:19:15] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -27.534456+0.001304j
[2025-08-25 23:19:20] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -27.533975+0.001812j
[2025-08-25 23:19:26] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -27.529305-0.000884j
[2025-08-25 23:19:32] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -27.534544-0.000925j
[2025-08-25 23:19:38] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -27.536463+0.005993j
[2025-08-25 23:19:43] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -27.545209+0.001320j
[2025-08-25 23:19:49] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -27.537375+0.001424j
[2025-08-25 23:19:55] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -27.542377+0.003253j
[2025-08-25 23:20:01] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -27.542301-0.000881j
[2025-08-25 23:20:06] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -27.536958-0.000065j
[2025-08-25 23:20:12] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -27.527237+0.005172j
[2025-08-25 23:20:18] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -27.527334+0.003146j
[2025-08-25 23:20:24] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -27.549907+0.004008j
[2025-08-25 23:20:30] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -27.532878+0.002061j
[2025-08-25 23:20:35] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -27.538847+0.000577j
[2025-08-25 23:20:41] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -27.527812-0.004074j
[2025-08-25 23:20:47] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -27.527990+0.003400j
[2025-08-25 23:20:53] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -27.525264+0.002621j
[2025-08-25 23:20:58] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -27.531871-0.004285j
[2025-08-25 23:21:04] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -27.543446+0.004950j
[2025-08-25 23:21:10] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -27.541839-0.001283j
[2025-08-25 23:21:16] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -27.536059-0.000211j
[2025-08-25 23:21:21] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -27.535107+0.000184j
[2025-08-25 23:21:27] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -27.530766-0.000024j
[2025-08-25 23:21:33] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -27.518601+0.002634j
[2025-08-25 23:21:39] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -27.526233-0.000080j
[2025-08-25 23:21:45] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -27.537075-0.002917j
[2025-08-25 23:21:50] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -27.540378-0.001120j
[2025-08-25 23:21:56] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -27.535050-0.004095j
[2025-08-25 23:22:02] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -27.522913+0.003990j
[2025-08-25 23:22:08] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -27.530704-0.000267j
[2025-08-25 23:22:13] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -27.530998+0.000400j
[2025-08-25 23:22:19] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -27.535980+0.005767j
[2025-08-25 23:22:25] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -27.535386+0.003581j
[2025-08-25 23:22:31] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -27.538442-0.002075j
[2025-08-25 23:22:37] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -27.528284-0.000214j
[2025-08-25 23:22:42] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -27.525115-0.000234j
[2025-08-25 23:22:48] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -27.532974-0.000840j
[2025-08-25 23:22:54] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -27.522520+0.001618j
[2025-08-25 23:23:00] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -27.534159-0.000296j
[2025-08-25 23:23:05] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -27.547183+0.008157j
[2025-08-25 23:23:11] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -27.531615-0.001371j
[2025-08-25 23:23:17] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -27.529355+0.001554j
[2025-08-25 23:23:23] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -27.534556-0.000610j
[2025-08-25 23:23:29] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -27.529801+0.001347j
[2025-08-25 23:23:34] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -27.534452-0.003748j
[2025-08-25 23:23:40] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -27.544443+0.001045j
[2025-08-25 23:23:46] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -27.533848+0.000056j
[2025-08-25 23:23:52] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -27.540064-0.000855j
[2025-08-25 23:23:57] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -27.536190-0.002483j
[2025-08-25 23:24:03] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -27.534051-0.000924j
[2025-08-25 23:24:09] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -27.542878+0.000577j
[2025-08-25 23:24:15] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -27.533724+0.002056j
[2025-08-25 23:24:20] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -27.531152-0.004344j
[2025-08-25 23:24:26] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -27.527472+0.001833j
[2025-08-25 23:24:32] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -27.533149-0.004043j
[2025-08-25 23:24:38] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -27.524027-0.001789j
[2025-08-25 23:24:45] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -27.542353-0.002666j
[2025-08-25 23:24:50] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -27.525697+0.002838j
[2025-08-25 23:24:56] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -27.535648+0.000151j
[2025-08-25 23:25:02] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -27.532839+0.001047j
[2025-08-25 23:25:08] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -27.540298+0.000507j
[2025-08-25 23:25:14] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -27.542472+0.002075j
[2025-08-25 23:25:20] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -27.531603-0.001538j
[2025-08-25 23:25:25] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -27.542104-0.003954j
[2025-08-25 23:25:31] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -27.537300+0.002559j
[2025-08-25 23:25:37] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -27.524228+0.000655j
[2025-08-25 23:25:43] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -27.541039-0.001153j
[2025-08-25 23:25:49] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -27.543114-0.002499j
[2025-08-25 23:25:54] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -27.536490+0.002556j
[2025-08-25 23:26:00] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -27.538189+0.000699j
[2025-08-25 23:26:06] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -27.529981+0.001896j
[2025-08-25 23:26:12] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -27.537678+0.003369j
[2025-08-25 23:26:17] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -27.534749+0.000717j
[2025-08-25 23:26:23] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -27.532240-0.001226j
[2025-08-25 23:26:29] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -27.534599-0.000094j
[2025-08-25 23:26:35] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -27.532520+0.005356j
[2025-08-25 23:26:41] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -27.548132+0.000007j
[2025-08-25 23:26:46] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -27.531636-0.000590j
[2025-08-25 23:26:52] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -27.528760-0.001401j
[2025-08-25 23:26:58] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -27.539480-0.002251j
[2025-08-25 23:27:04] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -27.535226+0.001233j
[2025-08-25 23:27:09] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -27.535546+0.004288j
[2025-08-25 23:27:15] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -27.549424-0.000561j
[2025-08-25 23:27:21] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -27.539616+0.000943j
[2025-08-25 23:27:27] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -27.545363-0.001679j
[2025-08-25 23:27:33] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -27.538817+0.003819j
[2025-08-25 23:27:33] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-25 23:27:38] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -27.533629-0.001650j
[2025-08-25 23:27:44] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -27.533376-0.001497j
[2025-08-25 23:27:50] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -27.535972-0.000521j
[2025-08-25 23:27:56] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -27.535504-0.000800j
[2025-08-25 23:28:02] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -27.538814+0.002240j
[2025-08-25 23:28:07] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -27.529900+0.002272j
[2025-08-25 23:28:13] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -27.533822+0.000278j
[2025-08-25 23:28:19] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -27.520727-0.003562j
[2025-08-25 23:28:25] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -27.544699+0.001793j
[2025-08-25 23:28:30] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -27.538322+0.000335j
[2025-08-25 23:28:36] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -27.534826-0.004234j
[2025-08-25 23:28:42] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -27.529653-0.005591j
[2025-08-25 23:28:48] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -27.530498-0.000465j
[2025-08-25 23:28:53] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -27.533266+0.001026j
[2025-08-25 23:28:59] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -27.542050-0.002341j
[2025-08-25 23:29:05] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -27.529744+0.001146j
[2025-08-25 23:29:11] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -27.545986+0.000119j
[2025-08-25 23:29:17] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -27.534590+0.001724j
[2025-08-25 23:29:22] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -27.532202-0.005442j
[2025-08-25 23:29:28] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -27.546606+0.001269j
[2025-08-25 23:29:34] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -27.538981-0.005609j
[2025-08-25 23:29:40] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -27.535381+0.000379j
[2025-08-25 23:29:45] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -27.540930-0.000001j
[2025-08-25 23:29:51] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -27.530170-0.002891j
[2025-08-25 23:29:57] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -27.536803-0.002736j
[2025-08-25 23:30:03] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -27.540030+0.000613j
[2025-08-25 23:30:09] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -27.541471-0.001204j
[2025-08-25 23:30:14] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -27.533353+0.002237j
[2025-08-25 23:30:20] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -27.528899+0.003380j
[2025-08-25 23:30:26] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -27.532553-0.002155j
[2025-08-25 23:30:32] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -27.532717+0.001087j
[2025-08-25 23:30:37] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -27.547531-0.001771j
[2025-08-25 23:30:43] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -27.527325+0.000228j
[2025-08-25 23:30:49] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -27.537249+0.000968j
[2025-08-25 23:30:55] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -27.524212-0.001546j
[2025-08-25 23:31:01] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -27.530987-0.000453j
[2025-08-25 23:31:06] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -27.535867-0.001468j
[2025-08-25 23:31:12] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -27.540421-0.002240j
[2025-08-25 23:31:18] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -27.536790+0.000736j
[2025-08-25 23:31:24] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -27.531253-0.004896j
[2025-08-25 23:31:29] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -27.537411+0.001584j
[2025-08-25 23:31:35] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -27.536888-0.000189j
[2025-08-25 23:31:41] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -27.538410+0.003180j
[2025-08-25 23:31:47] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -27.529734-0.001377j
[2025-08-25 23:31:53] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -27.541777-0.001201j
[2025-08-25 23:31:58] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -27.545565-0.000940j
[2025-08-25 23:32:04] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -27.539416-0.000617j
[2025-08-25 23:32:10] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -27.538938+0.002360j
[2025-08-25 23:32:16] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -27.534092-0.000315j
[2025-08-25 23:32:22] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -27.536829+0.005345j
[2025-08-25 23:32:22] ✅ Training completed | Restarts: 2
[2025-08-25 23:32:22] ============================================================
[2025-08-25 23:32:22] Training completed | Runtime: 6117.4s
[2025-08-25 23:32:24] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-25 23:32:24] ============================================================
