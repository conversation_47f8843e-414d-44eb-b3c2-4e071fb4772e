[2025-08-25 23:32:49] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.77/training/checkpoints/final_GCNN.pkl
[2025-08-25 23:32:49]   - 迭代次数: final
[2025-08-25 23:32:49]   - 能量: -27.536829+0.005345j ± 0.006552
[2025-08-25 23:32:49]   - 时间戳: 2025-08-25T23:32:24.008914+08:00
[2025-08-25 23:32:59] ✓ 变分状态参数已从checkpoint恢复
[2025-08-25 23:32:59] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-25 23:32:59] ==================================================
[2025-08-25 23:32:59] GCNN for Shastry-Sutherland Model
[2025-08-25 23:32:59] ==================================================
[2025-08-25 23:32:59] System parameters:
[2025-08-25 23:32:59]   - System size: L=4, N=64
[2025-08-25 23:32:59]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-08-25 23:32:59] --------------------------------------------------
[2025-08-25 23:32:59] Model parameters:
[2025-08-25 23:32:59]   - Number of layers = 4
[2025-08-25 23:32:59]   - Number of features = 4
[2025-08-25 23:32:59]   - Total parameters = 12572
[2025-08-25 23:32:59] --------------------------------------------------
[2025-08-25 23:32:59] Training parameters:
[2025-08-25 23:32:59]   - Learning rate: 0.01
[2025-08-25 23:32:59]   - Total iterations: 1050
[2025-08-25 23:32:59]   - Annealing cycles: 3
[2025-08-25 23:32:59]   - Initial period: 150
[2025-08-25 23:32:59]   - Period multiplier: 2.0
[2025-08-25 23:32:59]   - Temperature range: 0.0-1.0
[2025-08-25 23:32:59]   - Samples: 4096
[2025-08-25 23:32:59]   - Discarded samples: 0
[2025-08-25 23:32:59]   - Chunk size: 2048
[2025-08-25 23:32:59]   - Diagonal shift: 0.2
[2025-08-25 23:32:59]   - Gradient clipping: 1.0
[2025-08-25 23:32:59]   - Checkpoint enabled: interval=100
[2025-08-25 23:32:59]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.76/training/checkpoints
[2025-08-25 23:32:59] --------------------------------------------------
[2025-08-25 23:32:59] Device status:
[2025-08-25 23:32:59]   - Devices model: NVIDIA H200 NVL
[2025-08-25 23:32:59]   - Number of devices: 1
[2025-08-25 23:32:59]   - Sharding: True
[2025-08-25 23:32:59] ============================================================
[2025-08-25 23:33:37] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -27.127184-0.000436j
[2025-08-25 23:33:58] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -27.118262+0.002533j
[2025-08-25 23:34:00] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -27.131984+0.004545j
[2025-08-25 23:34:03] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -27.136600+0.002019j
[2025-08-25 23:34:06] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -27.125511+0.001746j
[2025-08-25 23:34:08] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -27.131681-0.003117j
[2025-08-25 23:34:11] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -27.139576+0.002618j
[2025-08-25 23:34:13] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -27.130400-0.000590j
[2025-08-25 23:34:16] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -27.131302+0.002756j
[2025-08-25 23:34:19] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -27.133684+0.002153j
[2025-08-25 23:34:21] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -27.125456-0.002465j
[2025-08-25 23:34:24] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -27.120976-0.000537j
[2025-08-25 23:34:26] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -27.130352+0.003658j
[2025-08-25 23:34:29] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -27.137333+0.001465j
[2025-08-25 23:34:32] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -27.124936-0.001524j
[2025-08-25 23:34:34] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -27.125787+0.002022j
[2025-08-25 23:34:37] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -27.127553+0.003108j
[2025-08-25 23:34:39] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -27.130893-0.000891j
[2025-08-25 23:34:42] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -27.131034+0.004768j
[2025-08-25 23:34:45] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -27.128423+0.001259j
[2025-08-25 23:34:47] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -27.125372+0.001798j
[2025-08-25 23:34:50] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -27.127007+0.000045j
[2025-08-25 23:34:53] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -27.132900-0.002502j
[2025-08-25 23:34:55] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -27.131115-0.002444j
[2025-08-25 23:34:58] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -27.138706-0.000673j
[2025-08-25 23:35:00] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -27.133803-0.000932j
[2025-08-25 23:35:03] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -27.119932+0.002072j
[2025-08-25 23:35:06] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -27.124063-0.001100j
[2025-08-25 23:35:08] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -27.127709+0.000458j
[2025-08-25 23:35:11] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -27.128829-0.001460j
[2025-08-25 23:35:13] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -27.115830+0.001107j
[2025-08-25 23:35:16] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -27.129749-0.000149j
[2025-08-25 23:35:19] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -27.137396+0.004317j
[2025-08-25 23:35:21] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -27.133692+0.001318j
[2025-08-25 23:35:24] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -27.131106+0.005181j
[2025-08-25 23:35:26] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -27.118929+0.000795j
[2025-08-25 23:35:29] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -27.132202+0.002404j
[2025-08-25 23:35:32] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -27.131921+0.000942j
[2025-08-25 23:35:34] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -27.135579-0.002004j
[2025-08-25 23:35:37] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -27.136829-0.003002j
[2025-08-25 23:35:40] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -27.137348-0.000341j
[2025-08-25 23:35:42] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -27.134634+0.001091j
[2025-08-25 23:35:45] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -27.136871+0.000397j
[2025-08-25 23:35:47] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -27.124732-0.001795j
[2025-08-25 23:35:50] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -27.122706-0.002052j
[2025-08-25 23:35:53] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -27.128921+0.003221j
[2025-08-25 23:35:55] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -27.119195-0.002450j
[2025-08-25 23:35:58] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -27.113883-0.003463j
[2025-08-25 23:36:00] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -27.137818+0.001220j
[2025-08-25 23:36:03] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -27.123019+0.000921j
[2025-08-25 23:36:06] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -27.133557-0.000383j
[2025-08-25 23:36:08] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -27.126527+0.001791j
[2025-08-25 23:36:11] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -27.137364-0.000100j
[2025-08-25 23:36:14] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -27.129780-0.001472j
[2025-08-25 23:36:16] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -27.136459+0.000813j
[2025-08-25 23:36:19] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -27.132039+0.000580j
[2025-08-25 23:36:21] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -27.129064+0.001851j
[2025-08-25 23:36:24] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -27.126059-0.002361j
[2025-08-25 23:36:27] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -27.139230+0.003069j
[2025-08-25 23:36:29] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -27.137550-0.000980j
[2025-08-25 23:36:32] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -27.126840+0.002597j
[2025-08-25 23:36:34] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -27.125063+0.004455j
[2025-08-25 23:36:37] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -27.140530+0.000473j
[2025-08-25 23:36:40] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -27.131998+0.000021j
[2025-08-25 23:36:42] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -27.133840+0.000906j
[2025-08-25 23:36:45] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -27.130360-0.002813j
[2025-08-25 23:36:47] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -27.139616+0.003918j
[2025-08-25 23:36:50] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -27.132256+0.002467j
[2025-08-25 23:36:53] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -27.123408+0.001213j
[2025-08-25 23:36:55] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -27.130606+0.002913j
[2025-08-25 23:36:58] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -27.136051-0.000391j
[2025-08-25 23:37:01] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -27.134510+0.000630j
[2025-08-25 23:37:03] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -27.115152-0.003599j
[2025-08-25 23:37:06] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -27.124971-0.003116j
[2025-08-25 23:37:08] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -27.120358-0.003354j
[2025-08-25 23:37:11] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -27.129435-0.001146j
[2025-08-25 23:37:14] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -27.133741-0.005239j
[2025-08-25 23:37:16] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -27.129541+0.000078j
[2025-08-25 23:37:19] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -27.128319-0.001606j
[2025-08-25 23:37:21] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -27.127752-0.002476j
[2025-08-25 23:37:24] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -27.137635+0.002943j
[2025-08-25 23:37:27] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -27.144125+0.000214j
[2025-08-25 23:37:29] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -27.128659+0.002394j
[2025-08-25 23:37:32] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -27.137719-0.002535j
[2025-08-25 23:37:35] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -27.128605+0.002901j
[2025-08-25 23:37:38] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -27.136370-0.002758j
[2025-08-25 23:37:40] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -27.134829+0.000105j
[2025-08-25 23:37:43] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -27.130868-0.001005j
[2025-08-25 23:37:46] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -27.128801+0.000105j
[2025-08-25 23:37:49] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -27.124220+0.001662j
[2025-08-25 23:37:51] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -27.124613+0.001727j
[2025-08-25 23:37:54] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -27.128817+0.002840j
[2025-08-25 23:37:57] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -27.136923+0.000534j
[2025-08-25 23:37:59] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -27.131751-0.001180j
[2025-08-25 23:38:02] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -27.130849-0.000213j
[2025-08-25 23:38:05] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -27.112258+0.001216j
[2025-08-25 23:38:08] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -27.130513+0.003366j
[2025-08-25 23:38:10] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -27.115324+0.001887j
[2025-08-25 23:38:13] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -27.141500-0.002529j
[2025-08-25 23:38:16] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -27.117213+0.000856j
[2025-08-25 23:38:17] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-25 23:38:19] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -27.130668-0.000922j
[2025-08-25 23:38:22] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -27.125073+0.001296j
[2025-08-25 23:38:25] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -27.139730+0.004340j
[2025-08-25 23:38:27] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -27.135998+0.004190j
[2025-08-25 23:38:30] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -27.129226+0.000252j
[2025-08-25 23:38:33] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -27.140164-0.001681j
[2025-08-25 23:38:35] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -27.141323+0.000501j
[2025-08-25 23:38:38] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -27.132599+0.000181j
[2025-08-25 23:38:41] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -27.133680+0.000000j
[2025-08-25 23:38:43] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -27.141636-0.000752j
[2025-08-25 23:38:46] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -27.126925-0.001256j
[2025-08-25 23:38:48] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -27.134251+0.003348j
[2025-08-25 23:38:51] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -27.128512+0.002159j
[2025-08-25 23:38:54] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -27.125126+0.002688j
[2025-08-25 23:38:56] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -27.128406+0.003702j
[2025-08-25 23:38:59] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -27.137803+0.001059j
[2025-08-25 23:39:01] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -27.131326+0.003533j
[2025-08-25 23:39:04] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -27.128499-0.002779j
[2025-08-25 23:39:07] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -27.136063-0.000423j
[2025-08-25 23:39:09] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -27.133262-0.002884j
[2025-08-25 23:39:12] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -27.130771-0.001005j
[2025-08-25 23:39:14] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -27.141836+0.003817j
[2025-08-25 23:39:17] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -27.137729+0.004327j
[2025-08-25 23:39:20] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -27.132975+0.002476j
[2025-08-25 23:39:22] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -27.140300+0.005024j
[2025-08-25 23:39:25] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -27.129206-0.001638j
[2025-08-25 23:39:28] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -27.124820+0.001065j
[2025-08-25 23:39:30] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -27.124466+0.002083j
[2025-08-25 23:39:33] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -27.133052+0.000485j
[2025-08-25 23:39:35] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -27.141925-0.002848j
[2025-08-25 23:39:38] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -27.122782-0.001304j
[2025-08-25 23:39:41] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -27.135941-0.002385j
[2025-08-25 23:39:43] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -27.128935-0.001971j
[2025-08-25 23:39:46] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -27.138333-0.001975j
[2025-08-25 23:39:48] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -27.130446-0.000525j
[2025-08-25 23:39:51] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -27.140886+0.002395j
[2025-08-25 23:39:54] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -27.131957+0.001842j
[2025-08-25 23:39:56] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -27.135359-0.000483j
[2025-08-25 23:39:59] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -27.133493+0.002152j
[2025-08-25 23:40:01] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -27.121300-0.001203j
[2025-08-25 23:40:04] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -27.132973-0.003086j
[2025-08-25 23:40:07] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -27.133742+0.000563j
[2025-08-25 23:40:09] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -27.131605-0.001795j
[2025-08-25 23:40:12] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -27.119789-0.000280j
[2025-08-25 23:40:15] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -27.126616+0.001619j
[2025-08-25 23:40:17] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -27.128033+0.002115j
[2025-08-25 23:40:20] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -27.129465+0.003138j
[2025-08-25 23:40:22] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -27.125286+0.001821j
[2025-08-25 23:40:25] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -27.135829-0.001563j
[2025-08-25 23:40:28] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -27.135345-0.003320j
[2025-08-25 23:40:28] RESTART #1 | Period: 300
[2025-08-25 23:40:30] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -27.125466+0.001006j
[2025-08-25 23:40:33] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -27.136442+0.002667j
[2025-08-25 23:40:35] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -27.134531+0.000995j
[2025-08-25 23:40:38] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -27.125338-0.001963j
[2025-08-25 23:40:41] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -27.132262+0.000164j
[2025-08-25 23:40:43] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -27.133575+0.002756j
[2025-08-25 23:40:46] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -27.138907+0.000894j
[2025-08-25 23:40:48] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -27.128421-0.001337j
[2025-08-25 23:40:51] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -27.120101-0.000579j
[2025-08-25 23:40:54] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -27.131616+0.001755j
[2025-08-25 23:40:56] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -27.131606+0.002501j
[2025-08-25 23:40:59] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -27.125438+0.000027j
[2025-08-25 23:41:02] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -27.133718+0.001382j
[2025-08-25 23:41:04] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -27.121798+0.002287j
[2025-08-25 23:41:07] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -27.127267+0.003763j
[2025-08-25 23:41:09] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -27.129095-0.000490j
[2025-08-25 23:41:12] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -27.135727+0.001653j
[2025-08-25 23:41:15] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -27.127822+0.001884j
[2025-08-25 23:41:17] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -27.133262+0.002661j
[2025-08-25 23:41:20] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -27.132029-0.001507j
[2025-08-25 23:41:22] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -27.126091+0.000608j
[2025-08-25 23:41:25] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -27.129559-0.002825j
[2025-08-25 23:41:28] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -27.123314+0.002200j
[2025-08-25 23:41:30] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -27.127614-0.006348j
[2025-08-25 23:41:33] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -27.124865-0.000585j
[2025-08-25 23:41:35] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -27.130462+0.000066j
[2025-08-25 23:41:38] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -27.127885+0.003063j
[2025-08-25 23:41:41] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -27.133117+0.002914j
[2025-08-25 23:41:43] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -27.133648-0.001496j
[2025-08-25 23:41:46] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -27.131720+0.001596j
[2025-08-25 23:41:49] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -27.128550+0.001390j
[2025-08-25 23:41:51] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -27.135920-0.001125j
[2025-08-25 23:41:54] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -27.132289-0.000474j
[2025-08-25 23:41:56] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -27.126767-0.001913j
[2025-08-25 23:41:59] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -27.132048+0.000184j
[2025-08-25 23:42:02] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -27.135895+0.000592j
[2025-08-25 23:42:04] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -27.122078+0.000094j
[2025-08-25 23:42:07] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -27.132975+0.002042j
[2025-08-25 23:42:09] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -27.134100+0.001443j
[2025-08-25 23:42:12] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -27.131096-0.000520j
[2025-08-25 23:42:15] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -27.141249+0.001639j
[2025-08-25 23:42:17] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -27.133129+0.000483j
[2025-08-25 23:42:20] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -27.135909+0.003995j
[2025-08-25 23:42:22] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -27.125972+0.000977j
[2025-08-25 23:42:25] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -27.131408+0.000376j
[2025-08-25 23:42:28] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -27.132699-0.003904j
[2025-08-25 23:42:30] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -27.121763+0.000347j
[2025-08-25 23:42:33] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -27.126040-0.001772j
[2025-08-25 23:42:36] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -27.119131-0.002707j
[2025-08-25 23:42:38] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -27.129671-0.003675j
[2025-08-25 23:42:38] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-25 23:42:41] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -27.133733-0.000403j
[2025-08-25 23:42:43] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -27.120476+0.004606j
[2025-08-25 23:42:46] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -27.128023+0.003308j
[2025-08-25 23:42:49] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -27.119141+0.000566j
[2025-08-25 23:42:51] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -27.134154+0.003788j
[2025-08-25 23:42:54] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -27.127436+0.003773j
[2025-08-25 23:42:56] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -27.119228+0.001590j
[2025-08-25 23:42:59] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -27.132455+0.001642j
[2025-08-25 23:43:02] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -27.126535+0.000612j
[2025-08-25 23:43:04] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -27.132582+0.000044j
[2025-08-25 23:43:07] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -27.122795-0.002344j
[2025-08-25 23:43:10] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -27.130936-0.002012j
[2025-08-25 23:43:12] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -27.132863-0.001215j
[2025-08-25 23:43:15] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -27.133354+0.001313j
[2025-08-25 23:43:17] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -27.122373-0.000543j
[2025-08-25 23:43:20] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -27.124732-0.001098j
[2025-08-25 23:43:23] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -27.127028-0.002672j
[2025-08-25 23:43:25] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -27.128555-0.000305j
[2025-08-25 23:43:28] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -27.121019+0.000920j
[2025-08-25 23:43:30] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -27.133980+0.001079j
[2025-08-25 23:43:33] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -27.137238-0.000272j
[2025-08-25 23:43:36] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -27.122465-0.002051j
[2025-08-25 23:43:38] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -27.130916-0.000233j
[2025-08-25 23:43:41] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -27.137928-0.000949j
[2025-08-25 23:43:43] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -27.127517-0.001449j
[2025-08-25 23:43:46] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -27.129089-0.000871j
[2025-08-25 23:43:49] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -27.134455+0.002479j
[2025-08-25 23:43:51] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -27.137844-0.000277j
[2025-08-25 23:43:54] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -27.133606+0.002669j
[2025-08-25 23:43:56] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -27.129061-0.000242j
[2025-08-25 23:43:59] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -27.138350+0.002943j
[2025-08-25 23:44:02] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -27.120522+0.005725j
[2025-08-25 23:44:04] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -27.124012+0.006861j
[2025-08-25 23:44:07] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -27.134049-0.001076j
[2025-08-25 23:44:10] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -27.127138-0.000115j
[2025-08-25 23:44:12] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -27.133133-0.000469j
[2025-08-25 23:44:15] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -27.128301+0.001303j
[2025-08-25 23:44:17] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -27.137024-0.003124j
[2025-08-25 23:44:20] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -27.131082-0.000478j
[2025-08-25 23:44:23] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -27.128452+0.001536j
[2025-08-25 23:44:25] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -27.128133-0.002523j
[2025-08-25 23:44:28] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -27.120464-0.002638j
[2025-08-25 23:44:30] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -27.137420+0.000941j
[2025-08-25 23:44:33] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -27.137305-0.001252j
[2025-08-25 23:44:36] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -27.131749-0.002298j
[2025-08-25 23:44:38] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -27.125410+0.000361j
[2025-08-25 23:44:41] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -27.129191-0.002093j
[2025-08-25 23:44:43] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -27.121272-0.000736j
[2025-08-25 23:44:46] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -27.133949+0.000180j
[2025-08-25 23:44:49] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -27.126782+0.001271j
[2025-08-25 23:44:51] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -27.134355+0.000947j
[2025-08-25 23:44:54] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -27.128325-0.001310j
[2025-08-25 23:44:57] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -27.119683+0.002454j
[2025-08-25 23:44:59] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -27.130479+0.000099j
[2025-08-25 23:45:02] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -27.133227-0.003987j
[2025-08-25 23:45:04] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -27.130218-0.000174j
[2025-08-25 23:45:07] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -27.128393+0.001575j
[2025-08-25 23:45:10] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -27.126517-0.006688j
[2025-08-25 23:45:12] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -27.129949-0.000688j
[2025-08-25 23:45:15] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -27.121365-0.001927j
[2025-08-25 23:45:17] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -27.129984-0.004732j
[2025-08-25 23:45:20] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -27.135560-0.004013j
[2025-08-25 23:45:23] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -27.140674-0.002180j
[2025-08-25 23:45:25] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -27.125521+0.001922j
[2025-08-25 23:45:28] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -27.142838+0.004182j
[2025-08-25 23:45:30] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -27.124616-0.001842j
[2025-08-25 23:45:33] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -27.134376+0.001362j
[2025-08-25 23:45:36] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -27.124817+0.003033j
[2025-08-25 23:45:38] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -27.134564+0.001588j
[2025-08-25 23:45:41] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -27.129366-0.000086j
[2025-08-25 23:45:44] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -27.122560-0.004093j
[2025-08-25 23:45:46] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -27.139454+0.001894j
[2025-08-25 23:45:49] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -27.131638+0.002305j
[2025-08-25 23:45:51] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -27.132334-0.000008j
[2025-08-25 23:45:54] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -27.138186+0.000869j
[2025-08-25 23:45:57] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -27.120972+0.000022j
[2025-08-25 23:45:59] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -27.123764+0.003887j
[2025-08-25 23:46:02] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -27.131504+0.005056j
[2025-08-25 23:46:04] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -27.138231-0.000449j
[2025-08-25 23:46:07] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -27.138388+0.001858j
[2025-08-25 23:46:10] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -27.134928+0.000478j
[2025-08-25 23:46:12] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -27.133428-0.001711j
[2025-08-25 23:46:15] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -27.129531-0.000084j
[2025-08-25 23:46:17] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -27.130254-0.001253j
[2025-08-25 23:46:20] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -27.129619-0.001301j
[2025-08-25 23:46:23] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -27.136121-0.000106j
[2025-08-25 23:46:25] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -27.152489+0.003347j
[2025-08-25 23:46:28] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -27.133498-0.002047j
[2025-08-25 23:46:31] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -27.127436-0.000200j
[2025-08-25 23:46:33] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -27.128714-0.000345j
[2025-08-25 23:46:36] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -27.129085-0.001211j
[2025-08-25 23:46:38] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -27.132885+0.003886j
[2025-08-25 23:46:41] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -27.132346-0.000977j
[2025-08-25 23:46:44] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -27.136902+0.003350j
[2025-08-25 23:46:46] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -27.127752-0.001886j
[2025-08-25 23:46:49] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -27.136362+0.002831j
[2025-08-25 23:46:51] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -27.142257+0.000947j
[2025-08-25 23:46:54] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -27.135980+0.006056j
[2025-08-25 23:46:57] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -27.117593+0.002647j
[2025-08-25 23:46:59] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -27.132661+0.001676j
[2025-08-25 23:46:59] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-25 23:47:02] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -27.133456-0.005002j
[2025-08-25 23:47:05] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -27.138676+0.000684j
[2025-08-25 23:47:07] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -27.130020+0.002550j
[2025-08-25 23:47:10] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -27.130433-0.000244j
[2025-08-25 23:47:12] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -27.131872-0.000585j
[2025-08-25 23:47:15] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -27.132889+0.000178j
[2025-08-25 23:47:18] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -27.128972+0.006122j
[2025-08-25 23:47:20] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -27.131977-0.001537j
[2025-08-25 23:47:23] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -27.135567+0.001926j
[2025-08-25 23:47:25] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -27.133675-0.000359j
[2025-08-25 23:47:28] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -27.128220+0.001584j
[2025-08-25 23:47:31] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -27.135497-0.001966j
[2025-08-25 23:47:33] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -27.131544-0.001052j
[2025-08-25 23:47:36] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -27.129531+0.001176j
[2025-08-25 23:47:38] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -27.120353+0.000427j
[2025-08-25 23:47:41] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -27.136570-0.004837j
[2025-08-25 23:47:44] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -27.134762-0.000730j
[2025-08-25 23:47:46] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -27.130585+0.000184j
[2025-08-25 23:47:49] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -27.145662+0.003498j
[2025-08-25 23:47:51] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -27.134226-0.002342j
[2025-08-25 23:47:54] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -27.136600-0.001704j
[2025-08-25 23:47:57] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -27.130049+0.004173j
[2025-08-25 23:47:59] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -27.140479+0.001166j
[2025-08-25 23:48:02] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -27.126942-0.002021j
[2025-08-25 23:48:05] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -27.116023+0.000089j
[2025-08-25 23:48:07] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -27.130752+0.000162j
[2025-08-25 23:48:10] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -27.133856-0.001645j
[2025-08-25 23:48:12] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -27.139615-0.001823j
[2025-08-25 23:48:15] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -27.137523-0.000590j
[2025-08-25 23:48:18] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -27.136934+0.002271j
[2025-08-25 23:48:20] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -27.135007-0.001537j
[2025-08-25 23:48:23] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -27.137164+0.000398j
[2025-08-25 23:48:25] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -27.123366+0.000952j
[2025-08-25 23:48:28] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -27.130353+0.000478j
[2025-08-25 23:48:31] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -27.134293-0.001244j
[2025-08-25 23:48:33] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -27.135645+0.002624j
[2025-08-25 23:48:36] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -27.141287+0.000641j
[2025-08-25 23:48:38] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -27.133400-0.000751j
[2025-08-25 23:48:41] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -27.126210+0.001332j
[2025-08-25 23:48:44] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -27.127978-0.001874j
[2025-08-25 23:48:46] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -27.126262-0.000400j
[2025-08-25 23:48:49] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -27.134471-0.002122j
[2025-08-25 23:48:52] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -27.132880-0.002293j
[2025-08-25 23:48:54] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -27.133279+0.003905j
[2025-08-25 23:48:57] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -27.126143+0.000840j
[2025-08-25 23:48:59] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -27.135974+0.001552j
[2025-08-25 23:49:02] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -27.122257+0.000393j
[2025-08-25 23:49:05] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -27.129377-0.002305j
[2025-08-25 23:49:07] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -27.134057-0.004365j
[2025-08-25 23:49:10] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -27.144558+0.000842j
[2025-08-25 23:49:12] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -27.132298+0.000933j
[2025-08-25 23:49:15] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -27.136773+0.000123j
[2025-08-25 23:49:18] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -27.127766-0.000051j
[2025-08-25 23:49:20] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -27.124619+0.000368j
[2025-08-25 23:49:23] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -27.123711-0.000980j
[2025-08-25 23:49:25] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -27.120397-0.001962j
[2025-08-25 23:49:28] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -27.139145-0.002860j
[2025-08-25 23:49:31] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -27.132749+0.001089j
[2025-08-25 23:49:33] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -27.126942+0.001559j
[2025-08-25 23:49:36] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -27.126798+0.001549j
[2025-08-25 23:49:39] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -27.130100-0.001806j
[2025-08-25 23:49:41] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -27.126135-0.002101j
[2025-08-25 23:49:44] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -27.132696+0.005008j
[2025-08-25 23:49:46] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -27.126909+0.000826j
[2025-08-25 23:49:49] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -27.127720-0.003144j
[2025-08-25 23:49:52] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -27.135506+0.003449j
[2025-08-25 23:49:54] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -27.147581+0.001531j
[2025-08-25 23:49:57] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -27.130364+0.001611j
[2025-08-25 23:49:59] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -27.142974+0.005278j
[2025-08-25 23:50:02] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -27.131473+0.000796j
[2025-08-25 23:50:05] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -27.129425-0.001122j
[2025-08-25 23:50:07] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -27.126653-0.002579j
[2025-08-25 23:50:10] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -27.140232+0.000936j
[2025-08-25 23:50:13] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -27.132305+0.000329j
[2025-08-25 23:50:15] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -27.126124-0.002024j
[2025-08-25 23:50:18] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -27.134422+0.003276j
[2025-08-25 23:50:20] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -27.130425-0.000454j
[2025-08-25 23:50:23] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -27.128022-0.002978j
[2025-08-25 23:50:26] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -27.138583+0.006602j
[2025-08-25 23:50:28] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -27.128328-0.002914j
[2025-08-25 23:50:31] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -27.122204+0.002403j
[2025-08-25 23:50:33] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -27.135420+0.000181j
[2025-08-25 23:50:36] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -27.123443+0.005862j
[2025-08-25 23:50:39] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -27.134705+0.000227j
[2025-08-25 23:50:41] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -27.134671-0.001164j
[2025-08-25 23:50:44] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -27.131900+0.004022j
[2025-08-25 23:50:46] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -27.134568+0.002503j
[2025-08-25 23:50:49] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -27.127157+0.000008j
[2025-08-25 23:50:52] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -27.128696-0.001249j
[2025-08-25 23:50:54] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -27.135380-0.000468j
[2025-08-25 23:50:57] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -27.136550-0.001862j
[2025-08-25 23:50:59] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -27.126375-0.000076j
[2025-08-25 23:51:02] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -27.135025+0.004261j
[2025-08-25 23:51:05] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -27.129871+0.000484j
[2025-08-25 23:51:07] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -27.132198-0.002877j
[2025-08-25 23:51:10] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -27.124161-0.004297j
[2025-08-25 23:51:13] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -27.143205+0.002484j
[2025-08-25 23:51:15] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -27.139838-0.001548j
[2025-08-25 23:51:18] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -27.126067+0.002755j
[2025-08-25 23:51:20] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -27.132078-0.002766j
[2025-08-25 23:51:20] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-25 23:51:23] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -27.135874-0.005208j
[2025-08-25 23:51:26] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -27.135499-0.001676j
[2025-08-25 23:51:28] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -27.135584-0.007205j
[2025-08-25 23:51:31] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -27.128156+0.000134j
[2025-08-25 23:51:33] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -27.127700+0.004759j
[2025-08-25 23:51:36] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -27.129949+0.002518j
[2025-08-25 23:51:39] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -27.124889-0.000658j
[2025-08-25 23:51:41] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -27.130243+0.003127j
[2025-08-25 23:51:44] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -27.136102+0.000981j
[2025-08-25 23:51:47] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -27.128274-0.002816j
[2025-08-25 23:51:49] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -27.129938+0.009329j
[2025-08-25 23:51:52] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -27.129165-0.000624j
[2025-08-25 23:51:54] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -27.130831+0.001119j
[2025-08-25 23:51:57] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -27.135478+0.000187j
[2025-08-25 23:52:00] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -27.126210+0.001085j
[2025-08-25 23:52:02] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -27.124408-0.000452j
[2025-08-25 23:52:05] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -27.137302-0.002701j
[2025-08-25 23:52:07] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -27.128287+0.003303j
[2025-08-25 23:52:10] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -27.128319-0.001274j
[2025-08-25 23:52:13] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -27.135079+0.001372j
[2025-08-25 23:52:15] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -27.134834+0.001728j
[2025-08-25 23:52:18] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -27.133928+0.000527j
[2025-08-25 23:52:20] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -27.128124+0.003023j
[2025-08-25 23:52:23] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -27.141414-0.002407j
[2025-08-25 23:52:26] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -27.128593+0.001698j
[2025-08-25 23:52:28] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -27.131140+0.007186j
[2025-08-25 23:52:31] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -27.120997+0.000462j
[2025-08-25 23:52:34] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -27.131321-0.002419j
[2025-08-25 23:52:36] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -27.130259-0.000819j
[2025-08-25 23:52:39] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -27.121478-0.004493j
[2025-08-25 23:52:41] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -27.129027+0.000790j
[2025-08-25 23:52:44] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -27.133733+0.003114j
[2025-08-25 23:52:47] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -27.131105-0.002593j
[2025-08-25 23:52:49] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -27.127995-0.003531j
[2025-08-25 23:52:52] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -27.136654+0.000789j
[2025-08-25 23:52:54] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -27.133624-0.001552j
[2025-08-25 23:52:57] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -27.135121+0.003323j
[2025-08-25 23:53:00] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -27.127641-0.001283j
[2025-08-25 23:53:02] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -27.124282-0.004958j
[2025-08-25 23:53:05] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -27.126636+0.002239j
[2025-08-25 23:53:07] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -27.130516+0.000677j
[2025-08-25 23:53:10] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -27.120437+0.000827j
[2025-08-25 23:53:13] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -27.135411-0.001855j
[2025-08-25 23:53:15] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -27.130752-0.002953j
[2025-08-25 23:53:18] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -27.121325+0.004006j
[2025-08-25 23:53:21] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -27.135903-0.000044j
[2025-08-25 23:53:23] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -27.135725+0.001027j
[2025-08-25 23:53:26] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -27.130876-0.003210j
[2025-08-25 23:53:28] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -27.136761-0.000616j
[2025-08-25 23:53:31] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -27.141299-0.000952j
[2025-08-25 23:53:31] RESTART #2 | Period: 600
[2025-08-25 23:53:34] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -27.141062-0.002567j
[2025-08-25 23:53:36] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -27.129656+0.001794j
[2025-08-25 23:53:39] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -27.124843+0.000273j
[2025-08-25 23:53:41] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -27.124539+0.004521j
[2025-08-25 23:53:44] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -27.131276+0.000745j
[2025-08-25 23:53:47] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -27.131661+0.003293j
[2025-08-25 23:53:49] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -27.127439+0.005273j
[2025-08-25 23:53:52] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -27.145321-0.001011j
[2025-08-25 23:53:54] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -27.143548-0.001462j
[2025-08-25 23:53:57] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -27.138404-0.000310j
[2025-08-25 23:54:00] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -27.122885-0.006583j
[2025-08-25 23:54:02] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -27.143177-0.002616j
[2025-08-25 23:54:05] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -27.132217+0.000036j
[2025-08-25 23:54:08] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -27.125194-0.004922j
[2025-08-25 23:54:10] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -27.128719+0.000278j
[2025-08-25 23:54:13] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -27.130920-0.000230j
[2025-08-25 23:54:15] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -27.126629-0.001814j
[2025-08-25 23:54:18] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -27.126233-0.000137j
[2025-08-25 23:54:21] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -27.129260-0.000090j
[2025-08-25 23:54:23] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -27.130303-0.001830j
[2025-08-25 23:54:26] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -27.124950-0.005774j
[2025-08-25 23:54:28] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -27.132609+0.000101j
[2025-08-25 23:54:31] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -27.129627-0.002552j
[2025-08-25 23:54:34] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -27.128492+0.000809j
[2025-08-25 23:54:36] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -27.126607-0.001053j
[2025-08-25 23:54:39] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -27.124744-0.000162j
[2025-08-25 23:54:41] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -27.141350-0.001801j
[2025-08-25 23:54:44] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -27.136095-0.003177j
[2025-08-25 23:54:47] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -27.134648-0.000182j
[2025-08-25 23:54:49] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -27.132547+0.003936j
[2025-08-25 23:54:52] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -27.122377+0.004200j
[2025-08-25 23:54:55] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -27.134031+0.000401j
[2025-08-25 23:54:57] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -27.132825-0.001243j
[2025-08-25 23:55:00] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -27.122522+0.000443j
[2025-08-25 23:55:02] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -27.120217+0.000086j
[2025-08-25 23:55:05] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -27.126932+0.003845j
[2025-08-25 23:55:08] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -27.128748-0.000816j
[2025-08-25 23:55:10] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -27.129258+0.001124j
[2025-08-25 23:55:13] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -27.126233+0.001034j
[2025-08-25 23:55:15] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -27.134835-0.000260j
[2025-08-25 23:55:18] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -27.132892-0.000453j
[2025-08-25 23:55:21] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -27.131174-0.002774j
[2025-08-25 23:55:23] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -27.125998-0.000823j
[2025-08-25 23:55:26] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -27.139316+0.001232j
[2025-08-25 23:55:28] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -27.130148+0.000363j
[2025-08-25 23:55:31] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -27.124313+0.000935j
[2025-08-25 23:55:34] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -27.117250+0.004694j
[2025-08-25 23:55:36] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -27.135425-0.003886j
[2025-08-25 23:55:39] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -27.135072+0.003291j
[2025-08-25 23:55:42] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -27.133501-0.000635j
[2025-08-25 23:55:42] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-25 23:55:44] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -27.132725-0.000818j
[2025-08-25 23:55:47] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -27.130940-0.000169j
[2025-08-25 23:55:49] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -27.127129-0.002291j
[2025-08-25 23:55:52] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -27.141038+0.001391j
[2025-08-25 23:55:55] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -27.130150-0.004731j
[2025-08-25 23:55:57] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -27.130631-0.000881j
[2025-08-25 23:56:00] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -27.132295-0.000487j
[2025-08-25 23:56:02] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -27.126154-0.002256j
[2025-08-25 23:56:05] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -27.122543+0.000259j
[2025-08-25 23:56:08] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -27.142318-0.001238j
[2025-08-25 23:56:10] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -27.110718+0.001693j
[2025-08-25 23:56:13] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -27.126414+0.002849j
[2025-08-25 23:56:16] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -27.122057-0.001074j
[2025-08-25 23:56:18] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -27.138799-0.001527j
[2025-08-25 23:56:21] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -27.137160-0.002055j
[2025-08-25 23:56:23] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -27.132647-0.003978j
[2025-08-25 23:56:26] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -27.131501-0.000255j
[2025-08-25 23:56:29] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -27.129397-0.001848j
[2025-08-25 23:56:31] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -27.135156-0.000553j
[2025-08-25 23:56:34] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -27.138771-0.000281j
[2025-08-25 23:56:36] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -27.132861+0.000555j
[2025-08-25 23:56:39] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -27.143376-0.000719j
[2025-08-25 23:56:42] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -27.127695-0.004187j
[2025-08-25 23:56:44] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -27.133921-0.003202j
[2025-08-25 23:56:47] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -27.141358-0.003534j
[2025-08-25 23:56:49] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -27.136940+0.000111j
[2025-08-25 23:56:52] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -27.119278+0.000241j
[2025-08-25 23:56:55] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -27.135102+0.002452j
[2025-08-25 23:56:57] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -27.128553-0.000369j
[2025-08-25 23:57:00] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -27.138166-0.005911j
[2025-08-25 23:57:03] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -27.134549-0.003469j
[2025-08-25 23:57:05] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -27.143081-0.002215j
[2025-08-25 23:57:08] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -27.122057+0.000064j
[2025-08-25 23:57:10] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -27.127394-0.000290j
[2025-08-25 23:57:13] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -27.138601+0.001604j
[2025-08-25 23:57:16] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -27.130339+0.000721j
[2025-08-25 23:57:18] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -27.147061-0.003868j
[2025-08-25 23:57:21] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -27.128222-0.000053j
[2025-08-25 23:57:23] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -27.140119+0.001041j
[2025-08-25 23:57:26] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -27.117710-0.001366j
[2025-08-25 23:57:29] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -27.122943-0.003962j
[2025-08-25 23:57:31] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -27.133760-0.000644j
[2025-08-25 23:57:34] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -27.136438+0.000533j
[2025-08-25 23:57:36] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -27.135004-0.002318j
[2025-08-25 23:57:39] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -27.129074+0.000554j
[2025-08-25 23:57:42] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -27.131129-0.001994j
[2025-08-25 23:57:44] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -27.137526-0.000597j
[2025-08-25 23:57:47] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -27.117179-0.000020j
[2025-08-25 23:57:50] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -27.131955+0.000398j
[2025-08-25 23:57:52] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -27.130949+0.002252j
[2025-08-25 23:57:55] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -27.128678+0.001823j
[2025-08-25 23:57:57] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -27.133635-0.002919j
[2025-08-25 23:58:00] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -27.122683+0.002457j
[2025-08-25 23:58:03] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -27.133737-0.001674j
[2025-08-25 23:58:05] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -27.122922-0.005633j
[2025-08-25 23:58:08] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -27.127514-0.001100j
[2025-08-25 23:58:10] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -27.134004-0.002448j
[2025-08-25 23:58:13] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -27.129253-0.003780j
[2025-08-25 23:58:16] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -27.123887-0.003339j
[2025-08-25 23:58:18] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -27.125048-0.002076j
[2025-08-25 23:58:21] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -27.138393-0.000989j
[2025-08-25 23:58:23] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -27.128383+0.002642j
[2025-08-25 23:58:26] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -27.136728+0.000646j
[2025-08-25 23:58:29] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -27.125980-0.002038j
[2025-08-25 23:58:31] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -27.124059-0.001090j
[2025-08-25 23:58:34] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -27.126117-0.003674j
[2025-08-25 23:58:37] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -27.132454-0.004837j
[2025-08-25 23:58:39] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -27.122494+0.001472j
[2025-08-25 23:58:42] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -27.138147+0.001747j
[2025-08-25 23:58:44] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -27.116024-0.003638j
[2025-08-25 23:58:47] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -27.126306+0.000277j
[2025-08-25 23:58:50] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -27.141083+0.001869j
[2025-08-25 23:58:52] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -27.119569+0.001687j
[2025-08-25 23:58:55] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -27.126165-0.001131j
[2025-08-25 23:58:57] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -27.134040-0.000389j
[2025-08-25 23:59:00] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -27.138882-0.001359j
[2025-08-25 23:59:03] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -27.132886-0.003592j
[2025-08-25 23:59:05] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -27.139977+0.001881j
[2025-08-25 23:59:08] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -27.131038+0.001478j
[2025-08-25 23:59:10] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -27.129663+0.000652j
[2025-08-25 23:59:13] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -27.126570+0.001419j
[2025-08-25 23:59:16] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -27.126247+0.000044j
[2025-08-25 23:59:18] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -27.135432+0.000580j
[2025-08-25 23:59:21] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -27.136477-0.001465j
[2025-08-25 23:59:24] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -27.130088-0.000705j
[2025-08-25 23:59:26] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -27.124364-0.001913j
[2025-08-25 23:59:29] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -27.134067+0.001056j
[2025-08-25 23:59:31] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -27.113379-0.001546j
[2025-08-25 23:59:34] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -27.136927-0.000836j
[2025-08-25 23:59:37] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -27.136087-0.000130j
[2025-08-25 23:59:39] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -27.131132-0.001067j
[2025-08-25 23:59:42] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -27.133403-0.004599j
[2025-08-25 23:59:44] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -27.137220+0.003760j
[2025-08-25 23:59:47] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -27.131631+0.001640j
[2025-08-25 23:59:50] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -27.130636-0.001166j
[2025-08-25 23:59:52] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -27.135991-0.000815j
[2025-08-25 23:59:55] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -27.134663-0.001211j
[2025-08-25 23:59:58] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -27.141830-0.002205j
[2025-08-26 00:00:00] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -27.131146+0.003681j
[2025-08-26 00:00:03] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -27.133575+0.001428j
[2025-08-26 00:00:03] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-26 00:00:05] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -27.127982+0.000491j
[2025-08-26 00:00:08] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -27.132046-0.004257j
[2025-08-26 00:00:11] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -27.124727+0.000561j
[2025-08-26 00:00:13] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -27.122549+0.001199j
[2025-08-26 00:00:16] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -27.135183-0.000771j
[2025-08-26 00:00:18] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -27.135258-0.000796j
[2025-08-26 00:00:21] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -27.128957-0.001555j
[2025-08-26 00:00:24] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -27.125376+0.003611j
[2025-08-26 00:00:26] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -27.123480+0.002399j
[2025-08-26 00:00:29] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -27.145030+0.001453j
[2025-08-26 00:00:31] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -27.133053+0.000162j
[2025-08-26 00:00:34] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -27.132447-0.002159j
[2025-08-26 00:00:37] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -27.128467+0.000689j
[2025-08-26 00:00:39] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -27.131777+0.003095j
[2025-08-26 00:00:42] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -27.130296+0.001394j
[2025-08-26 00:00:45] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -27.130098+0.002580j
[2025-08-26 00:00:47] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -27.132515+0.000467j
[2025-08-26 00:00:50] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -27.127786+0.001886j
[2025-08-26 00:00:52] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -27.130964+0.000045j
[2025-08-26 00:00:55] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -27.129277-0.000703j
[2025-08-26 00:00:58] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -27.143574-0.000363j
[2025-08-26 00:01:00] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -27.134654-0.003125j
[2025-08-26 00:01:03] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -27.129725+0.003270j
[2025-08-26 00:01:05] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -27.132132-0.001230j
[2025-08-26 00:01:08] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -27.123926+0.005182j
[2025-08-26 00:01:11] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -27.125753-0.000031j
[2025-08-26 00:01:13] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -27.130648-0.002337j
[2025-08-26 00:01:16] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -27.130063-0.000287j
[2025-08-26 00:01:18] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -27.129512-0.000488j
[2025-08-26 00:01:21] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -27.129075+0.001287j
[2025-08-26 00:01:24] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -27.130848-0.001294j
[2025-08-26 00:01:26] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -27.119137+0.002199j
[2025-08-26 00:01:29] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -27.135876+0.000877j
[2025-08-26 00:01:32] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -27.123260+0.002028j
[2025-08-26 00:01:34] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -27.134077+0.004925j
[2025-08-26 00:01:37] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -27.136499-0.003481j
[2025-08-26 00:01:39] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -27.131381+0.000292j
[2025-08-26 00:01:42] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -27.125597-0.000259j
[2025-08-26 00:01:45] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -27.126432+0.002097j
[2025-08-26 00:01:47] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -27.138234-0.002237j
[2025-08-26 00:01:50] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -27.135856+0.001420j
[2025-08-26 00:01:52] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -27.128753-0.003001j
[2025-08-26 00:01:55] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -27.134505-0.007325j
[2025-08-26 00:01:58] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -27.134362-0.002396j
[2025-08-26 00:02:00] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -27.120363+0.001162j
[2025-08-26 00:02:03] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -27.124475+0.002331j
[2025-08-26 00:02:05] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -27.129474+0.000262j
[2025-08-26 00:02:08] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -27.130140+0.001395j
[2025-08-26 00:02:11] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -27.134110+0.000771j
[2025-08-26 00:02:13] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -27.130024+0.001865j
[2025-08-26 00:02:16] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -27.126833-0.000893j
[2025-08-26 00:02:19] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -27.124649-0.001575j
[2025-08-26 00:02:21] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -27.137617+0.004017j
[2025-08-26 00:02:24] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -27.120936-0.001197j
[2025-08-26 00:02:26] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -27.131798-0.000094j
[2025-08-26 00:02:29] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -27.127596+0.000156j
[2025-08-26 00:02:32] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -27.126051-0.000447j
[2025-08-26 00:02:34] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -27.136158-0.003244j
[2025-08-26 00:02:37] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -27.133965+0.004164j
[2025-08-26 00:02:39] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -27.133538+0.003005j
[2025-08-26 00:02:42] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -27.144221-0.001018j
[2025-08-26 00:02:45] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -27.130660-0.000703j
[2025-08-26 00:02:47] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -27.130272-0.000746j
[2025-08-26 00:02:50] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -27.125449-0.000107j
[2025-08-26 00:02:52] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -27.137341-0.001864j
[2025-08-26 00:02:55] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -27.133636+0.001852j
[2025-08-26 00:02:58] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -27.139984-0.000334j
[2025-08-26 00:03:00] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -27.126224+0.005293j
[2025-08-26 00:03:03] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -27.139188-0.000806j
[2025-08-26 00:03:06] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -27.125582+0.003836j
[2025-08-26 00:03:08] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -27.128863-0.001042j
[2025-08-26 00:03:11] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -27.129907+0.000392j
[2025-08-26 00:03:13] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -27.126002+0.000629j
[2025-08-26 00:03:16] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -27.130042-0.000362j
[2025-08-26 00:03:19] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -27.138375+0.000420j
[2025-08-26 00:03:21] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -27.135464-0.000348j
[2025-08-26 00:03:24] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -27.132397+0.002833j
[2025-08-26 00:03:26] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -27.129720-0.003004j
[2025-08-26 00:03:29] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -27.127425-0.000677j
[2025-08-26 00:03:32] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -27.125100-0.006799j
[2025-08-26 00:03:34] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -27.132964+0.001373j
[2025-08-26 00:03:37] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -27.132540-0.001170j
[2025-08-26 00:03:39] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -27.143703+0.000134j
[2025-08-26 00:03:42] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -27.133137+0.002952j
[2025-08-26 00:03:45] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -27.136433-0.000315j
[2025-08-26 00:03:47] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -27.120191+0.000480j
[2025-08-26 00:03:50] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -27.124021-0.007346j
[2025-08-26 00:03:53] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -27.129790+0.003246j
[2025-08-26 00:03:55] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -27.124410-0.000075j
[2025-08-26 00:03:58] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -27.121140+0.002529j
[2025-08-26 00:04:00] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -27.127352+0.000803j
[2025-08-26 00:04:03] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -27.135326+0.002050j
[2025-08-26 00:04:06] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -27.137603+0.000188j
[2025-08-26 00:04:08] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -27.129567-0.002386j
[2025-08-26 00:04:11] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -27.138714+0.002727j
[2025-08-26 00:04:13] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -27.125588+0.003214j
[2025-08-26 00:04:16] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -27.123523-0.000560j
[2025-08-26 00:04:19] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -27.137720-0.001166j
[2025-08-26 00:04:21] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -27.135647+0.002191j
[2025-08-26 00:04:24] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -27.140590-0.001052j
[2025-08-26 00:04:24] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-26 00:04:27] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -27.126424-0.003112j
[2025-08-26 00:04:30] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -27.120209+0.000904j
[2025-08-26 00:04:33] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -27.128830-0.001934j
[2025-08-26 00:04:37] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -27.130607+0.000899j
[2025-08-26 00:04:39] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -27.126742+0.001066j
[2025-08-26 00:04:42] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -27.123369+0.001604j
[2025-08-26 00:04:44] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -27.121871-0.003163j
[2025-08-26 00:04:47] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -27.141677-0.000181j
[2025-08-26 00:04:50] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -27.117908+0.002829j
[2025-08-26 00:04:52] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -27.124524-0.001686j
[2025-08-26 00:04:55] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -27.132136-0.002962j
[2025-08-26 00:04:59] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -27.135179-0.001783j
[2025-08-26 00:05:01] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -27.127504-0.000811j
[2025-08-26 00:05:04] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -27.127900-0.000091j
[2025-08-26 00:05:07] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -27.132352+0.003341j
[2025-08-26 00:05:09] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -27.125419-0.002933j
[2025-08-26 00:05:12] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -27.131083-0.000526j
[2025-08-26 00:05:14] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -27.131430+0.002320j
[2025-08-26 00:05:17] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -27.132216+0.001684j
[2025-08-26 00:05:20] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -27.131501+0.002388j
[2025-08-26 00:05:22] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -27.136685-0.004640j
[2025-08-26 00:05:25] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -27.121584-0.001744j
[2025-08-26 00:05:27] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -27.124903-0.002489j
[2025-08-26 00:05:30] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -27.127440-0.003178j
[2025-08-26 00:05:33] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -27.135004-0.000110j
[2025-08-26 00:05:35] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -27.122486+0.001985j
[2025-08-26 00:05:38] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -27.133822-0.004928j
[2025-08-26 00:05:40] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -27.130347+0.002722j
[2025-08-26 00:05:43] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -27.131248-0.000556j
[2025-08-26 00:05:46] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -27.129941-0.001178j
[2025-08-26 00:05:48] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -27.133632+0.001399j
[2025-08-26 00:05:51] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -27.138331+0.000819j
[2025-08-26 00:05:54] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -27.138549+0.000213j
[2025-08-26 00:05:56] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -27.136958+0.002190j
[2025-08-26 00:05:59] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -27.128282-0.000318j
[2025-08-26 00:06:01] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -27.135980-0.002809j
[2025-08-26 00:06:04] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -27.127734-0.004527j
[2025-08-26 00:06:07] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -27.126677+0.002899j
[2025-08-26 00:06:09] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -27.136764-0.003362j
[2025-08-26 00:06:12] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -27.128224+0.001146j
[2025-08-26 00:06:14] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -27.133476+0.003144j
[2025-08-26 00:06:17] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -27.137372+0.000951j
[2025-08-26 00:06:20] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -27.130886+0.001985j
[2025-08-26 00:06:22] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -27.135219-0.000478j
[2025-08-26 00:06:25] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -27.125139+0.003831j
[2025-08-26 00:06:27] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -27.126987-0.000323j
[2025-08-26 00:06:30] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -27.131609+0.001383j
[2025-08-26 00:06:33] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -27.129745-0.002488j
[2025-08-26 00:06:35] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -27.132215+0.001392j
[2025-08-26 00:06:38] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -27.124792-0.000757j
[2025-08-26 00:06:41] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -27.138919+0.003325j
[2025-08-26 00:06:43] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -27.132230-0.002117j
[2025-08-26 00:06:46] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -27.131444+0.002348j
[2025-08-26 00:06:48] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -27.130586-0.001658j
[2025-08-26 00:06:51] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -27.130221+0.004389j
[2025-08-26 00:06:54] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -27.128472+0.002458j
[2025-08-26 00:06:56] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -27.139385+0.000448j
[2025-08-26 00:06:59] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -27.137403+0.000011j
[2025-08-26 00:07:01] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -27.134523+0.000408j
[2025-08-26 00:07:04] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -27.141437-0.000834j
[2025-08-26 00:07:07] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -27.124776-0.005227j
[2025-08-26 00:07:09] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -27.137629+0.000509j
[2025-08-26 00:07:12] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -27.130166-0.000370j
[2025-08-26 00:07:14] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -27.123818+0.003163j
[2025-08-26 00:07:17] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -27.139536-0.001541j
[2025-08-26 00:07:20] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -27.128595+0.000865j
[2025-08-26 00:07:22] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -27.133288+0.005878j
[2025-08-26 00:07:25] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -27.124303+0.005071j
[2025-08-26 00:07:28] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -27.126782-0.000882j
[2025-08-26 00:07:30] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -27.129683-0.003042j
[2025-08-26 00:07:33] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -27.137286-0.000097j
[2025-08-26 00:07:35] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -27.129440-0.001447j
[2025-08-26 00:07:38] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -27.128937-0.003695j
[2025-08-26 00:07:41] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -27.133932-0.001735j
[2025-08-26 00:07:43] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -27.130693-0.000310j
[2025-08-26 00:07:46] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -27.123269+0.000257j
[2025-08-26 00:07:48] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -27.128815+0.004208j
[2025-08-26 00:07:51] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -27.133086+0.003619j
[2025-08-26 00:07:54] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -27.115345-0.001359j
[2025-08-26 00:07:56] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -27.129074+0.001249j
[2025-08-26 00:07:59] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -27.134611+0.006516j
[2025-08-26 00:08:01] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -27.123587+0.001480j
[2025-08-26 00:08:04] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -27.133502-0.001473j
[2025-08-26 00:08:07] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -27.118606-0.001031j
[2025-08-26 00:08:09] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -27.133393-0.001175j
[2025-08-26 00:08:12] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -27.131355-0.002064j
[2025-08-26 00:08:14] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -27.136068+0.000030j
[2025-08-26 00:08:17] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -27.122784-0.001390j
[2025-08-26 00:08:20] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -27.127103-0.000131j
[2025-08-26 00:08:22] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -27.127315+0.001391j
[2025-08-26 00:08:25] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -27.133520-0.002092j
[2025-08-26 00:08:28] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -27.131652-0.000998j
[2025-08-26 00:08:30] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -27.139044+0.001155j
[2025-08-26 00:08:33] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -27.139630-0.001238j
[2025-08-26 00:08:35] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -27.116002+0.000607j
[2025-08-26 00:08:38] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -27.129241-0.006205j
[2025-08-26 00:08:41] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -27.128154-0.004196j
[2025-08-26 00:08:43] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -27.132082-0.001956j
[2025-08-26 00:08:46] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -27.138207-0.001340j
[2025-08-26 00:08:48] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -27.128815-0.001144j
[2025-08-26 00:08:48] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-26 00:08:51] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -27.131552+0.001211j
[2025-08-26 00:08:54] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -27.121254+0.001561j
[2025-08-26 00:08:56] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -27.132387+0.000108j
[2025-08-26 00:08:59] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -27.136547+0.003071j
[2025-08-26 00:09:02] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -27.135401-0.001487j
[2025-08-26 00:09:04] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -27.139600-0.004053j
[2025-08-26 00:09:07] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -27.138349-0.001835j
[2025-08-26 00:09:09] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -27.132469+0.003053j
[2025-08-26 00:09:12] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -27.134635+0.003743j
[2025-08-26 00:09:15] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -27.131741+0.000908j
[2025-08-26 00:09:17] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -27.128932+0.002034j
[2025-08-26 00:09:20] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -27.135556-0.000162j
[2025-08-26 00:09:22] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -27.134048-0.000023j
[2025-08-26 00:09:25] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -27.132009-0.000166j
[2025-08-26 00:09:28] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -27.122068-0.003388j
[2025-08-26 00:09:30] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -27.143208+0.002903j
[2025-08-26 00:09:33] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -27.134248+0.000082j
[2025-08-26 00:09:35] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -27.135473+0.007189j
[2025-08-26 00:09:38] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -27.128188-0.001959j
[2025-08-26 00:09:41] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -27.131071+0.003088j
[2025-08-26 00:09:43] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -27.134269-0.003718j
[2025-08-26 00:09:46] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -27.128203-0.000188j
[2025-08-26 00:09:48] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -27.124498+0.002155j
[2025-08-26 00:09:51] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -27.130183-0.002876j
[2025-08-26 00:09:54] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -27.136995-0.000199j
[2025-08-26 00:09:56] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -27.133642-0.002970j
[2025-08-26 00:09:59] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -27.137393-0.000287j
[2025-08-26 00:10:02] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -27.136570-0.000303j
[2025-08-26 00:10:04] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -27.135607-0.000625j
[2025-08-26 00:10:07] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -27.128497+0.001636j
[2025-08-26 00:10:09] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -27.133311-0.000775j
[2025-08-26 00:10:12] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -27.130476-0.002167j
[2025-08-26 00:10:15] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -27.136403+0.002499j
[2025-08-26 00:10:17] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -27.128150-0.001239j
[2025-08-26 00:10:20] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -27.129860-0.001517j
[2025-08-26 00:10:22] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -27.128916-0.001937j
[2025-08-26 00:10:25] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -27.132700+0.000791j
[2025-08-26 00:10:28] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -27.138238-0.000599j
[2025-08-26 00:10:30] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -27.123206+0.003976j
[2025-08-26 00:10:33] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -27.131517-0.000115j
[2025-08-26 00:10:35] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -27.137217-0.000647j
[2025-08-26 00:10:38] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -27.121570+0.003813j
[2025-08-26 00:10:41] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -27.123556-0.002455j
[2025-08-26 00:10:43] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -27.143493+0.001911j
[2025-08-26 00:10:46] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -27.129583-0.003080j
[2025-08-26 00:10:49] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -27.132164+0.004470j
[2025-08-26 00:10:51] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -27.125184+0.000760j
[2025-08-26 00:10:54] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -27.117850-0.002322j
[2025-08-26 00:10:56] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -27.131217+0.002971j
[2025-08-26 00:10:59] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -27.138217-0.007519j
[2025-08-26 00:11:02] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -27.129611-0.004137j
[2025-08-26 00:11:04] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -27.126001-0.000277j
[2025-08-26 00:11:07] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -27.136779-0.000236j
[2025-08-26 00:11:09] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -27.137249+0.004684j
[2025-08-26 00:11:12] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -27.135866+0.003632j
[2025-08-26 00:11:15] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -27.128601-0.000259j
[2025-08-26 00:11:17] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -27.126270-0.000391j
[2025-08-26 00:11:20] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -27.134517+0.002026j
[2025-08-26 00:11:23] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -27.134499+0.001818j
[2025-08-26 00:11:25] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -27.132563-0.001769j
[2025-08-26 00:11:28] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -27.135013+0.002746j
[2025-08-26 00:11:30] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -27.132887-0.000734j
[2025-08-26 00:11:33] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -27.118783-0.002354j
[2025-08-26 00:11:36] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -27.131555+0.001029j
[2025-08-26 00:11:38] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -27.126465+0.002414j
[2025-08-26 00:11:41] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -27.145616-0.000070j
[2025-08-26 00:11:43] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -27.124530+0.000920j
[2025-08-26 00:11:46] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -27.134047+0.001841j
[2025-08-26 00:11:49] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -27.129726-0.005691j
[2025-08-26 00:11:51] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -27.125561+0.000497j
[2025-08-26 00:11:54] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -27.125476+0.004488j
[2025-08-26 00:11:56] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -27.122348+0.000777j
[2025-08-26 00:11:59] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -27.135357+0.000779j
[2025-08-26 00:12:02] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -27.131604+0.004399j
[2025-08-26 00:12:04] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -27.130249-0.000190j
[2025-08-26 00:12:07] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -27.125049+0.000357j
[2025-08-26 00:12:10] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -27.137222+0.000319j
[2025-08-26 00:12:12] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -27.137966+0.001085j
[2025-08-26 00:12:15] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -27.132533+0.001667j
[2025-08-26 00:12:17] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -27.126798-0.002636j
[2025-08-26 00:12:20] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -27.146502-0.000936j
[2025-08-26 00:12:23] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -27.136545+0.002779j
[2025-08-26 00:12:25] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -27.128613-0.000633j
[2025-08-26 00:12:28] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -27.138110-0.001511j
[2025-08-26 00:12:30] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -27.121853+0.001546j
[2025-08-26 00:12:33] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -27.127685-0.000471j
[2025-08-26 00:12:36] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -27.126327-0.001615j
[2025-08-26 00:12:38] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -27.127978-0.001423j
[2025-08-26 00:12:41] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -27.130665+0.001773j
[2025-08-26 00:12:43] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -27.133003-0.002526j
[2025-08-26 00:12:46] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -27.132175-0.000075j
[2025-08-26 00:12:49] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -27.130970+0.001237j
[2025-08-26 00:12:51] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -27.130204-0.005524j
[2025-08-26 00:12:54] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -27.133281-0.007730j
[2025-08-26 00:12:57] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -27.130135+0.002682j
[2025-08-26 00:12:59] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -27.134764+0.001005j
[2025-08-26 00:13:02] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -27.133545+0.000686j
[2025-08-26 00:13:04] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -27.128031-0.002596j
[2025-08-26 00:13:07] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -27.126919+0.001883j
[2025-08-26 00:13:10] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -27.130668+0.004272j
[2025-08-26 00:13:10] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-26 00:13:12] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -27.129261+0.002479j
[2025-08-26 00:13:15] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -27.126861+0.000282j
[2025-08-26 00:13:17] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -27.137183-0.002431j
[2025-08-26 00:13:20] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -27.125656-0.002006j
[2025-08-26 00:13:23] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -27.136760+0.001390j
[2025-08-26 00:13:25] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -27.127145-0.002482j
[2025-08-26 00:13:28] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -27.131370-0.002902j
[2025-08-26 00:13:30] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -27.126332+0.002084j
[2025-08-26 00:13:33] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -27.135403-0.002621j
[2025-08-26 00:13:36] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -27.119365-0.002224j
[2025-08-26 00:13:38] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -27.130317+0.000003j
[2025-08-26 00:13:41] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -27.146515-0.003041j
[2025-08-26 00:13:44] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -27.136201-0.001507j
[2025-08-26 00:13:46] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -27.131053+0.004393j
[2025-08-26 00:13:49] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -27.133281+0.001391j
[2025-08-26 00:13:51] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -27.130609+0.002686j
[2025-08-26 00:13:54] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -27.132422-0.001692j
[2025-08-26 00:13:57] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -27.140925-0.002876j
[2025-08-26 00:13:59] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -27.132263+0.003460j
[2025-08-26 00:14:02] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -27.123202+0.001670j
[2025-08-26 00:14:04] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -27.130126-0.002672j
[2025-08-26 00:14:07] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -27.137399-0.001021j
[2025-08-26 00:14:10] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -27.136560-0.000531j
[2025-08-26 00:14:12] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -27.139277-0.001431j
[2025-08-26 00:14:15] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -27.132559+0.000037j
[2025-08-26 00:14:17] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -27.136189+0.003310j
[2025-08-26 00:14:20] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -27.131263-0.003259j
[2025-08-26 00:14:23] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -27.132949-0.000340j
[2025-08-26 00:14:25] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -27.123916-0.004137j
[2025-08-26 00:14:28] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -27.132505+0.002021j
[2025-08-26 00:14:31] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -27.129682+0.001371j
[2025-08-26 00:14:33] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -27.127043+0.000469j
[2025-08-26 00:14:36] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -27.135695-0.001353j
[2025-08-26 00:14:38] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -27.123685+0.001051j
[2025-08-26 00:14:41] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -27.135016+0.002166j
[2025-08-26 00:14:44] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -27.123008-0.001975j
[2025-08-26 00:14:46] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -27.135583+0.003207j
[2025-08-26 00:14:49] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -27.126856+0.001066j
[2025-08-26 00:14:51] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -27.128233+0.004208j
[2025-08-26 00:14:54] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -27.129010+0.003394j
[2025-08-26 00:14:57] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -27.121412-0.002069j
[2025-08-26 00:14:59] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -27.130798-0.000600j
[2025-08-26 00:15:02] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -27.123932-0.000746j
[2025-08-26 00:15:04] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -27.135786-0.002117j
[2025-08-26 00:15:07] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -27.141247-0.001013j
[2025-08-26 00:15:10] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -27.134291+0.001594j
[2025-08-26 00:15:12] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -27.136439+0.000152j
[2025-08-26 00:15:15] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -27.169253-0.012591j
[2025-08-26 00:15:18] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -27.130532-0.004338j
[2025-08-26 00:15:20] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -27.126718-0.001033j
[2025-08-26 00:15:23] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -27.130516-0.001794j
[2025-08-26 00:15:25] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -27.136685-0.000217j
[2025-08-26 00:15:28] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -27.130909-0.000136j
[2025-08-26 00:15:31] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -27.132847+0.001941j
[2025-08-26 00:15:33] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -27.129079+0.002264j
[2025-08-26 00:15:36] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -27.116107+0.002217j
[2025-08-26 00:15:38] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -27.140129-0.000793j
[2025-08-26 00:15:41] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -27.137699+0.005969j
[2025-08-26 00:15:44] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -27.143267+0.003217j
[2025-08-26 00:15:46] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -27.138773-0.003540j
[2025-08-26 00:15:49] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -27.137977+0.001314j
[2025-08-26 00:15:51] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -27.137809+0.000182j
[2025-08-26 00:15:54] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -27.123611-0.000102j
[2025-08-26 00:15:57] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -27.131353-0.001764j
[2025-08-26 00:15:59] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -27.145663+0.000200j
[2025-08-26 00:16:02] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -27.136453+0.002005j
[2025-08-26 00:16:05] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -27.128952-0.002022j
[2025-08-26 00:16:07] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -27.132690+0.001332j
[2025-08-26 00:16:10] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -27.133071+0.000115j
[2025-08-26 00:16:12] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -27.134456-0.000862j
[2025-08-26 00:16:15] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -27.127907+0.000817j
[2025-08-26 00:16:18] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -27.131969-0.000460j
[2025-08-26 00:16:20] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -27.141878-0.003046j
[2025-08-26 00:16:23] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -27.134513+0.001003j
[2025-08-26 00:16:25] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -27.140204+0.000963j
[2025-08-26 00:16:28] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -27.133223-0.005224j
[2025-08-26 00:16:31] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -27.134730+0.001326j
[2025-08-26 00:16:33] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -27.136632-0.003629j
[2025-08-26 00:16:36] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -27.140490+0.001254j
[2025-08-26 00:16:38] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -27.113325-0.000334j
[2025-08-26 00:16:41] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -27.124758+0.000118j
[2025-08-26 00:16:44] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -27.133185+0.004312j
[2025-08-26 00:16:46] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -27.127227+0.001765j
[2025-08-26 00:16:49] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -27.121973+0.002176j
[2025-08-26 00:16:52] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -27.138298-0.002551j
[2025-08-26 00:16:54] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -27.147125+0.000360j
[2025-08-26 00:16:57] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -27.142094+0.001539j
[2025-08-26 00:16:59] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -27.135271-0.003319j
[2025-08-26 00:17:02] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -27.142779+0.002056j
[2025-08-26 00:17:05] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -27.129786+0.002269j
[2025-08-26 00:17:07] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -27.142469-0.000566j
[2025-08-26 00:17:10] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -27.134313-0.002076j
[2025-08-26 00:17:12] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -27.137373-0.000402j
[2025-08-26 00:17:15] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -27.126284+0.001047j
[2025-08-26 00:17:18] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -27.137347-0.002252j
[2025-08-26 00:17:20] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -27.127825-0.003416j
[2025-08-26 00:17:23] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -27.134727-0.001545j
[2025-08-26 00:17:25] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -27.135470+0.001728j
[2025-08-26 00:17:28] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -27.133396+0.002018j
[2025-08-26 00:17:31] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -27.122771+0.004879j
[2025-08-26 00:17:31] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-26 00:17:33] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -27.133172+0.004229j
[2025-08-26 00:17:36] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -27.127715+0.000834j
[2025-08-26 00:17:39] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -27.128844-0.003177j
[2025-08-26 00:17:41] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -27.129226-0.001802j
[2025-08-26 00:17:44] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -27.133311-0.001731j
[2025-08-26 00:17:46] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -27.136535+0.002121j
[2025-08-26 00:17:49] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -27.129909+0.001042j
[2025-08-26 00:17:52] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -27.131806+0.001003j
[2025-08-26 00:17:54] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -27.136538-0.000677j
[2025-08-26 00:17:57] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -27.148877-0.000180j
[2025-08-26 00:17:59] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -27.123393-0.001575j
[2025-08-26 00:18:02] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -27.128505+0.002407j
[2025-08-26 00:18:05] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -27.132348-0.001702j
[2025-08-26 00:18:07] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -27.144639+0.000959j
[2025-08-26 00:18:10] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -27.131598+0.003269j
[2025-08-26 00:18:13] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -27.136898-0.000105j
[2025-08-26 00:18:15] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -27.142107-0.003835j
[2025-08-26 00:18:18] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -27.122905-0.001850j
[2025-08-26 00:18:20] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -27.133800+0.001736j
[2025-08-26 00:18:23] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -27.131542+0.000584j
[2025-08-26 00:18:26] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -27.136533-0.000305j
[2025-08-26 00:18:28] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -27.136196-0.001452j
[2025-08-26 00:18:31] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -27.132013+0.002096j
[2025-08-26 00:18:33] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -27.126334+0.002040j
[2025-08-26 00:18:36] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -27.138565+0.000049j
[2025-08-26 00:18:39] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -27.125535-0.002483j
[2025-08-26 00:18:41] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -27.137499-0.000265j
[2025-08-26 00:18:44] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -27.129825-0.000323j
[2025-08-26 00:18:46] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -27.126381+0.000219j
[2025-08-26 00:18:49] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -27.128874+0.004396j
[2025-08-26 00:18:52] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -27.124400-0.009449j
[2025-08-26 00:18:54] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -27.130316-0.003855j
[2025-08-26 00:18:57] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -27.141151+0.001302j
[2025-08-26 00:18:59] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -27.141879+0.000124j
[2025-08-26 00:19:02] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -27.130809-0.000519j
[2025-08-26 00:19:05] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -27.125704+0.002221j
[2025-08-26 00:19:07] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -27.131766-0.004122j
[2025-08-26 00:19:10] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -27.131106-0.001335j
[2025-08-26 00:19:13] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -27.122451+0.000520j
[2025-08-26 00:19:15] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -27.133444+0.002394j
[2025-08-26 00:19:18] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -27.138800-0.003939j
[2025-08-26 00:19:20] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -27.133661-0.000587j
[2025-08-26 00:19:23] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -27.138544-0.000451j
[2025-08-26 00:19:26] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -27.135024-0.000506j
[2025-08-26 00:19:28] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -27.136336+0.000783j
[2025-08-26 00:19:31] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -27.130566-0.004343j
[2025-08-26 00:19:33] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -27.131283-0.001699j
[2025-08-26 00:19:36] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -27.138143+0.001740j
[2025-08-26 00:19:39] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -27.128304-0.000184j
[2025-08-26 00:19:41] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -27.128794-0.004127j
[2025-08-26 00:19:41] ✅ Training completed | Restarts: 2
[2025-08-26 00:19:41] ============================================================
[2025-08-26 00:19:41] Training completed | Runtime: 2802.1s
[2025-08-26 00:19:42] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-26 00:19:42] ============================================================
