[2025-08-26 17:24:26] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.82/training/checkpoints/checkpoint_iter_000300.pkl
[2025-08-26 17:24:37] ✓ 从checkpoint加载参数: 300
[2025-08-26 17:24:37]   - 能量: -29.587106-0.003168j ± 0.005486
[2025-08-26 17:24:37] ================================================================================
[2025-08-26 17:24:37] 加载量子态: L=4, J2=1.00, J1=0.82, checkpoint=checkpoint_iter_000300
[2025-08-26 17:24:37] 设置样本数为: 1048576
[2025-08-26 17:24:37] 开始生成共享样本集...
[2025-08-26 17:26:00] 样本生成完成,耗时: 82.696 秒
[2025-08-26 17:26:00] ================================================================================
[2025-08-26 17:26:00] 开始计算自旋结构因子...
[2025-08-26 17:26:00] 初始化操作符缓存...
[2025-08-26 17:26:00] 预构建所有自旋相关操作符...
[2025-08-26 17:26:00] 开始计算自旋相关函数...
[2025-08-26 17:26:08] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.563s
[2025-08-26 17:26:17] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.941s
[2025-08-26 17:26:21] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.244s
[2025-08-26 17:26:25] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.260s
[2025-08-26 17:26:29] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.274s
[2025-08-26 17:26:34] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.261s
[2025-08-26 17:26:38] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.241s
[2025-08-26 17:26:42] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.282s
[2025-08-26 17:26:46] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.241s
[2025-08-26 17:26:51] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.283s
[2025-08-26 17:26:55] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.254s
[2025-08-26 17:26:59] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.277s
[2025-08-26 17:27:03] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.242s
[2025-08-26 17:27:08] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.262s
[2025-08-26 17:27:12] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.274s
[2025-08-26 17:27:16] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.248s
[2025-08-26 17:27:20] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.258s
[2025-08-26 17:27:25] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.281s
[2025-08-26 17:27:29] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.243s
[2025-08-26 17:27:33] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.282s
[2025-08-26 17:27:38] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.260s
[2025-08-26 17:27:42] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.281s
[2025-08-26 17:27:46] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.243s
[2025-08-26 17:27:50] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.260s
[2025-08-26 17:27:55] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.251s
[2025-08-26 17:27:59] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.262s
[2025-08-26 17:28:03] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.243s
[2025-08-26 17:28:07] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.284s
[2025-08-26 17:28:12] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.251s
[2025-08-26 17:28:16] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.242s
[2025-08-26 17:28:20] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.243s
[2025-08-26 17:28:24] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.258s
[2025-08-26 17:28:29] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.257s
[2025-08-26 17:28:33] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.260s
[2025-08-26 17:28:37] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.251s
[2025-08-26 17:28:41] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.260s
[2025-08-26 17:28:46] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.245s
[2025-08-26 17:28:50] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.260s
[2025-08-26 17:28:54] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.244s
[2025-08-26 17:28:58] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.279s
[2025-08-26 17:29:03] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.243s
[2025-08-26 17:29:07] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.251s
[2025-08-26 17:29:11] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.243s
[2025-08-26 17:29:16] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.262s
[2025-08-26 17:29:20] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.243s
[2025-08-26 17:29:24] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.242s
[2025-08-26 17:29:28] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.281s
[2025-08-26 17:29:33] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.245s
[2025-08-26 17:29:37] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.280s
[2025-08-26 17:29:41] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.282s
[2025-08-26 17:29:45] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.243s
[2025-08-26 17:29:50] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.282s
[2025-08-26 17:29:54] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.249s
[2025-08-26 17:29:58] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.260s
[2025-08-26 17:30:02] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.242s
[2025-08-26 17:30:07] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.283s
[2025-08-26 17:30:11] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.242s
[2025-08-26 17:30:15] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.242s
[2025-08-26 17:30:19] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.243s
[2025-08-26 17:30:24] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.282s
[2025-08-26 17:30:29] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.962s
[2025-08-26 17:30:33] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.249s
[2025-08-26 17:30:37] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.283s
[2025-08-26 17:30:41] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.251s
[2025-08-26 17:30:41] 自旋相关函数计算完成,总耗时 281.47 秒
[2025-08-26 17:30:42] 计算傅里叶变换...
[2025-08-26 17:30:43] 自旋结构因子计算完成
[2025-08-26 17:30:43] 自旋相关函数平均误差: 0.000550
