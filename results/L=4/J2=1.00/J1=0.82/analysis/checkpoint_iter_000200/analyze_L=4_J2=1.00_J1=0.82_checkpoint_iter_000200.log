[2025-08-26 17:18:03] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.82/training/checkpoints/checkpoint_iter_000200.pkl
[2025-08-26 17:18:14] ✓ 从checkpoint加载参数: 200
[2025-08-26 17:18:14]   - 能量: -29.580606-0.002455j ± 0.005933
[2025-08-26 17:18:14] ================================================================================
[2025-08-26 17:18:14] 加载量子态: L=4, J2=1.00, J1=0.82, checkpoint=checkpoint_iter_000200
[2025-08-26 17:18:14] 设置样本数为: 1048576
[2025-08-26 17:18:14] 开始生成共享样本集...
[2025-08-26 17:19:37] 样本生成完成,耗时: 82.751 秒
[2025-08-26 17:19:37] ================================================================================
[2025-08-26 17:19:37] 开始计算自旋结构因子...
[2025-08-26 17:19:37] 初始化操作符缓存...
[2025-08-26 17:19:37] 预构建所有自旋相关操作符...
[2025-08-26 17:19:37] 开始计算自旋相关函数...
[2025-08-26 17:19:44] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.557s
[2025-08-26 17:19:53] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.937s
[2025-08-26 17:19:58] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.241s
[2025-08-26 17:20:02] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.260s
[2025-08-26 17:20:06] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.278s
[2025-08-26 17:20:10] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.259s
[2025-08-26 17:20:15] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.241s
[2025-08-26 17:20:19] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.281s
[2025-08-26 17:20:23] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.241s
[2025-08-26 17:20:27] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.283s
[2025-08-26 17:20:32] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.254s
[2025-08-26 17:20:36] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.279s
[2025-08-26 17:20:40] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.242s
[2025-08-26 17:20:45] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.260s
[2025-08-26 17:20:49] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.277s
[2025-08-26 17:20:53] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.247s
[2025-08-26 17:20:57] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.258s
[2025-08-26 17:21:02] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.281s
[2025-08-26 17:21:06] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.243s
[2025-08-26 17:21:10] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.281s
[2025-08-26 17:21:14] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.259s
[2025-08-26 17:21:19] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.279s
[2025-08-26 17:21:23] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.242s
[2025-08-26 17:21:27] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.260s
[2025-08-26 17:21:31] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.250s
[2025-08-26 17:21:36] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.261s
[2025-08-26 17:21:40] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.243s
[2025-08-26 17:21:44] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.281s
[2025-08-26 17:21:48] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.251s
[2025-08-26 17:21:53] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.242s
[2025-08-26 17:21:57] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.241s
[2025-08-26 17:22:01] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.257s
[2025-08-26 17:22:05] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.257s
[2025-08-26 17:22:10] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.258s
[2025-08-26 17:22:14] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.249s
[2025-08-26 17:22:18] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.261s
[2025-08-26 17:22:23] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.243s
[2025-08-26 17:22:27] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.259s
[2025-08-26 17:22:31] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.244s
[2025-08-26 17:22:35] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.281s
[2025-08-26 17:22:40] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.242s
[2025-08-26 17:22:44] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.250s
[2025-08-26 17:22:48] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.242s
[2025-08-26 17:22:52] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.262s
[2025-08-26 17:22:57] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.241s
[2025-08-26 17:23:01] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.241s
[2025-08-26 17:23:05] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.280s
[2025-08-26 17:23:09] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.242s
[2025-08-26 17:23:14] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.278s
[2025-08-26 17:23:18] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.280s
[2025-08-26 17:23:22] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.241s
[2025-08-26 17:23:26] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.281s
[2025-08-26 17:23:31] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.248s
[2025-08-26 17:23:35] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.259s
[2025-08-26 17:23:39] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.242s
[2025-08-26 17:23:43] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.282s
[2025-08-26 17:23:48] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.241s
[2025-08-26 17:23:52] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.241s
[2025-08-26 17:23:56] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.242s
[2025-08-26 17:24:01] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.281s
[2025-08-26 17:24:05] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.247s
[2025-08-26 17:24:09] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.241s
[2025-08-26 17:24:13] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.279s
[2025-08-26 17:24:18] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.247s
[2025-08-26 17:24:18] 自旋相关函数计算完成,总耗时 280.69 秒
[2025-08-26 17:24:18] 计算傅里叶变换...
[2025-08-26 17:24:19] 自旋结构因子计算完成
[2025-08-26 17:24:19] 自旋相关函数平均误差: 0.000543
