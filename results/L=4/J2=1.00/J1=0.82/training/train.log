[2025-08-25 20:07:33] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.81/training/checkpoints/final_GCNN.pkl
[2025-08-25 20:07:33]   - 迭代次数: final
[2025-08-25 20:07:33]   - 能量: -29.167712-0.000626j ± 0.005608
[2025-08-25 20:07:33]   - 时间戳: 2025-08-25T20:06:42.020798+08:00
[2025-08-25 20:07:46] ✓ 变分状态参数已从checkpoint恢复
[2025-08-25 20:07:46] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-25 20:07:46] ==================================================
[2025-08-25 20:07:46] GCNN for Shastry-Sutherland Model
[2025-08-25 20:07:46] ==================================================
[2025-08-25 20:07:46] System parameters:
[2025-08-25 20:07:46]   - System size: L=4, N=64
[2025-08-25 20:07:46]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-08-25 20:07:46] --------------------------------------------------
[2025-08-25 20:07:46] Model parameters:
[2025-08-25 20:07:46]   - Number of layers = 4
[2025-08-25 20:07:46]   - Number of features = 4
[2025-08-25 20:07:46]   - Total parameters = 12572
[2025-08-25 20:07:46] --------------------------------------------------
[2025-08-25 20:07:46] Training parameters:
[2025-08-25 20:07:46]   - Learning rate: 0.01
[2025-08-25 20:07:46]   - Total iterations: 1050
[2025-08-25 20:07:46]   - Annealing cycles: 3
[2025-08-25 20:07:46]   - Initial period: 150
[2025-08-25 20:07:46]   - Period multiplier: 2.0
[2025-08-25 20:07:46]   - Temperature range: 0.0-1.0
[2025-08-25 20:07:46]   - Samples: 4096
[2025-08-25 20:07:46]   - Discarded samples: 0
[2025-08-25 20:07:46]   - Chunk size: 2048
[2025-08-25 20:07:46]   - Diagonal shift: 0.2
[2025-08-25 20:07:46]   - Gradient clipping: 1.0
[2025-08-25 20:07:46]   - Checkpoint enabled: interval=100
[2025-08-25 20:07:46]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.82/training/checkpoints
[2025-08-25 20:07:46] --------------------------------------------------
[2025-08-25 20:07:46] Device status:
[2025-08-25 20:07:46]   - Devices model: NVIDIA H200 NVL
[2025-08-25 20:07:46]   - Number of devices: 1
[2025-08-25 20:07:46]   - Sharding: True
[2025-08-25 20:07:46] ============================================================
[2025-08-25 20:08:26] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -29.585036-0.004512j
[2025-08-25 20:08:50] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -29.579431+0.004349j
[2025-08-25 20:08:56] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -29.584738+0.000328j
[2025-08-25 20:09:01] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -29.571390+0.001378j
[2025-08-25 20:09:07] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -29.582095-0.002187j
[2025-08-25 20:09:13] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -29.577542-0.001951j
[2025-08-25 20:09:19] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -29.592494+0.001544j
[2025-08-25 20:09:25] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -29.589797-0.000951j
[2025-08-25 20:09:30] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -29.585047-0.001458j
[2025-08-25 20:09:36] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -29.580481+0.002581j
[2025-08-25 20:09:42] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -29.586013-0.001944j
[2025-08-25 20:09:48] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -29.586210-0.002543j
[2025-08-25 20:09:54] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -29.580920+0.000777j
[2025-08-25 20:09:59] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -29.585812-0.000845j
[2025-08-25 20:10:05] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -29.583401+0.001427j
[2025-08-25 20:10:11] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -29.584362+0.000988j
[2025-08-25 20:10:17] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -29.590137+0.001038j
[2025-08-25 20:10:23] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -29.578918-0.000695j
[2025-08-25 20:10:28] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -29.595546-0.000123j
[2025-08-25 20:10:34] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -29.581587-0.002270j
[2025-08-25 20:10:40] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -29.577136+0.000953j
[2025-08-25 20:10:46] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -29.584745-0.001928j
[2025-08-25 20:10:52] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -29.567901+0.002634j
[2025-08-25 20:10:57] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -29.584111-0.000049j
[2025-08-25 20:11:03] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -29.575522+0.018806j
[2025-08-25 20:11:09] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -29.589013-0.000344j
[2025-08-25 20:11:15] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -29.586712+0.004831j
[2025-08-25 20:11:21] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -29.577331-0.002342j
[2025-08-25 20:11:26] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -29.586602+0.005736j
[2025-08-25 20:11:32] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -29.586274+0.000720j
[2025-08-25 20:11:38] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -29.580898-0.001759j
[2025-08-25 20:11:44] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -29.580416+0.002635j
[2025-08-25 20:11:50] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -29.589682+0.004044j
[2025-08-25 20:11:55] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -29.582188-0.000613j
[2025-08-25 20:12:01] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -29.583382-0.000125j
[2025-08-25 20:12:07] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -29.584294+0.003475j
[2025-08-25 20:12:13] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -29.573025-0.003078j
[2025-08-25 20:12:19] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -29.585669-0.000423j
[2025-08-25 20:12:24] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -29.580906-0.000709j
[2025-08-25 20:12:30] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -29.581798-0.000383j
[2025-08-25 20:12:36] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -29.583903-0.001453j
[2025-08-25 20:12:42] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -29.584426+0.001025j
[2025-08-25 20:12:48] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -29.576277-0.001597j
[2025-08-25 20:12:53] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -29.587972-0.000356j
[2025-08-25 20:12:59] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -29.581914-0.001826j
[2025-08-25 20:13:05] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -29.585399-0.000497j
[2025-08-25 20:13:11] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -29.598473-0.000927j
[2025-08-25 20:13:17] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -29.578583-0.000760j
[2025-08-25 20:13:22] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -29.578096+0.001325j
[2025-08-25 20:13:28] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -29.587690-0.000303j
[2025-08-25 20:13:34] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -29.583865+0.001751j
[2025-08-25 20:13:40] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -29.579274+0.001454j
[2025-08-25 20:13:46] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -29.584760+0.003056j
[2025-08-25 20:13:51] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -29.592685+0.000498j
[2025-08-25 20:13:57] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -29.582773+0.002790j
[2025-08-25 20:14:03] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -29.595135-0.003191j
[2025-08-25 20:14:09] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -29.588867-0.000823j
[2025-08-25 20:14:15] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -29.592241-0.001039j
[2025-08-25 20:14:20] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -29.591687+0.000563j
[2025-08-25 20:14:26] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -29.585655+0.003200j
[2025-08-25 20:14:32] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -29.588982+0.004271j
[2025-08-25 20:14:38] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -29.594338+0.001785j
[2025-08-25 20:14:44] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -29.584009-0.002159j
[2025-08-25 20:14:49] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -29.584318-0.000773j
[2025-08-25 20:14:55] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -29.577189+0.000587j
[2025-08-25 20:15:01] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -29.582470+0.001025j
[2025-08-25 20:15:07] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -29.580869+0.002837j
[2025-08-25 20:15:12] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -29.579376-0.000574j
[2025-08-25 20:15:18] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -29.581558-0.002663j
[2025-08-25 20:15:24] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -29.584430+0.000459j
[2025-08-25 20:15:30] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -29.582822+0.000397j
[2025-08-25 20:15:36] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -29.593760+0.000757j
[2025-08-25 20:15:41] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -29.586066+0.001779j
[2025-08-25 20:15:47] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -29.580189+0.000922j
[2025-08-25 20:15:53] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -29.582864+0.001102j
[2025-08-25 20:15:59] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -29.586881-0.002034j
[2025-08-25 20:16:05] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -29.585384+0.000664j
[2025-08-25 20:16:10] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -29.580399+0.000365j
[2025-08-25 20:16:16] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -29.577654-0.000898j
[2025-08-25 20:16:22] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -29.589689-0.000736j
[2025-08-25 20:16:28] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -29.583042+0.003891j
[2025-08-25 20:16:34] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -29.585000-0.003209j
[2025-08-25 20:16:39] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -29.585623+0.001902j
[2025-08-25 20:16:45] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -29.588158+0.002886j
[2025-08-25 20:16:51] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -29.586831+0.005174j
[2025-08-25 20:16:57] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -29.576838-0.000105j
[2025-08-25 20:17:03] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -29.568074+0.001680j
[2025-08-25 20:17:08] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -29.580997-0.000034j
[2025-08-25 20:17:14] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -29.576326+0.003399j
[2025-08-25 20:17:20] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -29.577655+0.001410j
[2025-08-25 20:17:26] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -29.587923+0.000312j
[2025-08-25 20:17:32] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -29.585334-0.000565j
[2025-08-25 20:17:37] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -29.577145-0.000418j
[2025-08-25 20:17:43] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -29.578438+0.001828j
[2025-08-25 20:17:49] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -29.591415-0.001457j
[2025-08-25 20:17:55] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -29.589600-0.002709j
[2025-08-25 20:18:00] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -29.578163-0.000705j
[2025-08-25 20:18:06] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -29.574524-0.001456j
[2025-08-25 20:18:12] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -29.580438+0.002024j
[2025-08-25 20:18:18] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -29.578502+0.000555j
[2025-08-25 20:18:18] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-25 20:18:24] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -29.582165-0.002983j
[2025-08-25 20:18:29] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -29.580169+0.000343j
[2025-08-25 20:18:35] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -29.584915+0.002836j
[2025-08-25 20:18:41] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -29.583200+0.002529j
[2025-08-25 20:18:47] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -29.587262+0.001153j
[2025-08-25 20:18:53] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -29.583904+0.001294j
[2025-08-25 20:18:58] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -29.581760-0.001857j
[2025-08-25 20:19:04] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -29.582069+0.000208j
[2025-08-25 20:19:10] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -29.587874-0.002408j
[2025-08-25 20:19:16] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -29.585179+0.000051j
[2025-08-25 20:19:22] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -29.590224-0.002503j
[2025-08-25 20:19:27] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -29.582664-0.002636j
[2025-08-25 20:19:33] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -29.581743+0.001796j
[2025-08-25 20:19:39] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -29.578902-0.001066j
[2025-08-25 20:19:45] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -29.588741+0.001433j
[2025-08-25 20:19:51] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -29.586992+0.000268j
[2025-08-25 20:19:56] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -29.595114-0.002492j
[2025-08-25 20:20:02] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -29.586905+0.001496j
[2025-08-25 20:20:08] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -29.581557+0.001040j
[2025-08-25 20:20:14] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -29.582653+0.005149j
[2025-08-25 20:20:20] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -29.573361-0.003402j
[2025-08-25 20:20:25] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -29.590973+0.000175j
[2025-08-25 20:20:31] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -29.589307+0.000895j
[2025-08-25 20:20:37] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -29.581179-0.001450j
[2025-08-25 20:20:43] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -29.594430+0.003351j
[2025-08-25 20:20:48] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -29.599926+0.001161j
[2025-08-25 20:20:54] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -29.582050+0.000486j
[2025-08-25 20:21:00] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -29.591091+0.001575j
[2025-08-25 20:21:06] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -29.589005-0.001086j
[2025-08-25 20:21:12] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -29.582623+0.003259j
[2025-08-25 20:21:17] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -29.580743-0.001966j
[2025-08-25 20:21:23] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -29.585141+0.000069j
[2025-08-25 20:21:29] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -29.582773-0.002149j
[2025-08-25 20:21:35] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -29.581766+0.001700j
[2025-08-25 20:21:41] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -29.578177-0.000712j
[2025-08-25 20:21:46] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -29.578856+0.000366j
[2025-08-25 20:21:52] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -29.589193-0.002262j
[2025-08-25 20:21:58] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -29.582573-0.003777j
[2025-08-25 20:22:04] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -29.581448-0.000080j
[2025-08-25 20:22:10] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -29.593413-0.000212j
[2025-08-25 20:22:15] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -29.590096+0.001448j
[2025-08-25 20:22:21] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -29.580194+0.001806j
[2025-08-25 20:22:27] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -29.581545+0.001511j
[2025-08-25 20:22:33] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -29.591729+0.002069j
[2025-08-25 20:22:39] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -29.581618+0.003530j
[2025-08-25 20:22:44] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -29.582002+0.000076j
[2025-08-25 20:22:50] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -29.579234-0.001741j
[2025-08-25 20:22:56] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -29.601720+0.000721j
[2025-08-25 20:23:02] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -29.580478+0.000080j
[2025-08-25 20:23:07] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -29.574884+0.000564j
[2025-08-25 20:23:07] RESTART #1 | Period: 300
[2025-08-25 20:23:13] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -29.577721+0.000447j
[2025-08-25 20:23:19] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -29.586780+0.003228j
[2025-08-25 20:23:25] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -29.592018-0.001164j
[2025-08-25 20:23:31] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -29.574944+0.000091j
[2025-08-25 20:23:36] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -29.582814+0.001634j
[2025-08-25 20:23:42] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -29.591234+0.002312j
[2025-08-25 20:23:48] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -29.581107+0.003124j
[2025-08-25 20:23:54] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -29.583619+0.002630j
[2025-08-25 20:24:00] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -29.573932+0.001673j
[2025-08-25 20:24:05] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -29.583905+0.004103j
[2025-08-25 20:24:11] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -29.582797+0.003172j
[2025-08-25 20:24:17] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -29.582352+0.000027j
[2025-08-25 20:24:23] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -29.579435+0.000663j
[2025-08-25 20:24:29] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -29.578028+0.003234j
[2025-08-25 20:24:34] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -29.571875+0.002747j
[2025-08-25 20:24:40] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -29.575257+0.000284j
[2025-08-25 20:24:46] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -29.598309-0.001850j
[2025-08-25 20:24:52] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -29.601064-0.001979j
[2025-08-25 20:24:58] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -29.581816+0.000092j
[2025-08-25 20:25:03] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -29.587506-0.001886j
[2025-08-25 20:25:09] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -29.580173-0.001216j
[2025-08-25 20:25:15] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -29.603249+0.003722j
[2025-08-25 20:25:21] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -29.582546-0.000614j
[2025-08-25 20:25:27] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -29.587258+0.001636j
[2025-08-25 20:25:32] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -29.578014-0.001726j
[2025-08-25 20:25:38] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -29.582138+0.003860j
[2025-08-25 20:25:44] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -29.577887+0.001194j
[2025-08-25 20:25:50] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -29.595270+0.002641j
[2025-08-25 20:25:56] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -29.580553-0.000753j
[2025-08-25 20:26:01] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -29.586570-0.000897j
[2025-08-25 20:26:07] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -29.588529+0.000587j
[2025-08-25 20:26:13] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -29.588396+0.001083j
[2025-08-25 20:26:19] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -29.580019-0.004191j
[2025-08-25 20:26:25] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -29.594545+0.000282j
[2025-08-25 20:26:30] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -29.585168-0.002762j
[2025-08-25 20:26:36] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -29.584633+0.000639j
[2025-08-25 20:26:42] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -29.574705-0.001741j
[2025-08-25 20:26:48] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -29.592172-0.003809j
[2025-08-25 20:26:53] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -29.580731+0.003142j
[2025-08-25 20:26:59] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -29.587870-0.000999j
[2025-08-25 20:27:05] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -29.589761-0.001801j
[2025-08-25 20:27:11] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -29.590611+0.004518j
[2025-08-25 20:27:17] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -29.575758-0.002591j
[2025-08-25 20:27:22] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -29.584250-0.000320j
[2025-08-25 20:27:28] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -29.577849+0.002945j
[2025-08-25 20:27:34] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -29.588887+0.001973j
[2025-08-25 20:27:40] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -29.579967+0.001001j
[2025-08-25 20:27:46] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -29.585987+0.002249j
[2025-08-25 20:27:51] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -29.580858-0.000073j
[2025-08-25 20:27:57] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -29.580606-0.002455j
[2025-08-25 20:27:57] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-25 20:28:03] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -29.594412-0.001072j
[2025-08-25 20:28:09] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -29.573463+0.002076j
[2025-08-25 20:28:15] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -29.587634+0.000171j
[2025-08-25 20:28:20] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -29.582579-0.000951j
[2025-08-25 20:28:26] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -29.579692-0.001282j
[2025-08-25 20:28:32] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -29.582875+0.001167j
[2025-08-25 20:28:38] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -29.580892+0.000498j
[2025-08-25 20:28:44] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -29.590359+0.000257j
[2025-08-25 20:28:49] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -29.588397-0.000306j
[2025-08-25 20:28:55] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -29.590080+0.000040j
[2025-08-25 20:29:01] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -29.593199-0.000205j
[2025-08-25 20:29:07] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -29.580942+0.000367j
[2025-08-25 20:29:13] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -29.583626+0.000845j
[2025-08-25 20:29:18] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -29.584632+0.002543j
[2025-08-25 20:29:24] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -29.576603+0.002120j
[2025-08-25 20:29:30] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -29.579365+0.000384j
[2025-08-25 20:29:36] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -29.584189-0.004408j
[2025-08-25 20:29:42] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -29.584252+0.001930j
[2025-08-25 20:29:47] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -29.584206-0.000105j
[2025-08-25 20:29:53] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -29.582405-0.002583j
[2025-08-25 20:29:59] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -29.579953+0.001314j
[2025-08-25 20:30:05] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -29.592695+0.001861j
[2025-08-25 20:30:11] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -29.583603-0.002548j
[2025-08-25 20:30:16] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -29.580919-0.001482j
[2025-08-25 20:30:22] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -29.590091+0.000479j
[2025-08-25 20:30:28] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -29.576068-0.002050j
[2025-08-25 20:30:34] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -29.581055+0.000636j
[2025-08-25 20:30:39] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -29.588325+0.000228j
[2025-08-25 20:30:45] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -29.585942-0.003177j
[2025-08-25 20:30:51] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -29.588348+0.001345j
[2025-08-25 20:30:57] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -29.575917+0.000776j
[2025-08-25 20:31:03] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -29.587966-0.001100j
[2025-08-25 20:31:08] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -29.589387+0.003454j
[2025-08-25 20:31:14] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -29.587346-0.002169j
[2025-08-25 20:31:20] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -29.587857+0.000406j
[2025-08-25 20:31:26] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -29.585327-0.002159j
[2025-08-25 20:31:32] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -29.584807-0.001779j
[2025-08-25 20:31:37] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -29.586708+0.001657j
[2025-08-25 20:31:43] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -29.593969+0.000812j
[2025-08-25 20:31:49] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -29.572385+0.001482j
[2025-08-25 20:31:55] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -29.579302-0.001450j
[2025-08-25 20:32:01] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -29.590098+0.002273j
[2025-08-25 20:32:06] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -29.584353+0.000058j
[2025-08-25 20:32:12] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -29.586386-0.004280j
[2025-08-25 20:32:18] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -29.580155-0.000474j
[2025-08-25 20:32:24] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -29.575686-0.002589j
[2025-08-25 20:32:30] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -29.585992-0.000652j
[2025-08-25 20:32:35] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -29.581515+0.003154j
[2025-08-25 20:32:41] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -29.586075-0.000910j
[2025-08-25 20:32:47] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -29.573314-0.000405j
[2025-08-25 20:32:52] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -29.573501+0.003490j
[2025-08-25 20:32:58] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -29.592593+0.001007j
[2025-08-25 20:33:04] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -29.586162-0.003123j
[2025-08-25 20:33:09] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -29.596849+0.002122j
[2025-08-25 20:33:15] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -29.580714+0.001209j
[2025-08-25 20:33:21] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -29.579881-0.001216j
[2025-08-25 20:33:27] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -29.589301+0.002852j
[2025-08-25 20:33:32] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -29.588526+0.000107j
[2025-08-25 20:33:38] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -29.589598-0.002070j
[2025-08-25 20:33:44] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -29.571754-0.004914j
[2025-08-25 20:33:50] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -29.585355+0.000114j
[2025-08-25 20:33:56] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -29.577535-0.002252j
[2025-08-25 20:34:01] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -29.594863+0.000618j
[2025-08-25 20:34:07] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -29.574523-0.001126j
[2025-08-25 20:34:13] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -29.588551-0.000610j
[2025-08-25 20:34:19] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -29.575571-0.002882j
[2025-08-25 20:34:25] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -29.590770-0.001586j
[2025-08-25 20:34:30] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -29.584539+0.001400j
[2025-08-25 20:34:36] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -29.594801-0.002657j
[2025-08-25 20:34:42] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -29.594396-0.001599j
[2025-08-25 20:34:48] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -29.583216-0.005206j
[2025-08-25 20:34:54] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -29.579571+0.000318j
[2025-08-25 20:34:59] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -29.580893+0.002063j
[2025-08-25 20:35:05] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -29.590182-0.000936j
[2025-08-25 20:35:11] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -29.588742-0.003504j
[2025-08-25 20:35:17] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -29.582216-0.001709j
[2025-08-25 20:35:23] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -29.580412+0.002170j
[2025-08-25 20:35:28] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -29.581108-0.001151j
[2025-08-25 20:35:34] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -29.584957-0.004114j
[2025-08-25 20:35:40] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -29.588102-0.000143j
[2025-08-25 20:35:46] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -29.579758+0.001021j
[2025-08-25 20:35:52] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -29.585275-0.001059j
[2025-08-25 20:35:57] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -29.582995-0.000095j
[2025-08-25 20:36:03] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -29.589111+0.003737j
[2025-08-25 20:36:09] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -29.590325+0.003020j
[2025-08-25 20:36:15] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -29.581527+0.001947j
[2025-08-25 20:36:21] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -29.581302+0.001092j
[2025-08-25 20:36:26] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -29.584497+0.000733j
[2025-08-25 20:36:32] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -29.580065+0.002500j
[2025-08-25 20:36:38] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -29.590497-0.000879j
[2025-08-25 20:36:44] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -29.588958-0.001961j
[2025-08-25 20:36:49] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -29.592232+0.001905j
[2025-08-25 20:36:55] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -29.588783+0.000048j
[2025-08-25 20:37:01] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -29.584937-0.002399j
[2025-08-25 20:37:07] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -29.586320-0.000670j
[2025-08-25 20:37:13] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -29.585106-0.002012j
[2025-08-25 20:37:18] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -29.580599+0.003613j
[2025-08-25 20:37:24] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -29.581277+0.001841j
[2025-08-25 20:37:30] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -29.579241+0.002101j
[2025-08-25 20:37:36] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -29.587106-0.003168j
[2025-08-25 20:37:36] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-25 20:37:42] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -29.586505-0.002482j
[2025-08-25 20:37:47] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -29.589762+0.002391j
[2025-08-25 20:37:53] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -29.585629+0.001438j
[2025-08-25 20:37:59] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -29.591901-0.003442j
[2025-08-25 20:38:05] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -29.587278-0.002509j
[2025-08-25 20:38:10] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -29.591995+0.002131j
[2025-08-25 20:38:16] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -29.583639-0.001288j
[2025-08-25 20:38:22] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -29.590708-0.000676j
[2025-08-25 20:38:28] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -29.581380-0.002567j
[2025-08-25 20:38:34] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -29.573287+0.001483j
[2025-08-25 20:38:40] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -29.580231+0.000235j
[2025-08-25 20:38:45] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -29.584848+0.001020j
[2025-08-25 20:38:51] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -29.584581+0.001128j
[2025-08-25 20:38:57] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -29.571240-0.000484j
[2025-08-25 20:39:03] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -29.578680-0.000813j
[2025-08-25 20:39:08] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -29.574747+0.002473j
[2025-08-25 20:39:14] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -29.581100+0.001224j
[2025-08-25 20:39:20] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -29.583896-0.001673j
[2025-08-25 20:39:26] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -29.585674-0.000125j
[2025-08-25 20:39:32] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -29.593590-0.001869j
[2025-08-25 20:39:38] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -29.593145+0.000915j
[2025-08-25 20:39:43] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -29.578170+0.002588j
[2025-08-25 20:39:49] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -29.568373+0.000714j
[2025-08-25 20:39:55] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -29.591964+0.003143j
[2025-08-25 20:40:01] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -29.583451+0.000117j
[2025-08-25 20:40:07] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -29.585373+0.000259j
[2025-08-25 20:40:12] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -29.584732+0.001872j
[2025-08-25 20:40:18] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -29.585273+0.002780j
[2025-08-25 20:40:24] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -29.581897-0.000270j
[2025-08-25 20:40:30] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -29.589870+0.000851j
[2025-08-25 20:40:35] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -29.579414-0.002143j
[2025-08-25 20:40:41] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -29.578747+0.000777j
[2025-08-25 20:40:47] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -29.578586-0.000687j
[2025-08-25 20:40:53] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -29.586081-0.001696j
[2025-08-25 20:40:59] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -29.593182-0.000723j
[2025-08-25 20:41:04] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -29.588834-0.001520j
[2025-08-25 20:41:10] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -29.586871+0.000602j
[2025-08-25 20:41:16] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -29.586901-0.003478j
[2025-08-25 20:41:22] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -29.588602-0.001532j
[2025-08-25 20:41:28] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -29.587213+0.000690j
[2025-08-25 20:41:33] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -29.581105-0.000589j
[2025-08-25 20:41:39] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -29.591253+0.002285j
[2025-08-25 20:41:45] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -29.582624+0.000309j
[2025-08-25 20:41:51] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -29.591081+0.000705j
[2025-08-25 20:41:57] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -29.579534+0.001790j
[2025-08-25 20:42:02] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -29.590125-0.000013j
[2025-08-25 20:42:08] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -29.582060-0.000255j
[2025-08-25 20:42:14] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -29.579775-0.000767j
[2025-08-25 20:42:20] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -29.584619+0.002150j
[2025-08-25 20:42:26] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -29.587198-0.000247j
[2025-08-25 20:42:31] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -29.581514-0.000431j
[2025-08-25 20:42:37] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -29.583834+0.000759j
[2025-08-25 20:42:43] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -29.576327-0.002387j
[2025-08-25 20:42:49] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -29.585603+0.003296j
[2025-08-25 20:42:54] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -29.574924+0.001170j
[2025-08-25 20:43:00] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -29.577755+0.000971j
[2025-08-25 20:43:06] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -29.587074+0.000489j
[2025-08-25 20:43:12] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -29.575909+0.000660j
[2025-08-25 20:43:18] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -29.580150-0.001347j
[2025-08-25 20:43:23] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -29.583235+0.001476j
[2025-08-25 20:43:29] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -29.581922+0.003340j
[2025-08-25 20:43:35] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -29.578734-0.002293j
[2025-08-25 20:43:41] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -29.582332+0.002439j
[2025-08-25 20:43:47] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -29.587064+0.000009j
[2025-08-25 20:43:52] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -29.579778-0.005760j
[2025-08-25 20:43:58] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -29.591216+0.000555j
[2025-08-25 20:44:04] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -29.586872-0.000276j
[2025-08-25 20:44:10] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -29.578655+0.001085j
[2025-08-25 20:44:15] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -29.583103-0.003720j
[2025-08-25 20:44:21] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -29.583745-0.002378j
[2025-08-25 20:44:27] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -29.586410-0.001531j
[2025-08-25 20:44:33] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -29.576075-0.002972j
[2025-08-25 20:44:38] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -29.592142+0.002944j
[2025-08-25 20:44:44] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -29.582255-0.001896j
[2025-08-25 20:44:50] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -29.584477-0.002153j
[2025-08-25 20:44:55] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -29.585860+0.000748j
[2025-08-25 20:45:01] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -29.596089-0.000630j
[2025-08-25 20:45:07] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -29.585992-0.000620j
[2025-08-25 20:45:13] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -29.582000-0.000335j
[2025-08-25 20:45:18] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -29.576844+0.005588j
[2025-08-25 20:45:24] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -29.583866+0.002398j
[2025-08-25 20:45:30] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -29.582013-0.000676j
[2025-08-25 20:45:36] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -29.576978+0.000333j
[2025-08-25 20:45:41] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -29.575641+0.001123j
[2025-08-25 20:45:47] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -29.587542+0.001368j
[2025-08-25 20:45:52] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -29.588322+0.003093j
[2025-08-25 20:45:58] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -29.588303-0.001373j
[2025-08-25 20:46:04] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -29.586507+0.002689j
[2025-08-25 20:46:09] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -29.577490+0.000517j
[2025-08-25 20:46:15] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -29.592356-0.000930j
[2025-08-25 20:46:21] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -29.588274+0.000235j
[2025-08-25 20:46:27] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -29.591020-0.002769j
[2025-08-25 20:46:32] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -29.596674-0.001124j
[2025-08-25 20:46:38] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -29.583571+0.002754j
[2025-08-25 20:46:44] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -29.592273+0.003695j
[2025-08-25 20:46:50] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -29.587135-0.001447j
[2025-08-25 20:46:55] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -29.590382+0.003028j
[2025-08-25 20:47:01] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -29.584891+0.001089j
[2025-08-25 20:47:07] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -29.588451-0.000832j
[2025-08-25 20:47:12] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -29.578437-0.002167j
[2025-08-25 20:47:12] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-25 20:47:18] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -29.594447-0.000322j
[2025-08-25 20:47:24] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -29.593760-0.000071j
[2025-08-25 20:47:30] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -29.573014-0.006411j
[2025-08-25 20:47:35] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -29.581302+0.000465j
[2025-08-25 20:47:41] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -29.592398-0.000370j
[2025-08-25 20:47:47] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -29.592244+0.002380j
[2025-08-25 20:47:53] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -29.578122-0.001630j
[2025-08-25 20:47:58] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -29.583756+0.001386j
[2025-08-25 20:48:04] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -29.588333-0.001048j
[2025-08-25 20:48:10] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -29.588620-0.002653j
[2025-08-25 20:48:16] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -29.581147+0.002714j
[2025-08-25 20:48:21] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -29.588428-0.001302j
[2025-08-25 20:48:27] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -29.574375+0.000230j
[2025-08-25 20:48:33] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -29.582593+0.002047j
[2025-08-25 20:48:38] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -29.593149+0.000435j
[2025-08-25 20:48:44] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -29.580613-0.000050j
[2025-08-25 20:48:50] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -29.582885+0.004794j
[2025-08-25 20:48:56] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -29.583674+0.001797j
[2025-08-25 20:49:01] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -29.583567+0.000910j
[2025-08-25 20:49:07] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -29.589994+0.002134j
[2025-08-25 20:49:13] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -29.576740+0.002536j
[2025-08-25 20:49:19] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -29.582062+0.000984j
[2025-08-25 20:49:24] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -29.577839+0.002076j
[2025-08-25 20:49:30] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -29.579724+0.001498j
[2025-08-25 20:49:36] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -29.594731-0.000740j
[2025-08-25 20:49:42] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -29.589312-0.001289j
[2025-08-25 20:49:47] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -29.581401+0.000797j
[2025-08-25 20:49:53] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -29.586508+0.001803j
[2025-08-25 20:49:59] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -29.575951-0.003989j
[2025-08-25 20:50:05] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -29.584533-0.007371j
[2025-08-25 20:50:10] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -29.587985-0.002147j
[2025-08-25 20:50:16] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -29.594074-0.004515j
[2025-08-25 20:50:22] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -29.585393+0.000675j
[2025-08-25 20:50:28] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -29.572458+0.001055j
[2025-08-25 20:50:33] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -29.577716+0.003093j
[2025-08-25 20:50:39] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -29.583851-0.000254j
[2025-08-25 20:50:45] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -29.586537+0.001506j
[2025-08-25 20:50:51] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -29.593745-0.008780j
[2025-08-25 20:50:56] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -29.575030-0.001997j
[2025-08-25 20:51:02] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -29.588755+0.001187j
[2025-08-25 20:51:08] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -29.584782-0.000212j
[2025-08-25 20:51:14] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -29.586569-0.000911j
[2025-08-25 20:51:19] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -29.583237+0.002307j
[2025-08-25 20:51:25] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -29.578419+0.000140j
[2025-08-25 20:51:31] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -29.577337-0.001232j
[2025-08-25 20:51:37] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -29.594448-0.002050j
[2025-08-25 20:51:42] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -29.586947+0.001153j
[2025-08-25 20:51:48] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -29.581471+0.002918j
[2025-08-25 20:51:54] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -29.583032-0.000939j
[2025-08-25 20:52:00] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -29.594240-0.001656j
[2025-08-25 20:52:00] RESTART #2 | Period: 600
[2025-08-25 20:52:05] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -29.578582+0.000481j
[2025-08-25 20:52:11] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -29.589520-0.002062j
[2025-08-25 20:52:17] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -29.583420-0.002516j
[2025-08-25 20:52:23] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -29.591260+0.002666j
[2025-08-25 20:52:28] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -29.593910-0.004442j
[2025-08-25 20:52:34] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -29.586349+0.003023j
[2025-08-25 20:52:40] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -29.588497-0.001110j
[2025-08-25 20:52:46] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -29.580907-0.001581j
[2025-08-25 20:52:51] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -29.582591+0.002393j
[2025-08-25 20:52:57] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -29.582284-0.003572j
[2025-08-25 20:53:03] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -29.587046+0.001800j
[2025-08-25 20:53:09] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -29.580469+0.002083j
[2025-08-25 20:53:14] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -29.583656+0.004087j
[2025-08-25 20:53:20] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -29.586318-0.000308j
[2025-08-25 20:53:26] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -29.571954+0.000012j
[2025-08-25 20:53:31] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -29.588999-0.000700j
[2025-08-25 20:53:37] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -29.582484-0.000276j
[2025-08-25 20:53:43] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -29.589827+0.001516j
[2025-08-25 20:53:49] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -29.591114-0.001782j
[2025-08-25 20:53:54] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -29.591168-0.002049j
[2025-08-25 20:54:00] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -29.581795+0.000579j
[2025-08-25 20:54:06] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -29.582464-0.000942j
[2025-08-25 20:54:12] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -29.583054-0.000623j
[2025-08-25 20:54:17] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -29.582093+0.000181j
[2025-08-25 20:54:23] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -29.573802-0.001363j
[2025-08-25 20:54:29] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -29.580547-0.002321j
[2025-08-25 20:54:35] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -29.582960-0.000345j
[2025-08-25 20:54:40] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -29.579161-0.000259j
[2025-08-25 20:54:46] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -29.588660-0.004891j
[2025-08-25 20:54:52] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -29.584210-0.003797j
[2025-08-25 20:54:57] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -29.584617-0.004253j
[2025-08-25 20:55:03] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -29.582221+0.000208j
[2025-08-25 20:55:09] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -29.574388-0.007918j
[2025-08-25 20:55:15] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -29.583570-0.001532j
[2025-08-25 20:55:20] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -29.587062+0.003417j
[2025-08-25 20:55:26] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -29.578047+0.000237j
[2025-08-25 20:55:32] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -29.586209-0.001624j
[2025-08-25 20:55:38] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -29.582658+0.006168j
[2025-08-25 20:55:43] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -29.577168-0.000185j
[2025-08-25 20:55:49] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -29.586496+0.002705j
[2025-08-25 20:55:55] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -29.583019+0.000186j
[2025-08-25 20:56:01] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -29.586474-0.000390j
[2025-08-25 20:56:06] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -29.592389-0.002174j
[2025-08-25 20:56:12] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -29.592849+0.004126j
[2025-08-25 20:56:18] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -29.581008-0.000378j
[2025-08-25 20:56:24] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -29.593551-0.002905j
[2025-08-25 20:56:29] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -29.580427+0.000663j
[2025-08-25 20:56:35] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -29.585602-0.000130j
[2025-08-25 20:56:41] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -29.587440-0.001453j
[2025-08-25 20:56:46] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -29.578740-0.001381j
[2025-08-25 20:56:47] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-25 20:56:52] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -29.582298+0.000822j
[2025-08-25 20:56:58] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -29.579291-0.000175j
[2025-08-25 20:57:04] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -29.590130-0.000920j
[2025-08-25 20:57:09] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -29.596739+0.000671j
[2025-08-25 20:57:15] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -29.591205+0.002712j
[2025-08-25 20:57:21] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -29.568724+0.000329j
[2025-08-25 20:57:27] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -29.577320+0.004329j
[2025-08-25 20:57:32] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -29.588653-0.003728j
[2025-08-25 20:57:38] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -29.580476+0.002017j
[2025-08-25 20:57:44] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -29.579984+0.002710j
[2025-08-25 20:57:50] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -29.578458-0.000978j
[2025-08-25 20:57:55] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -29.582275-0.000121j
[2025-08-25 20:58:01] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -29.584441+0.000005j
[2025-08-25 20:58:07] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -29.579960+0.000953j
[2025-08-25 20:58:13] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -29.585343+0.001162j
[2025-08-25 20:58:18] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -29.591816-0.000614j
[2025-08-25 20:58:24] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -29.584188+0.001927j
[2025-08-25 20:58:30] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -29.580161+0.001757j
[2025-08-25 20:58:36] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -29.591834+0.000252j
[2025-08-25 20:58:41] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -29.585865-0.000104j
[2025-08-25 20:58:47] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -29.588358-0.001786j
[2025-08-25 20:58:53] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -29.582692+0.003785j
[2025-08-25 20:58:59] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -29.587765-0.003236j
[2025-08-25 20:59:04] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -29.585099+0.000675j
[2025-08-25 20:59:10] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -29.577492-0.002066j
[2025-08-25 20:59:16] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -29.573296+0.000417j
[2025-08-25 20:59:22] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -29.581028+0.001496j
[2025-08-25 20:59:27] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -29.586916-0.001228j
[2025-08-25 20:59:33] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -29.581709+0.004252j
[2025-08-25 20:59:39] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -29.587091-0.003970j
[2025-08-25 20:59:45] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -29.581396+0.001395j
[2025-08-25 20:59:50] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -29.584891+0.003559j
[2025-08-25 20:59:56] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -29.583574+0.002854j
[2025-08-25 21:00:02] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -29.587135-0.000992j
[2025-08-25 21:00:08] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -29.574753-0.004500j
[2025-08-25 21:00:14] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -29.582834-0.001926j
[2025-08-25 21:00:19] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -29.582017-0.001532j
[2025-08-25 21:00:25] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -29.592597+0.001586j
[2025-08-25 21:00:31] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -29.583682+0.000975j
[2025-08-25 21:00:37] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -29.588154+0.000013j
[2025-08-25 21:00:42] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -29.581208-0.004430j
[2025-08-25 21:00:48] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -29.584223-0.000508j
[2025-08-25 21:00:54] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -29.592892+0.002098j
[2025-08-25 21:00:59] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -29.589441-0.000676j
[2025-08-25 21:01:05] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -29.585712+0.001913j
[2025-08-25 21:01:11] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -29.580040+0.000498j
[2025-08-25 21:01:17] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -29.583724+0.001410j
[2025-08-25 21:01:22] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -29.589336-0.002839j
[2025-08-25 21:01:28] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -29.587016-0.000529j
[2025-08-25 21:01:34] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -29.586544+0.000239j
[2025-08-25 21:01:40] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -29.586088+0.000592j
[2025-08-25 21:01:45] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -29.587256-0.002194j
[2025-08-25 21:01:51] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -29.593686+0.001093j
[2025-08-25 21:01:57] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -29.579735-0.003061j
[2025-08-25 21:02:03] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -29.590116+0.004357j
[2025-08-25 21:02:08] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -29.583106-0.003036j
[2025-08-25 21:02:14] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -29.578499+0.001858j
[2025-08-25 21:02:20] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -29.592032+0.000871j
[2025-08-25 21:02:26] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -29.587173+0.000605j
[2025-08-25 21:02:32] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -29.588340+0.000304j
[2025-08-25 21:02:37] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -29.582695+0.002203j
[2025-08-25 21:02:43] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -29.588855-0.000578j
[2025-08-25 21:02:49] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -29.588916-0.001009j
[2025-08-25 21:02:54] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -29.576347+0.001038j
[2025-08-25 21:03:00] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -29.580956-0.001083j
[2025-08-25 21:03:06] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -29.578162-0.003283j
[2025-08-25 21:03:11] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -29.582904-0.000474j
[2025-08-25 21:03:17] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -29.581719-0.000042j
[2025-08-25 21:03:23] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -29.583781+0.000127j
[2025-08-25 21:03:29] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -29.579574-0.001066j
[2025-08-25 21:03:35] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -29.574917+0.002050j
[2025-08-25 21:03:40] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -29.586157+0.000134j
[2025-08-25 21:03:46] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -29.605540+0.002410j
[2025-08-25 21:03:52] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -29.585354+0.000386j
[2025-08-25 21:03:58] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -29.578855-0.003114j
[2025-08-25 21:04:03] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -29.586694-0.000739j
[2025-08-25 21:04:09] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -29.582659+0.001461j
[2025-08-25 21:04:15] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -29.579547+0.001284j
[2025-08-25 21:04:21] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -29.577923+0.000085j
[2025-08-25 21:04:26] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -29.586866-0.000038j
[2025-08-25 21:04:32] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -29.584971+0.002049j
[2025-08-25 21:04:38] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -29.585162-0.000101j
[2025-08-25 21:04:44] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -29.579938-0.002322j
[2025-08-25 21:04:49] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -29.585491+0.002504j
[2025-08-25 21:04:55] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -29.582560-0.001127j
[2025-08-25 21:05:01] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -29.580828-0.002347j
[2025-08-25 21:05:07] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -29.587617-0.003173j
[2025-08-25 21:05:12] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -29.582910-0.001650j
[2025-08-25 21:05:18] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -29.580279-0.001817j
[2025-08-25 21:05:24] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -29.599772-0.001180j
[2025-08-25 21:05:30] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -29.580389+0.000327j
[2025-08-25 21:05:35] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -29.579276-0.001880j
[2025-08-25 21:05:41] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -29.591850-0.004013j
[2025-08-25 21:05:47] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -29.586433+0.002357j
[2025-08-25 21:05:53] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -29.578421+0.000954j
[2025-08-25 21:05:58] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -29.588592+0.000083j
[2025-08-25 21:06:04] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -29.580499+0.005426j
[2025-08-25 21:06:10] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -29.585400-0.001063j
[2025-08-25 21:06:16] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -29.584282+0.001157j
[2025-08-25 21:06:21] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -29.581887-0.004150j
[2025-08-25 21:06:21] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-25 21:06:27] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -29.579193-0.001263j
[2025-08-25 21:06:33] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -29.583964-0.003001j
[2025-08-25 21:06:39] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -29.585609+0.002000j
[2025-08-25 21:06:44] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -29.581864-0.000394j
[2025-08-25 21:06:50] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -29.590806-0.001950j
[2025-08-25 21:06:56] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -29.584920-0.003446j
[2025-08-25 21:07:02] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -29.585710-0.003745j
[2025-08-25 21:07:07] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -29.580044-0.001689j
[2025-08-25 21:07:13] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -29.592788-0.000782j
[2025-08-25 21:07:19] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -29.574670-0.000789j
[2025-08-25 21:07:25] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -29.579328-0.003289j
[2025-08-25 21:07:30] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -29.585885-0.000730j
[2025-08-25 21:07:36] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -29.578353+0.001647j
[2025-08-25 21:07:42] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -29.586738-0.002593j
[2025-08-25 21:07:48] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -29.578329-0.000550j
[2025-08-25 21:07:53] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -29.587878-0.001089j
[2025-08-25 21:07:59] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -29.584315-0.000279j
[2025-08-25 21:08:05] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -29.593400+0.000594j
[2025-08-25 21:08:11] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -29.584683-0.000277j
[2025-08-25 21:08:16] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -29.582919+0.001601j
[2025-08-25 21:08:22] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -29.586687+0.000775j
[2025-08-25 21:08:28] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -29.578128+0.002496j
[2025-08-25 21:08:34] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -29.578941-0.000739j
[2025-08-25 21:08:40] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -29.572479+0.002897j
[2025-08-25 21:08:45] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -29.589963+0.001006j
[2025-08-25 21:08:51] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -29.582629+0.003084j
[2025-08-25 21:08:57] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -29.590887+0.000786j
[2025-08-25 21:09:03] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -29.592338+0.001734j
[2025-08-25 21:09:08] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -29.575066+0.001211j
[2025-08-25 21:09:14] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -29.589759+0.000775j
[2025-08-25 21:09:20] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -29.575566-0.002123j
[2025-08-25 21:09:25] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -29.584336-0.000112j
[2025-08-25 21:09:31] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -29.584250-0.000141j
[2025-08-25 21:09:37] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -29.586060-0.002350j
[2025-08-25 21:09:43] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -29.596011-0.000368j
[2025-08-25 21:09:49] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -29.588579-0.000268j
[2025-08-25 21:09:54] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -29.587046+0.001610j
[2025-08-25 21:10:00] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -29.574675-0.000395j
[2025-08-25 21:10:06] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -29.592994+0.003853j
[2025-08-25 21:10:12] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -29.573697-0.000793j
[2025-08-25 21:10:17] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -29.585622-0.001628j
[2025-08-25 21:10:23] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -29.578873+0.001837j
[2025-08-25 21:10:29] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -29.579736-0.000294j
[2025-08-25 21:10:34] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -29.584680+0.001366j
[2025-08-25 21:10:40] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -29.588738+0.002551j
[2025-08-25 21:10:46] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -29.585163+0.003149j
[2025-08-25 21:10:52] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -29.589732-0.000063j
[2025-08-25 21:10:57] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -29.585822-0.001185j
[2025-08-25 21:11:03] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -29.589246-0.001368j
[2025-08-25 21:11:09] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -29.584907+0.001048j
[2025-08-25 21:11:15] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -29.579347-0.000166j
[2025-08-25 21:11:20] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -29.582742+0.001055j
[2025-08-25 21:11:26] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -29.586545+0.003228j
[2025-08-25 21:11:32] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -29.592627-0.000980j
[2025-08-25 21:11:38] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -29.590319+0.002943j
[2025-08-25 21:11:43] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -29.581053+0.003093j
[2025-08-25 21:11:49] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -29.578891+0.001665j
[2025-08-25 21:11:55] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -29.582200-0.000270j
[2025-08-25 21:12:01] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -29.588829-0.004130j
[2025-08-25 21:12:06] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -29.581482-0.004629j
[2025-08-25 21:12:12] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -29.579741+0.001348j
[2025-08-25 21:12:18] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -29.581753+0.001082j
[2025-08-25 21:12:24] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -29.582224-0.000844j
[2025-08-25 21:12:29] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -29.581468+0.002701j
[2025-08-25 21:12:35] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -29.590605+0.001436j
[2025-08-25 21:12:41] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -29.574847-0.002118j
[2025-08-25 21:12:47] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -29.594054+0.001665j
[2025-08-25 21:12:52] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -29.575509+0.001808j
[2025-08-25 21:12:58] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -29.599088+0.001468j
[2025-08-25 21:13:04] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -29.586095-0.001620j
[2025-08-25 21:13:09] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -29.581975+0.001065j
[2025-08-25 21:13:15] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -29.582090-0.002817j
[2025-08-25 21:13:21] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -29.586539+0.000753j
[2025-08-25 21:13:27] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -29.602175-0.004807j
[2025-08-25 21:13:32] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -29.591578+0.003406j
[2025-08-25 21:13:38] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -29.584187-0.000485j
[2025-08-25 21:13:44] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -29.574407-0.001489j
[2025-08-25 21:13:50] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -29.574395-0.002670j
[2025-08-25 21:13:55] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -29.583029+0.000963j
[2025-08-25 21:14:01] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -29.580611-0.000438j
[2025-08-25 21:14:07] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -29.589969+0.003997j
[2025-08-25 21:14:13] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -29.576930-0.000119j
[2025-08-25 21:14:18] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -29.575646-0.001384j
[2025-08-25 21:14:24] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -29.582253-0.000689j
[2025-08-25 21:14:30] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -29.581195+0.001443j
[2025-08-25 21:14:36] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -29.589173+0.001702j
[2025-08-25 21:14:41] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -29.581007+0.005176j
[2025-08-25 21:14:47] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -29.588069+0.002892j
[2025-08-25 21:14:53] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -29.585329+0.000819j
[2025-08-25 21:14:59] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -29.574959-0.001123j
[2025-08-25 21:15:04] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -29.594782-0.000721j
[2025-08-25 21:15:10] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -29.585060-0.004208j
[2025-08-25 21:15:16] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -29.585866-0.000648j
[2025-08-25 21:15:22] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -29.593225-0.000267j
[2025-08-25 21:15:27] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -29.578117-0.000966j
[2025-08-25 21:15:33] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -29.592137-0.001370j
[2025-08-25 21:15:39] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -29.580035+0.001357j
[2025-08-25 21:15:45] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -29.574810+0.000255j
[2025-08-25 21:15:50] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -29.575693+0.003662j
[2025-08-25 21:15:56] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -29.586484-0.003827j
[2025-08-25 21:15:56] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-25 21:16:02] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -29.587546-0.000012j
[2025-08-25 21:16:08] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -29.587703+0.000176j
[2025-08-25 21:16:13] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -29.585472-0.001237j
[2025-08-25 21:16:19] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -29.581596-0.000438j
[2025-08-25 21:16:25] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -29.592414-0.000967j
[2025-08-25 21:16:31] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -29.586214+0.000168j
[2025-08-25 21:16:36] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -29.587888+0.000430j
[2025-08-25 21:16:42] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -29.588380-0.001993j
[2025-08-25 21:16:48] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -29.580462-0.001768j
[2025-08-25 21:16:54] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -29.591706+0.002128j
[2025-08-25 21:16:59] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -29.585953+0.000018j
[2025-08-25 21:17:05] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -29.586340-0.000776j
[2025-08-25 21:17:11] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -29.585377-0.004197j
[2025-08-25 21:17:17] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -29.587142-0.001163j
[2025-08-25 21:17:22] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -29.577004-0.002933j
[2025-08-25 21:17:28] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -29.587733-0.003117j
[2025-08-25 21:17:34] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -29.582260+0.002375j
[2025-08-25 21:17:40] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -29.580874-0.000049j
[2025-08-25 21:17:45] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -29.589084+0.000918j
[2025-08-25 21:17:51] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -29.585759+0.006077j
[2025-08-25 21:17:57] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -29.582450-0.002349j
[2025-08-25 21:18:03] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -29.590231+0.002350j
[2025-08-25 21:18:08] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -29.580462+0.003177j
[2025-08-25 21:18:14] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -29.587522+0.000881j
[2025-08-25 21:18:20] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -29.586852-0.003835j
[2025-08-25 21:18:26] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -29.586179-0.002569j
[2025-08-25 21:18:31] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -29.582874+0.001812j
[2025-08-25 21:18:37] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -29.591709-0.000351j
[2025-08-25 21:18:43] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -29.580465-0.001648j
[2025-08-25 21:18:49] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -29.587002+0.000490j
[2025-08-25 21:18:54] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -29.592328-0.005208j
[2025-08-25 21:19:00] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -29.589413-0.001433j
[2025-08-25 21:19:06] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -29.585811-0.001387j
[2025-08-25 21:19:12] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -29.579891+0.003576j
[2025-08-25 21:19:17] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -29.585885+0.001462j
[2025-08-25 21:19:23] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -29.586912+0.001041j
[2025-08-25 21:19:29] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -29.582221+0.004268j
[2025-08-25 21:19:35] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -29.583311+0.003827j
[2025-08-25 21:19:40] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -29.584092+0.001227j
[2025-08-25 21:19:46] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -29.587674-0.003720j
[2025-08-25 21:19:52] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -29.576302-0.004430j
[2025-08-25 21:19:58] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -29.594003-0.000899j
[2025-08-25 21:20:04] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -29.589772-0.000397j
[2025-08-25 21:20:09] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -29.587856-0.001743j
[2025-08-25 21:20:15] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -29.585595+0.000984j
[2025-08-25 21:20:21] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -29.585087+0.001890j
[2025-08-25 21:20:27] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -29.597857-0.000431j
[2025-08-25 21:20:32] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -29.585746-0.001139j
[2025-08-25 21:20:38] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -29.597122-0.000638j
[2025-08-25 21:20:44] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -29.591549+0.002713j
[2025-08-25 21:20:50] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -29.583275+0.001975j
[2025-08-25 21:20:55] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -29.585613-0.000666j
[2025-08-25 21:21:01] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -29.586926-0.001161j
[2025-08-25 21:21:07] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -29.584344-0.000375j
[2025-08-25 21:21:13] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -29.585170+0.002892j
[2025-08-25 21:21:18] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -29.581536-0.000217j
[2025-08-25 21:21:24] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -29.588080+0.003544j
[2025-08-25 21:21:30] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -29.576248+0.000821j
[2025-08-25 21:21:36] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -29.584437-0.002371j
[2025-08-25 21:21:41] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -29.584699-0.001503j
[2025-08-25 21:21:47] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -29.586509-0.002072j
[2025-08-25 21:21:53] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -29.574917+0.002101j
[2025-08-25 21:21:59] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -29.578276+0.000096j
[2025-08-25 21:22:04] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -29.585753+0.001121j
[2025-08-25 21:22:10] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -29.585241+0.001509j
[2025-08-25 21:22:16] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -29.589459+0.000819j
[2025-08-25 21:22:22] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -29.581733-0.004322j
[2025-08-25 21:22:27] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -29.579796-0.003727j
[2025-08-25 21:22:33] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -29.585331-0.001413j
[2025-08-25 21:22:39] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -29.583847-0.001225j
[2025-08-25 21:22:45] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -29.586750+0.002478j
[2025-08-25 21:22:50] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -29.585061-0.000474j
[2025-08-25 21:22:56] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -29.579276-0.000994j
[2025-08-25 21:23:02] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -29.590499-0.001451j
[2025-08-25 21:23:08] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -29.575711+0.001665j
[2025-08-25 21:23:13] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -29.584574+0.001568j
[2025-08-25 21:23:19] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -29.577715+0.001394j
[2025-08-25 21:23:25] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -29.579403+0.001535j
[2025-08-25 21:23:31] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -29.583775-0.002454j
[2025-08-25 21:23:36] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -29.584017+0.000435j
[2025-08-25 21:23:42] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -29.590034-0.001026j
[2025-08-25 21:23:48] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -29.588513-0.003292j
[2025-08-25 21:23:54] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -29.579850-0.000075j
[2025-08-25 21:23:59] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -29.573625+0.000971j
[2025-08-25 21:24:05] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -29.591929-0.001002j
[2025-08-25 21:24:11] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -29.587021-0.003498j
[2025-08-25 21:24:17] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -29.585463-0.002162j
[2025-08-25 21:24:22] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -29.586915+0.000471j
[2025-08-25 21:24:28] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -29.591503-0.003338j
[2025-08-25 21:24:34] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -29.585071-0.001385j
[2025-08-25 21:24:40] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -29.582164-0.001231j
[2025-08-25 21:24:45] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -29.586291+0.003660j
[2025-08-25 21:24:51] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -29.584073+0.001543j
[2025-08-25 21:24:57] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -29.588577-0.001030j
[2025-08-25 21:25:03] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -29.584851-0.001437j
[2025-08-25 21:25:08] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -29.593798-0.000486j
[2025-08-25 21:25:14] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -29.584461-0.001537j
[2025-08-25 21:25:20] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -29.592638-0.002992j
[2025-08-25 21:25:26] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -29.585820+0.001369j
[2025-08-25 21:25:31] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -29.589036+0.000863j
[2025-08-25 21:25:31] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-25 21:25:37] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -29.587778+0.001379j
[2025-08-25 21:25:43] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -29.582107+0.003194j
[2025-08-25 21:25:49] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -29.592853-0.001297j
[2025-08-25 21:25:54] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -29.584952+0.000248j
[2025-08-25 21:26:00] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -29.588085+0.001056j
[2025-08-25 21:26:06] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -29.584330+0.001157j
[2025-08-25 21:26:12] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -29.582537+0.004120j
[2025-08-25 21:26:17] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -29.588858+0.001475j
[2025-08-25 21:26:23] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -29.577056+0.001549j
[2025-08-25 21:26:29] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -29.593984-0.000981j
[2025-08-25 21:26:35] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -29.584040-0.003141j
[2025-08-25 21:26:40] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -29.587403+0.003951j
[2025-08-25 21:26:46] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -29.589718-0.002607j
[2025-08-25 21:26:52] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -29.589747+0.003922j
[2025-08-25 21:26:57] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -29.584418-0.001138j
[2025-08-25 21:27:03] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -29.577348+0.004099j
[2025-08-25 21:27:09] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -29.595671+0.001250j
[2025-08-25 21:27:15] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -29.585061-0.001654j
[2025-08-25 21:27:21] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -29.587615-0.001729j
[2025-08-25 21:27:26] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -29.601020-0.000262j
[2025-08-25 21:27:32] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -29.579374+0.002646j
[2025-08-25 21:27:38] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -29.584873-0.000294j
[2025-08-25 21:27:44] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -29.584776+0.000400j
[2025-08-25 21:27:49] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -29.582598+0.000507j
[2025-08-25 21:27:55] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -29.588011-0.000436j
[2025-08-25 21:28:01] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -29.572956-0.000116j
[2025-08-25 21:28:07] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -29.582626+0.002191j
[2025-08-25 21:28:13] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -29.584026+0.000479j
[2025-08-25 21:28:18] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -29.586435-0.000117j
[2025-08-25 21:28:24] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -29.591282+0.002771j
[2025-08-25 21:28:30] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -29.580337-0.002479j
[2025-08-25 21:28:36] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -29.587818-0.000223j
[2025-08-25 21:28:41] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -29.583342-0.002026j
[2025-08-25 21:28:47] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -29.587664-0.000475j
[2025-08-25 21:28:53] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -29.588758-0.001281j
[2025-08-25 21:28:59] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -29.588393-0.001062j
[2025-08-25 21:29:05] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -29.576839-0.001208j
[2025-08-25 21:29:10] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -29.584934-0.002251j
[2025-08-25 21:29:16] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -29.595625-0.001288j
[2025-08-25 21:29:22] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -29.586836-0.001386j
[2025-08-25 21:29:28] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -29.569011+0.000446j
[2025-08-25 21:29:33] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -29.586482-0.000215j
[2025-08-25 21:29:39] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -29.583987+0.000540j
[2025-08-25 21:29:45] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -29.590804-0.000168j
[2025-08-25 21:29:51] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -29.586672-0.002271j
[2025-08-25 21:29:57] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -29.590817+0.001629j
[2025-08-25 21:30:02] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -29.572618+0.000040j
[2025-08-25 21:30:08] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -29.590255-0.001461j
[2025-08-25 21:30:14] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -29.587682+0.000400j
[2025-08-25 21:30:20] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -29.592768+0.000761j
[2025-08-25 21:30:25] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -29.581434+0.002392j
[2025-08-25 21:30:31] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -29.594300+0.002527j
[2025-08-25 21:30:37] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -29.585359-0.000288j
[2025-08-25 21:30:43] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -29.580286+0.001000j
[2025-08-25 21:30:49] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -29.590895-0.000551j
[2025-08-25 21:30:54] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -29.575260+0.000118j
[2025-08-25 21:31:00] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -29.597253-0.001667j
[2025-08-25 21:31:06] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -29.583408-0.001238j
[2025-08-25 21:31:12] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -29.581619+0.003026j
[2025-08-25 21:31:18] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -29.577960-0.000686j
[2025-08-25 21:31:23] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -29.583636-0.000121j
[2025-08-25 21:31:29] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -29.578641+0.001371j
[2025-08-25 21:31:35] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -29.577727-0.000472j
[2025-08-25 21:31:41] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -29.587824-0.001227j
[2025-08-25 21:31:46] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -29.580012+0.005229j
[2025-08-25 21:31:52] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -29.584445+0.000183j
[2025-08-25 21:31:58] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -29.589934-0.002291j
[2025-08-25 21:32:04] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -29.581566-0.000746j
[2025-08-25 21:32:10] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -29.581715-0.002199j
[2025-08-25 21:32:15] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -29.583466-0.002393j
[2025-08-25 21:32:21] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -29.587342+0.002297j
[2025-08-25 21:32:27] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -29.587182+0.000061j
[2025-08-25 21:32:33] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -29.585423+0.001724j
[2025-08-25 21:32:38] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -29.587581-0.001180j
[2025-08-25 21:32:44] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -29.583577-0.006173j
[2025-08-25 21:32:50] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -29.581937+0.002114j
[2025-08-25 21:32:56] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -29.583679+0.001717j
[2025-08-25 21:33:02] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -29.598470+0.002031j
[2025-08-25 21:33:07] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -29.580488+0.000565j
[2025-08-25 21:33:13] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -29.583642+0.000798j
[2025-08-25 21:33:19] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -29.592266-0.002723j
[2025-08-25 21:33:25] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -29.597984-0.003003j
[2025-08-25 21:33:31] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -29.586892-0.002663j
[2025-08-25 21:33:36] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -29.583147-0.002445j
[2025-08-25 21:33:42] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -29.588195+0.000045j
[2025-08-25 21:33:48] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -29.589728-0.001064j
[2025-08-25 21:33:54] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -29.595322+0.001349j
[2025-08-25 21:33:59] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -29.598464+0.000222j
[2025-08-25 21:34:05] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -29.581774-0.000864j
[2025-08-25 21:34:11] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -29.584789+0.002820j
[2025-08-25 21:34:17] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -29.586529-0.000605j
[2025-08-25 21:34:23] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -29.589269+0.002401j
[2025-08-25 21:34:28] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -29.591077-0.000417j
[2025-08-25 21:34:34] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -29.575002+0.000715j
[2025-08-25 21:34:40] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -29.593916-0.000753j
[2025-08-25 21:34:46] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -29.592589-0.002297j
[2025-08-25 21:34:51] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -29.583235-0.000074j
[2025-08-25 21:34:57] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -29.590379-0.000801j
[2025-08-25 21:35:03] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -29.588945-0.001848j
[2025-08-25 21:35:09] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -29.582690+0.000265j
[2025-08-25 21:35:09] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-25 21:35:15] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -29.587213-0.001406j
[2025-08-25 21:35:20] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -29.579569-0.001177j
[2025-08-25 21:35:26] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -29.576621+0.001165j
[2025-08-25 21:35:32] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -29.585896+0.000074j
[2025-08-25 21:35:38] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -29.591145+0.001338j
[2025-08-25 21:35:44] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -29.583568-0.001956j
[2025-08-25 21:35:49] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -29.591772-0.002364j
[2025-08-25 21:35:55] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -29.585133-0.001241j
[2025-08-25 21:36:01] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -29.575756-0.000627j
[2025-08-25 21:36:07] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -29.578516-0.000088j
[2025-08-25 21:36:12] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -29.586698+0.000574j
[2025-08-25 21:36:18] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -29.586561-0.003564j
[2025-08-25 21:36:24] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -29.586249+0.000419j
[2025-08-25 21:36:30] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -29.591690-0.000146j
[2025-08-25 21:36:36] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -29.584562-0.002371j
[2025-08-25 21:36:41] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -29.576334+0.001097j
[2025-08-25 21:36:47] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -29.584319+0.001633j
[2025-08-25 21:36:53] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -29.582753-0.000273j
[2025-08-25 21:36:59] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -29.589985+0.000551j
[2025-08-25 21:37:05] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -29.579126+0.002455j
[2025-08-25 21:37:10] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -29.588122+0.000577j
[2025-08-25 21:37:16] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -29.585942+0.000519j
[2025-08-25 21:37:22] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -29.587292-0.001035j
[2025-08-25 21:37:28] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -29.588535-0.001384j
[2025-08-25 21:37:33] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -29.590152-0.000420j
[2025-08-25 21:37:39] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -29.575967+0.000084j
[2025-08-25 21:37:45] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -29.579437+0.001776j
[2025-08-25 21:37:51] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -29.583297-0.001714j
[2025-08-25 21:37:57] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -29.575937+0.002344j
[2025-08-25 21:38:02] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -29.576602+0.000821j
[2025-08-25 21:38:08] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -29.592009+0.001254j
[2025-08-25 21:38:14] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -29.585233-0.001780j
[2025-08-25 21:38:20] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -29.587037-0.000218j
[2025-08-25 21:38:25] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -29.578477-0.001471j
[2025-08-25 21:38:31] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -29.583512-0.003480j
[2025-08-25 21:38:37] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -29.585183+0.004711j
[2025-08-25 21:38:43] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -29.583827+0.000424j
[2025-08-25 21:38:49] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -29.578790+0.001724j
[2025-08-25 21:38:54] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -29.590556-0.000670j
[2025-08-25 21:39:00] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -29.588767-0.000656j
[2025-08-25 21:39:06] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -29.582047-0.001251j
[2025-08-25 21:39:12] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -29.595777-0.001247j
[2025-08-25 21:39:18] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -29.590849-0.001433j
[2025-08-25 21:39:23] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -29.581307+0.001299j
[2025-08-25 21:39:29] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -29.591725+0.000218j
[2025-08-25 21:39:35] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -29.574066-0.001044j
[2025-08-25 21:39:41] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -29.579413+0.004106j
[2025-08-25 21:39:46] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -29.580811-0.000735j
[2025-08-25 21:39:52] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -29.591532+0.000138j
[2025-08-25 21:39:58] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -29.583744-0.000210j
[2025-08-25 21:40:04] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -29.583005-0.001700j
[2025-08-25 21:40:10] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -29.589128-0.002343j
[2025-08-25 21:40:15] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -29.590098-0.001158j
[2025-08-25 21:40:21] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -29.585256+0.002044j
[2025-08-25 21:40:27] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -29.586959-0.001594j
[2025-08-25 21:40:33] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -29.587288+0.000832j
[2025-08-25 21:40:38] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -29.584799-0.007834j
[2025-08-25 21:40:44] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -29.579218+0.002557j
[2025-08-25 21:40:50] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -29.580184-0.001693j
[2025-08-25 21:40:56] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -29.577266-0.001550j
[2025-08-25 21:41:02] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -29.587156+0.001460j
[2025-08-25 21:41:07] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -29.584032+0.001074j
[2025-08-25 21:41:13] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -29.584012+0.001148j
[2025-08-25 21:41:19] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -29.588424-0.001580j
[2025-08-25 21:41:25] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -29.596438-0.001608j
[2025-08-25 21:41:31] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -29.578695-0.000818j
[2025-08-25 21:41:36] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -29.587574+0.001217j
[2025-08-25 21:41:42] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -29.590089+0.001617j
[2025-08-25 21:41:48] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -29.591757-0.003644j
[2025-08-25 21:41:54] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -29.587499-0.000166j
[2025-08-25 21:41:59] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -29.574440-0.000317j
[2025-08-25 21:42:05] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -29.584473-0.001618j
[2025-08-25 21:42:11] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -29.582105-0.000008j
[2025-08-25 21:42:17] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -29.592766-0.003933j
[2025-08-25 21:42:23] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -29.571546-0.001152j
[2025-08-25 21:42:28] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -29.586626+0.000888j
[2025-08-25 21:42:34] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -29.585757+0.000278j
[2025-08-25 21:42:40] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -29.584887-0.001854j
[2025-08-25 21:42:46] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -29.592674-0.003338j
[2025-08-25 21:42:51] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -29.579252+0.001830j
[2025-08-25 21:42:57] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -29.586786+0.002924j
[2025-08-25 21:43:03] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -29.577241-0.000908j
[2025-08-25 21:43:09] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -29.580492+0.004913j
[2025-08-25 21:43:15] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -29.580943-0.001288j
[2025-08-25 21:43:20] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -29.584935+0.003973j
[2025-08-25 21:43:26] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -29.592761-0.001629j
[2025-08-25 21:43:32] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -29.592042-0.002856j
[2025-08-25 21:43:38] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -29.594614+0.000235j
[2025-08-25 21:43:44] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -29.578637-0.000333j
[2025-08-25 21:43:49] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -29.575690-0.003729j
[2025-08-25 21:43:55] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -29.577863+0.001571j
[2025-08-25 21:44:01] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -29.581633+0.004003j
[2025-08-25 21:44:07] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -29.579715-0.001305j
[2025-08-25 21:44:13] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -29.582128-0.001518j
[2025-08-25 21:44:18] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -29.595584+0.001391j
[2025-08-25 21:44:24] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -29.586885+0.002601j
[2025-08-25 21:44:30] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -29.587976+0.004359j
[2025-08-25 21:44:36] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -29.591199+0.001925j
[2025-08-25 21:44:41] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -29.585631-0.003414j
[2025-08-25 21:44:47] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -29.593532-0.007679j
[2025-08-25 21:44:47] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-25 21:44:53] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -29.578346+0.002390j
[2025-08-25 21:44:59] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -29.582252-0.000413j
[2025-08-25 21:45:05] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -29.577131+0.005144j
[2025-08-25 21:45:10] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -29.585291-0.002558j
[2025-08-25 21:45:16] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -29.577542-0.001299j
[2025-08-25 21:45:22] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -29.588020+0.000335j
[2025-08-25 21:45:28] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -29.583978+0.001573j
[2025-08-25 21:45:33] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -29.578762-0.000038j
[2025-08-25 21:45:39] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -29.589591+0.003921j
[2025-08-25 21:45:45] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -29.587863+0.001229j
[2025-08-25 21:45:51] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -29.582231-0.000102j
[2025-08-25 21:45:57] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -29.582773-0.001622j
[2025-08-25 21:46:02] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -29.580398+0.000712j
[2025-08-25 21:46:08] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -29.584334+0.000159j
[2025-08-25 21:46:14] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -29.584663-0.001508j
[2025-08-25 21:46:20] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -29.590693-0.000853j
[2025-08-25 21:46:26] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -29.596704-0.000891j
[2025-08-25 21:46:31] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -29.585847+0.002055j
[2025-08-25 21:46:37] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -29.585122+0.001252j
[2025-08-25 21:46:43] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -29.591300-0.002148j
[2025-08-25 21:46:49] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -29.588878+0.001045j
[2025-08-25 21:46:54] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -29.590520-0.000363j
[2025-08-25 21:47:00] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -29.583717-0.000362j
[2025-08-25 21:47:06] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -29.583808+0.000606j
[2025-08-25 21:47:12] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -29.580132-0.000849j
[2025-08-25 21:47:18] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -29.577406+0.000256j
[2025-08-25 21:47:23] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -29.588102+0.000927j
[2025-08-25 21:47:29] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -29.591126-0.001110j
[2025-08-25 21:47:35] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -29.582657-0.001093j
[2025-08-25 21:47:41] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -29.584085+0.001230j
[2025-08-25 21:47:47] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -29.590059+0.002302j
[2025-08-25 21:47:52] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -29.581702-0.000438j
[2025-08-25 21:47:58] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -29.581811-0.004288j
[2025-08-25 21:48:04] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -29.588798-0.000044j
[2025-08-25 21:48:10] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -29.583522+0.001793j
[2025-08-25 21:48:15] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -29.590124+0.003626j
[2025-08-25 21:48:21] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -29.586868+0.001877j
[2025-08-25 21:48:27] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -29.582593+0.001001j
[2025-08-25 21:48:33] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -29.579516-0.001081j
[2025-08-25 21:48:39] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -29.585473+0.001141j
[2025-08-25 21:48:44] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -29.587165+0.000837j
[2025-08-25 21:48:50] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -29.583619-0.000591j
[2025-08-25 21:48:56] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -29.586480+0.000451j
[2025-08-25 21:49:02] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -29.577570+0.001109j
[2025-08-25 21:49:08] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -29.591890-0.002400j
[2025-08-25 21:49:13] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -29.585930+0.000875j
[2025-08-25 21:49:19] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -29.580905-0.001395j
[2025-08-25 21:49:25] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -29.611170-0.001496j
[2025-08-25 21:49:31] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -29.592286-0.001741j
[2025-08-25 21:49:36] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -29.583995-0.001544j
[2025-08-25 21:49:36] ✅ Training completed | Restarts: 2
[2025-08-25 21:49:36] ============================================================
[2025-08-25 21:49:36] Training completed | Runtime: 6110.1s
[2025-08-25 21:49:38] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-25 21:49:38] ============================================================
