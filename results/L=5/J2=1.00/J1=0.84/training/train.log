[2025-08-27 03:03:14] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.83/training/checkpoints/final_GCNN.pkl
[2025-08-27 03:03:14]   - 迭代次数: final
[2025-08-27 03:03:14]   - 能量: -46.783297-0.001752j ± 0.008233
[2025-08-27 03:03:14]   - 时间戳: 2025-08-27T03:03:02.301159+08:00
[2025-08-27 03:03:24] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 03:03:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 03:03:24] ==================================================
[2025-08-27 03:03:24] GCNN for Shastry-Sutherland Model
[2025-08-27 03:03:24] ==================================================
[2025-08-27 03:03:24] System parameters:
[2025-08-27 03:03:24]   - System size: L=5, N=100
[2025-08-27 03:03:24]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-08-27 03:03:24] --------------------------------------------------
[2025-08-27 03:03:24] Model parameters:
[2025-08-27 03:03:24]   - Number of layers = 4
[2025-08-27 03:03:24]   - Number of features = 4
[2025-08-27 03:03:24]   - Total parameters = 19628
[2025-08-27 03:03:24] --------------------------------------------------
[2025-08-27 03:03:24] Training parameters:
[2025-08-27 03:03:24]   - Learning rate: 0.015
[2025-08-27 03:03:24]   - Total iterations: 450
[2025-08-27 03:03:24]   - Annealing cycles: 2
[2025-08-27 03:03:24]   - Initial period: 150
[2025-08-27 03:03:24]   - Period multiplier: 2.0
[2025-08-27 03:03:24]   - Temperature range: 0.0-1.0
[2025-08-27 03:03:24]   - Samples: 4096
[2025-08-27 03:03:24]   - Discarded samples: 0
[2025-08-27 03:03:24]   - Chunk size: 2048
[2025-08-27 03:03:24]   - Diagonal shift: 0.2
[2025-08-27 03:03:24]   - Gradient clipping: 1.0
[2025-08-27 03:03:24]   - Checkpoint enabled: interval=50
[2025-08-27 03:03:24]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.84/training/checkpoints
[2025-08-27 03:03:24] --------------------------------------------------
[2025-08-27 03:03:24] Device status:
[2025-08-27 03:03:24]   - Devices model: NVIDIA H200 NVL
[2025-08-27 03:03:24]   - Number of devices: 1
[2025-08-27 03:03:24]   - Sharding: True
[2025-08-27 03:03:24] ============================================================
[2025-08-27 03:04:00] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -47.453919-0.008575j
[2025-08-27 03:04:22] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -47.430535-0.003037j
[2025-08-27 03:04:28] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -47.439999-0.003909j
[2025-08-27 03:04:34] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -47.445253+0.003053j
[2025-08-27 03:04:39] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -47.432478+0.003119j
[2025-08-27 03:04:45] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -47.455681-0.000460j
[2025-08-27 03:04:50] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -47.436811+0.001757j
[2025-08-27 03:04:56] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -47.425363+0.003688j
[2025-08-27 03:05:01] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -47.440628+0.002538j
[2025-08-27 03:05:07] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -47.429480+0.004649j
[2025-08-27 03:05:12] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -47.434597-0.001671j
[2025-08-27 03:05:18] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -47.433861+0.001991j
[2025-08-27 03:05:23] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -47.431673+0.005273j
[2025-08-27 03:05:29] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -47.443051-0.004104j
[2025-08-27 03:05:35] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -47.436952+0.000162j
[2025-08-27 03:05:40] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -47.435386-0.003042j
[2025-08-27 03:05:46] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -47.411893+0.001662j
[2025-08-27 03:05:51] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -47.455848-0.003789j
[2025-08-27 03:05:57] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -47.441800-0.003219j
[2025-08-27 03:06:02] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -47.429623-0.000683j
[2025-08-27 03:06:08] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -47.422775+0.002045j
[2025-08-27 03:06:13] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -47.422260+0.000934j
[2025-08-27 03:06:19] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -47.434070+0.006431j
[2025-08-27 03:06:25] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -47.430058+0.002595j
[2025-08-27 03:06:30] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -47.441291+0.001969j
[2025-08-27 03:06:36] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -47.445244-0.001343j
[2025-08-27 03:06:41] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -47.440282+0.005452j
[2025-08-27 03:06:47] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -47.428457+0.000129j
[2025-08-27 03:06:52] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -47.429404-0.001776j
[2025-08-27 03:06:58] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -47.443847-0.003750j
[2025-08-27 03:07:03] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -47.434983-0.001866j
[2025-08-27 03:07:09] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -47.435916-0.002912j
[2025-08-27 03:07:15] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -47.424873-0.002796j
[2025-08-27 03:07:20] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -47.427990-0.002163j
[2025-08-27 03:07:26] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -47.438527+0.001888j
[2025-08-27 03:07:31] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -47.437715+0.000601j
[2025-08-27 03:07:37] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -47.433079+0.003181j
[2025-08-27 03:07:42] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -47.438757-0.001287j
[2025-08-27 03:07:48] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -47.427404-0.001830j
[2025-08-27 03:07:53] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -47.440520+0.000892j
[2025-08-27 03:07:59] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -47.438237-0.004009j
[2025-08-27 03:08:04] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -47.440825-0.000423j
[2025-08-27 03:08:10] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -47.427927-0.000741j
[2025-08-27 03:08:16] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -47.433030+0.004256j
[2025-08-27 03:08:21] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -47.432759+0.000124j
[2025-08-27 03:08:27] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -47.450486+0.001428j
[2025-08-27 03:08:32] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -47.418618+0.002289j
[2025-08-27 03:08:38] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -47.428938-0.002614j
[2025-08-27 03:08:43] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -47.433519-0.000398j
[2025-08-27 03:08:49] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -47.424929+0.001649j
[2025-08-27 03:08:49] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 03:08:54] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -47.437182-0.004291j
[2025-08-27 03:09:00] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -47.424257+0.001607j
[2025-08-27 03:09:06] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -47.438764+0.001323j
[2025-08-27 03:09:11] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -47.436250+0.002549j
[2025-08-27 03:09:17] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -47.441776-0.000875j
[2025-08-27 03:09:22] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -47.442972+0.004031j
[2025-08-27 03:09:28] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -47.440079+0.000052j
[2025-08-27 03:09:33] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -47.423553+0.001239j
[2025-08-27 03:09:39] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -47.442044-0.000160j
[2025-08-27 03:09:44] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -47.443083-0.001687j
[2025-08-27 03:09:50] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -47.428294-0.000688j
[2025-08-27 03:09:56] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -47.430898-0.000384j
[2025-08-27 03:10:01] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -47.416084-0.000698j
[2025-08-27 03:10:07] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -47.438969+0.004542j
[2025-08-27 03:10:12] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -47.455250+0.000155j
[2025-08-27 03:10:18] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -47.434014-0.000678j
[2025-08-27 03:10:23] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -47.441711+0.001421j
[2025-08-27 03:10:29] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -47.414489-0.001208j
[2025-08-27 03:10:34] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -47.447067-0.003561j
[2025-08-27 03:10:40] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -47.427664+0.003438j
[2025-08-27 03:10:46] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -47.434662+0.000605j
[2025-08-27 03:10:51] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -47.438097+0.001675j
[2025-08-27 03:10:57] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -47.420778-0.004648j
[2025-08-27 03:11:02] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -47.434779+0.000891j
[2025-08-27 03:11:08] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -47.431636+0.003173j
[2025-08-27 03:11:13] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -47.435566-0.002955j
[2025-08-27 03:11:19] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -47.441215-0.000602j
[2025-08-27 03:11:24] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -47.428709-0.001992j
[2025-08-27 03:11:30] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -47.438992-0.000608j
[2025-08-27 03:11:36] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -47.432245-0.000604j
[2025-08-27 03:11:41] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -47.428535-0.000343j
[2025-08-27 03:11:47] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -47.433042+0.000868j
[2025-08-27 03:11:52] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -47.438813-0.001229j
[2025-08-27 03:11:58] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -47.438151+0.002107j
[2025-08-27 03:12:03] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -47.432713-0.003861j
[2025-08-27 03:12:09] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -47.440307+0.002325j
[2025-08-27 03:12:14] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -47.444914-0.000345j
[2025-08-27 03:12:20] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -47.434013-0.002959j
[2025-08-27 03:12:26] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -47.444414+0.001671j
[2025-08-27 03:12:31] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -47.426674+0.002875j
[2025-08-27 03:12:37] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -47.428480-0.001743j
[2025-08-27 03:12:42] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -47.433996-0.000097j
[2025-08-27 03:12:48] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -47.434858-0.008927j
[2025-08-27 03:12:53] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -47.418817-0.001065j
[2025-08-27 03:12:59] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -47.436688-0.000294j
[2025-08-27 03:13:04] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -47.435453-0.001980j
[2025-08-27 03:13:10] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -47.444607+0.001301j
[2025-08-27 03:13:16] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -47.419776+0.003131j
[2025-08-27 03:13:21] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -47.427845+0.000795j
[2025-08-27 03:13:27] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -47.438924+0.005778j
[2025-08-27 03:13:27] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 03:13:32] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -47.427190-0.002746j
[2025-08-27 03:13:38] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -47.433702-0.001735j
[2025-08-27 03:13:43] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -47.438889+0.002012j
[2025-08-27 03:13:49] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -47.421037-0.003706j
[2025-08-27 03:13:55] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -47.415248-0.001356j
[2025-08-27 03:14:00] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -47.450063+0.004728j
[2025-08-27 03:14:06] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -47.417678-0.002656j
[2025-08-27 03:14:11] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -47.440192+0.000902j
[2025-08-27 03:14:17] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -47.438739+0.003345j
[2025-08-27 03:14:22] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -47.432086-0.002528j
[2025-08-27 03:14:28] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -47.425794+0.001127j
[2025-08-27 03:14:33] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -47.430536+0.001616j
[2025-08-27 03:14:39] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -47.435704+0.001794j
[2025-08-27 03:14:44] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -47.420497+0.000152j
[2025-08-27 03:14:50] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -47.423829-0.003132j
[2025-08-27 03:14:56] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -47.429965+0.002919j
[2025-08-27 03:15:01] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -47.446339+0.001812j
[2025-08-27 03:15:07] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -47.438872+0.001963j
[2025-08-27 03:15:12] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -47.444517+0.000159j
[2025-08-27 03:15:18] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -47.440856-0.001368j
[2025-08-27 03:15:23] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -47.438475-0.001086j
[2025-08-27 03:15:29] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -47.435272+0.001324j
[2025-08-27 03:15:34] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -47.421613-0.004763j
[2025-08-27 03:15:40] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -47.425215-0.000349j
[2025-08-27 03:15:45] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -47.429635+0.000101j
[2025-08-27 03:15:51] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -47.450018-0.004848j
[2025-08-27 03:15:57] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -47.441862+0.000294j
[2025-08-27 03:16:02] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -47.442958+0.001227j
[2025-08-27 03:16:08] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -47.430286-0.004147j
[2025-08-27 03:16:13] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -47.439404-0.003102j
[2025-08-27 03:16:19] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -47.415845+0.000947j
[2025-08-27 03:16:24] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -47.425843+0.000991j
[2025-08-27 03:16:30] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -47.427958+0.002816j
[2025-08-27 03:16:35] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -47.438186+0.003053j
[2025-08-27 03:16:41] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -47.452342+0.000237j
[2025-08-27 03:16:47] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -47.441993-0.004068j
[2025-08-27 03:16:52] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -47.417760+0.000480j
[2025-08-27 03:16:58] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -47.435061-0.002710j
[2025-08-27 03:17:03] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -47.434098-0.001756j
[2025-08-27 03:17:09] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -47.450069+0.004082j
[2025-08-27 03:17:14] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -47.441201+0.003095j
[2025-08-27 03:17:20] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -47.429618+0.000537j
[2025-08-27 03:17:25] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -47.439128-0.000388j
[2025-08-27 03:17:31] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -47.418473+0.002989j
[2025-08-27 03:17:36] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -47.442279-0.000088j
[2025-08-27 03:17:42] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -47.425020-0.000509j
[2025-08-27 03:17:48] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -47.439510+0.002661j
[2025-08-27 03:17:53] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -47.424420+0.000293j
[2025-08-27 03:17:59] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -47.444661-0.000992j
[2025-08-27 03:18:04] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -47.433305-0.005945j
[2025-08-27 03:18:04] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 03:18:04] RESTART #1 | Period: 300
[2025-08-27 03:18:10] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -47.439517-0.000312j
[2025-08-27 03:18:15] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -47.438919-0.000933j
[2025-08-27 03:18:21] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -47.448448-0.002419j
[2025-08-27 03:18:26] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -47.427754+0.000351j
[2025-08-27 03:18:32] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -47.428767+0.000324j
[2025-08-27 03:18:38] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -47.428095+0.000283j
[2025-08-27 03:18:43] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -47.441216-0.001326j
[2025-08-27 03:18:49] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -47.430664-0.003248j
[2025-08-27 03:18:54] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -47.428081-0.001953j
[2025-08-27 03:19:00] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -47.422715+0.002404j
[2025-08-27 03:19:05] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -47.429626+0.004970j
[2025-08-27 03:19:11] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -47.452705-0.000531j
[2025-08-27 03:19:16] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -47.422415+0.000110j
[2025-08-27 03:19:22] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -47.433841+0.005245j
[2025-08-27 03:19:28] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -47.434338+0.000243j
[2025-08-27 03:19:33] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -47.417702-0.000191j
[2025-08-27 03:19:39] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -47.425988-0.000188j
[2025-08-27 03:19:44] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -47.438068+0.002471j
[2025-08-27 03:19:50] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -47.440268-0.000071j
[2025-08-27 03:19:55] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -47.441892-0.002814j
[2025-08-27 03:20:01] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -47.442784-0.002103j
[2025-08-27 03:20:06] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -47.442032-0.000020j
[2025-08-27 03:20:12] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -47.438871-0.001271j
[2025-08-27 03:20:17] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -47.452573+0.009700j
[2025-08-27 03:20:23] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -47.423881-0.000374j
[2025-08-27 03:20:29] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -47.445638-0.001653j
[2025-08-27 03:20:34] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -47.443415-0.002608j
[2025-08-27 03:20:40] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -47.442084-0.000110j
[2025-08-27 03:20:45] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -47.420620+0.000248j
[2025-08-27 03:20:51] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -47.438254-0.000973j
[2025-08-27 03:20:56] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -47.435245-0.000572j
[2025-08-27 03:21:02] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -47.405630-0.000742j
[2025-08-27 03:21:07] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -47.430879-0.005213j
[2025-08-27 03:21:13] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -47.432820+0.004377j
[2025-08-27 03:21:19] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -47.427563+0.000087j
[2025-08-27 03:21:24] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -47.451567-0.000593j
[2025-08-27 03:21:30] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -47.440408-0.000512j
[2025-08-27 03:21:35] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -47.433399+0.001479j
[2025-08-27 03:21:41] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -47.437971+0.000377j
[2025-08-27 03:21:46] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -47.432554+0.000813j
[2025-08-27 03:21:52] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -47.438647+0.004194j
[2025-08-27 03:21:57] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -47.435159-0.002274j
[2025-08-27 03:22:03] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -47.419247+0.001964j
[2025-08-27 03:22:09] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -47.436848+0.003036j
[2025-08-27 03:22:14] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -47.441132-0.003166j
[2025-08-27 03:22:20] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -47.432589+0.002912j
[2025-08-27 03:22:25] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -47.434652-0.002474j
[2025-08-27 03:22:31] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -47.434361-0.001527j
[2025-08-27 03:22:36] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -47.434549-0.001344j
[2025-08-27 03:22:42] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -47.431614-0.005417j
[2025-08-27 03:22:42] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 03:22:47] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -47.435340+0.003012j
[2025-08-27 03:22:53] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -47.430982+0.000514j
[2025-08-27 03:22:58] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -47.438192-0.001547j
[2025-08-27 03:23:04] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -47.439503+0.000896j
[2025-08-27 03:23:10] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -47.429733-0.001099j
[2025-08-27 03:23:15] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -47.423567-0.001702j
[2025-08-27 03:23:21] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -47.435706-0.000822j
[2025-08-27 03:23:26] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -47.432604+0.002459j
[2025-08-27 03:23:32] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -47.420882+0.001624j
[2025-08-27 03:23:37] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -47.447355+0.000013j
[2025-08-27 03:23:43] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -47.432159+0.002447j
[2025-08-27 03:23:48] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -47.428086+0.001834j
[2025-08-27 03:23:54] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -47.445113+0.000412j
[2025-08-27 03:23:59] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -47.439239-0.003270j
[2025-08-27 03:24:05] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -47.435145+0.001402j
[2025-08-27 03:24:11] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -47.437288-0.003021j
[2025-08-27 03:24:16] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -47.437594-0.003240j
[2025-08-27 03:24:22] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -47.444962-0.002541j
[2025-08-27 03:24:27] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -47.428383-0.000598j
[2025-08-27 03:24:33] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -47.453036+0.000182j
[2025-08-27 03:24:38] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -47.440516+0.000750j
[2025-08-27 03:24:44] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -47.427395+0.002063j
[2025-08-27 03:24:49] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -47.438043+0.000527j
[2025-08-27 03:24:55] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -47.418645+0.005532j
[2025-08-27 03:25:01] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -47.437841+0.001645j
[2025-08-27 03:25:06] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -47.447917+0.003344j
[2025-08-27 03:25:12] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -47.437434+0.001921j
[2025-08-27 03:25:17] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -47.429088-0.005583j
[2025-08-27 03:25:23] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -47.432590-0.001185j
[2025-08-27 03:25:28] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -47.448431-0.004700j
[2025-08-27 03:25:34] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -47.423221+0.000067j
[2025-08-27 03:25:39] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -47.431394-0.000891j
[2025-08-27 03:25:45] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -47.435758-0.004502j
[2025-08-27 03:25:50] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -47.427960+0.002598j
[2025-08-27 03:25:56] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -47.430644-0.003127j
[2025-08-27 03:26:02] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -47.437101-0.000582j
[2025-08-27 03:26:07] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -47.453465+0.004326j
[2025-08-27 03:26:13] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -47.428499+0.002232j
[2025-08-27 03:26:18] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -47.436707+0.002170j
[2025-08-27 03:26:24] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -47.422234-0.000220j
[2025-08-27 03:26:29] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -47.436780+0.004290j
[2025-08-27 03:26:35] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -47.420766+0.004362j
[2025-08-27 03:26:40] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -47.445934+0.000021j
[2025-08-27 03:26:46] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -47.431508+0.001571j
[2025-08-27 03:26:51] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -47.428092+0.000993j
[2025-08-27 03:26:57] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -47.437090+0.002563j
[2025-08-27 03:27:03] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -47.445675-0.000295j
[2025-08-27 03:27:08] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -47.422489-0.001167j
[2025-08-27 03:27:14] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -47.435280+0.006722j
[2025-08-27 03:27:19] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -47.439983+0.000133j
[2025-08-27 03:27:19] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 03:27:25] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -47.427750+0.006967j
[2025-08-27 03:27:31] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -47.433202-0.000790j
[2025-08-27 03:27:36] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -47.429011+0.000190j
[2025-08-27 03:27:42] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -47.438266+0.002567j
[2025-08-27 03:27:47] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -47.428074+0.002848j
[2025-08-27 03:27:53] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -47.451114-0.007325j
[2025-08-27 03:27:58] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -47.436138-0.001100j
[2025-08-27 03:28:04] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -47.438926-0.000831j
[2025-08-27 03:28:09] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -47.430360+0.000198j
[2025-08-27 03:28:15] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -47.429413+0.001221j
[2025-08-27 03:28:20] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -47.441140+0.002113j
[2025-08-27 03:28:26] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -47.440227+0.001715j
[2025-08-27 03:28:32] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -47.448040+0.001081j
[2025-08-27 03:28:37] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -47.426910+0.002423j
[2025-08-27 03:28:43] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -47.434068+0.001556j
[2025-08-27 03:28:48] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -47.440627-0.001615j
[2025-08-27 03:28:54] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -47.430203+0.002379j
[2025-08-27 03:28:59] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -47.434962-0.002119j
[2025-08-27 03:29:05] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -47.438778-0.000344j
[2025-08-27 03:29:10] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -47.429582-0.000007j
[2025-08-27 03:29:16] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -47.432788-0.001323j
[2025-08-27 03:29:21] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -47.436815+0.000656j
[2025-08-27 03:29:27] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -47.438640+0.001238j
[2025-08-27 03:29:33] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -47.442135+0.003274j
[2025-08-27 03:29:38] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -47.435238-0.003423j
[2025-08-27 03:29:44] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -47.430413-0.000973j
[2025-08-27 03:29:49] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -47.445133+0.001228j
[2025-08-27 03:29:55] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -47.423271-0.001638j
[2025-08-27 03:30:00] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -47.435865-0.000600j
[2025-08-27 03:30:06] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -47.439961-0.000098j
[2025-08-27 03:30:11] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -47.430421-0.000496j
[2025-08-27 03:30:17] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -47.442153-0.000526j
[2025-08-27 03:30:22] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -47.431395-0.005887j
[2025-08-27 03:30:28] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -47.438516-0.001591j
[2025-08-27 03:30:34] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -47.434875+0.001572j
[2025-08-27 03:30:39] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -47.436705+0.001169j
[2025-08-27 03:30:45] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -47.434516-0.003099j
[2025-08-27 03:30:50] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -47.445711-0.001188j
[2025-08-27 03:30:56] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -47.442748-0.002447j
[2025-08-27 03:31:01] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -47.446072-0.003488j
[2025-08-27 03:31:07] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -47.437272+0.001295j
[2025-08-27 03:31:12] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -47.439324-0.002373j
[2025-08-27 03:31:18] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -47.434955-0.002835j
[2025-08-27 03:31:24] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -47.440852-0.001975j
[2025-08-27 03:31:29] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -47.438943-0.003475j
[2025-08-27 03:31:35] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -47.428185-0.001670j
[2025-08-27 03:31:40] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -47.436807+0.004906j
[2025-08-27 03:31:46] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -47.431912-0.001443j
[2025-08-27 03:31:51] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -47.430553+0.000298j
[2025-08-27 03:31:57] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -47.420973-0.000291j
[2025-08-27 03:31:57] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 03:32:02] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -47.430792+0.000092j
[2025-08-27 03:32:08] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -47.439838+0.001242j
[2025-08-27 03:32:13] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -47.425382-0.004518j
[2025-08-27 03:32:19] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -47.441898+0.000579j
[2025-08-27 03:32:25] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -47.441734-0.000559j
[2025-08-27 03:32:30] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -47.444069+0.000082j
[2025-08-27 03:32:36] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -47.442967+0.001187j
[2025-08-27 03:32:41] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -47.431213-0.002236j
[2025-08-27 03:32:47] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -47.432040+0.000780j
[2025-08-27 03:32:52] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -47.428725+0.001013j
[2025-08-27 03:32:58] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -47.437544+0.002689j
[2025-08-27 03:33:03] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -47.439005+0.001822j
[2025-08-27 03:33:09] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -47.438791-0.001342j
[2025-08-27 03:33:14] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -47.431721+0.005131j
[2025-08-27 03:33:20] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -47.428678+0.000590j
[2025-08-27 03:33:26] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -47.423857-0.000338j
[2025-08-27 03:33:31] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -47.445346-0.001346j
[2025-08-27 03:33:37] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -47.444041-0.004781j
[2025-08-27 03:33:42] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -47.439716+0.001914j
[2025-08-27 03:33:48] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -47.427688+0.004168j
[2025-08-27 03:33:53] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -47.422532-0.001937j
[2025-08-27 03:33:59] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -47.430769-0.003978j
[2025-08-27 03:34:04] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -47.434249-0.000860j
[2025-08-27 03:34:10] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -47.437380-0.000821j
[2025-08-27 03:34:15] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -47.421655+0.001737j
[2025-08-27 03:34:21] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -47.450271+0.001369j
[2025-08-27 03:34:27] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -47.442524+0.000510j
[2025-08-27 03:34:32] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -47.450470+0.002414j
[2025-08-27 03:34:38] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -47.425523-0.005297j
[2025-08-27 03:34:43] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -47.424526-0.000079j
[2025-08-27 03:34:49] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -47.440749-0.003744j
[2025-08-27 03:34:54] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -47.423207+0.001835j
[2025-08-27 03:35:00] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -47.427356+0.002795j
[2025-08-27 03:35:05] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -47.442054+0.004543j
[2025-08-27 03:35:11] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -47.425335+0.007801j
[2025-08-27 03:35:17] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -47.440345+0.001313j
[2025-08-27 03:35:22] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -47.446048+0.001033j
[2025-08-27 03:35:28] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -47.440693-0.000727j
[2025-08-27 03:35:33] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -47.425200+0.001364j
[2025-08-27 03:35:39] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -47.433504+0.000625j
[2025-08-27 03:35:44] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -47.431646-0.000728j
[2025-08-27 03:35:50] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -47.430169+0.001673j
[2025-08-27 03:35:55] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -47.430048+0.002359j
[2025-08-27 03:36:01] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -47.445593-0.001925j
[2025-08-27 03:36:06] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -47.428858-0.001375j
[2025-08-27 03:36:12] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -47.438696-0.002338j
[2025-08-27 03:36:18] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -47.443848-0.000134j
[2025-08-27 03:36:23] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -47.431550+0.006140j
[2025-08-27 03:36:29] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -47.453107+0.003287j
[2025-08-27 03:36:34] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -47.441747-0.003978j
[2025-08-27 03:36:34] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 03:36:40] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -47.438414-0.001748j
[2025-08-27 03:36:45] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -47.432864-0.000977j
[2025-08-27 03:36:51] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -47.436255-0.002506j
[2025-08-27 03:36:56] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -47.435038-0.002475j
[2025-08-27 03:37:02] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -47.447301+0.002130j
[2025-08-27 03:37:08] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -47.435289-0.002054j
[2025-08-27 03:37:13] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -47.447133+0.000052j
[2025-08-27 03:37:19] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -47.427899+0.000307j
[2025-08-27 03:37:24] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -47.433567-0.002764j
[2025-08-27 03:37:30] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -47.432670-0.001073j
[2025-08-27 03:37:35] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -47.426555+0.001496j
[2025-08-27 03:37:41] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -47.440312+0.002509j
[2025-08-27 03:37:46] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -47.419363-0.000140j
[2025-08-27 03:37:52] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -47.446293+0.003016j
[2025-08-27 03:37:57] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -47.429657+0.003600j
[2025-08-27 03:38:03] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -47.430448-0.001502j
[2025-08-27 03:38:09] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -47.432211-0.003967j
[2025-08-27 03:38:14] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -47.435489-0.005005j
[2025-08-27 03:38:20] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -47.423700-0.002023j
[2025-08-27 03:38:25] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -47.427174-0.005438j
[2025-08-27 03:38:31] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -47.449384-0.003678j
[2025-08-27 03:38:36] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -47.444301+0.005765j
[2025-08-27 03:38:42] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -47.441517-0.003409j
[2025-08-27 03:38:47] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -47.424276+0.000541j
[2025-08-27 03:38:53] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -47.425077-0.002517j
[2025-08-27 03:38:58] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -47.436786-0.000710j
[2025-08-27 03:39:04] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -47.436557+0.004344j
[2025-08-27 03:39:10] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -47.430338-0.001026j
[2025-08-27 03:39:15] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -47.432475-0.000213j
[2025-08-27 03:39:21] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -47.419927+0.002004j
[2025-08-27 03:39:26] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -47.431625-0.004803j
[2025-08-27 03:39:32] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -47.429874-0.001587j
[2025-08-27 03:39:37] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -47.434636-0.003186j
[2025-08-27 03:39:43] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -47.428290+0.001130j
[2025-08-27 03:39:48] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -47.438210-0.001498j
[2025-08-27 03:39:54] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -47.446453+0.001239j
[2025-08-27 03:39:59] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -47.437590+0.000196j
[2025-08-27 03:40:05] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -47.437572-0.001335j
[2025-08-27 03:40:11] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -47.446005-0.000007j
[2025-08-27 03:40:16] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -47.442970+0.004420j
[2025-08-27 03:40:22] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -47.441067-0.001355j
[2025-08-27 03:40:27] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -47.434535+0.000752j
[2025-08-27 03:40:33] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -47.436472-0.000954j
[2025-08-27 03:40:38] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -47.436041-0.000660j
[2025-08-27 03:40:44] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -47.434772-0.003845j
[2025-08-27 03:40:49] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -47.440397-0.000269j
[2025-08-27 03:40:55] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -47.441105-0.002795j
[2025-08-27 03:41:01] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -47.430058+0.001894j
[2025-08-27 03:41:06] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -47.437874+0.000494j
[2025-08-27 03:41:12] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -47.434302-0.001369j
[2025-08-27 03:41:12] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 03:41:17] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -47.434554+0.002120j
[2025-08-27 03:41:23] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -47.435105-0.002499j
[2025-08-27 03:41:28] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -47.429107-0.000583j
[2025-08-27 03:41:34] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -47.426564+0.000559j
[2025-08-27 03:41:39] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -47.434506-0.001643j
[2025-08-27 03:41:45] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -47.428301+0.000426j
[2025-08-27 03:41:51] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -47.446419+0.001373j
[2025-08-27 03:41:56] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -47.434609-0.001316j
[2025-08-27 03:42:02] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -47.442538+0.000843j
[2025-08-27 03:42:07] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -47.436153+0.003422j
[2025-08-27 03:42:13] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -47.443043+0.005965j
[2025-08-27 03:42:18] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -47.437188-0.002685j
[2025-08-27 03:42:24] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -47.436833-0.000383j
[2025-08-27 03:42:29] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -47.419254-0.000269j
[2025-08-27 03:42:35] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -47.431533-0.000532j
[2025-08-27 03:42:40] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -47.434797+0.000195j
[2025-08-27 03:42:46] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -47.431514-0.000688j
[2025-08-27 03:42:52] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -47.435295+0.000073j
[2025-08-27 03:42:57] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -47.435392+0.002494j
[2025-08-27 03:43:03] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -47.436989+0.001263j
[2025-08-27 03:43:08] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -47.442047-0.001169j
[2025-08-27 03:43:14] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -47.427310-0.001197j
[2025-08-27 03:43:19] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -47.437948+0.001616j
[2025-08-27 03:43:25] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -47.428938-0.001095j
[2025-08-27 03:43:30] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -47.439260-0.000376j
[2025-08-27 03:43:36] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -47.425606-0.001276j
[2025-08-27 03:43:41] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -47.424355-0.001495j
[2025-08-27 03:43:47] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -47.424711+0.001737j
[2025-08-27 03:43:53] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -47.416637-0.006083j
[2025-08-27 03:43:58] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -47.438513-0.001535j
[2025-08-27 03:44:04] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -47.436189-0.001689j
[2025-08-27 03:44:09] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -47.446267+0.000637j
[2025-08-27 03:44:15] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -47.453754-0.003238j
[2025-08-27 03:44:20] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -47.439968+0.000521j
[2025-08-27 03:44:26] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -47.417916-0.002507j
[2025-08-27 03:44:31] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -47.429672+0.001339j
[2025-08-27 03:44:37] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -47.434733+0.001426j
[2025-08-27 03:44:42] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -47.441204+0.002735j
[2025-08-27 03:44:48] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -47.442037-0.001229j
[2025-08-27 03:44:54] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -47.431014+0.003943j
[2025-08-27 03:44:59] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -47.427083+0.001365j
[2025-08-27 03:45:05] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -47.439692+0.002733j
[2025-08-27 03:45:10] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -47.445293-0.001386j
[2025-08-27 03:45:16] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -47.432061+0.001117j
[2025-08-27 03:45:21] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -47.432479-0.000512j
[2025-08-27 03:45:27] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -47.428891-0.001247j
[2025-08-27 03:45:32] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -47.435275-0.000930j
[2025-08-27 03:45:38] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -47.425922+0.002133j
[2025-08-27 03:45:43] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -47.425954-0.001350j
[2025-08-27 03:45:49] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -47.445911+0.004170j
[2025-08-27 03:45:49] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 03:45:49] ✅ Training completed | Restarts: 1
[2025-08-27 03:45:49] ============================================================
[2025-08-27 03:45:49] Training completed | Runtime: 2544.6s
[2025-08-27 03:45:51] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 03:45:51] ============================================================
