[2025-08-27 14:12:55] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.77/training/checkpoints/final_GCNN.pkl
[2025-08-27 14:12:55]   - 迭代次数: final
[2025-08-27 14:12:56]   - 能量: -42.941273-0.001785j ± 0.008207
[2025-08-27 14:12:56]   - 时间戳: 2025-08-27T03:03:01.942940+08:00
[2025-08-27 14:13:06] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 14:13:06] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 14:13:06] ==================================================
[2025-08-27 14:13:06] GCNN for Shastry-Sutherland Model
[2025-08-27 14:13:06] ==================================================
[2025-08-27 14:13:06] System parameters:
[2025-08-27 14:13:06]   - System size: L=5, N=100
[2025-08-27 14:13:06]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-08-27 14:13:06] --------------------------------------------------
[2025-08-27 14:13:06] Model parameters:
[2025-08-27 14:13:06]   - Number of layers = 4
[2025-08-27 14:13:06]   - Number of features = 4
[2025-08-27 14:13:06]   - Total parameters = 19628
[2025-08-27 14:13:06] --------------------------------------------------
[2025-08-27 14:13:06] Training parameters:
[2025-08-27 14:13:06]   - Learning rate: 0.015
[2025-08-27 14:13:06]   - Total iterations: 450
[2025-08-27 14:13:06]   - Annealing cycles: 2
[2025-08-27 14:13:06]   - Initial period: 150
[2025-08-27 14:13:06]   - Period multiplier: 2.0
[2025-08-27 14:13:06]   - Temperature range: 0.0-1.0
[2025-08-27 14:13:06]   - Samples: 4096
[2025-08-27 14:13:06]   - Discarded samples: 0
[2025-08-27 14:13:06]   - Chunk size: 2048
[2025-08-27 14:13:06]   - Diagonal shift: 0.2
[2025-08-27 14:13:06]   - Gradient clipping: 1.0
[2025-08-27 14:13:06]   - Checkpoint enabled: interval=50
[2025-08-27 14:13:06]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.76/training/checkpoints
[2025-08-27 14:13:06] --------------------------------------------------
[2025-08-27 14:13:06] Device status:
[2025-08-27 14:13:06]   - Devices model: NVIDIA H200 NVL
[2025-08-27 14:13:06]   - Number of devices: 1
[2025-08-27 14:13:06]   - Sharding: True
[2025-08-27 14:13:06] ============================================================
[2025-08-27 14:13:42] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -42.265797-0.002855j
[2025-08-27 14:14:04] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -42.319285+0.003977j
[2025-08-27 14:14:10] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -42.297903+0.000672j
[2025-08-27 14:14:15] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -42.314636-0.000895j
[2025-08-27 14:14:21] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -42.317310-0.003837j
[2025-08-27 14:14:26] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -42.314088+0.000446j
[2025-08-27 14:14:32] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -42.316185-0.001700j
[2025-08-27 14:14:38] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -42.312445-0.006034j
[2025-08-27 14:14:43] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -42.312321+0.001112j
[2025-08-27 14:14:49] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -42.318457+0.004531j
[2025-08-27 14:14:54] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -42.313308+0.002048j
[2025-08-27 14:15:00] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -42.321625-0.002422j
[2025-08-27 14:15:06] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -42.321775-0.000503j
[2025-08-27 14:15:11] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -42.328230-0.005729j
[2025-08-27 14:15:17] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -42.311213+0.001685j
[2025-08-27 14:15:22] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -42.320909+0.002893j
[2025-08-27 14:15:28] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -42.302071+0.000237j
[2025-08-27 14:15:34] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -42.324084-0.004508j
[2025-08-27 14:15:39] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -42.297878+0.002368j
[2025-08-27 14:15:45] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -42.312259+0.007477j
[2025-08-27 14:15:50] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -42.326106+0.000659j
[2025-08-27 14:15:56] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -42.322915-0.004601j
[2025-08-27 14:16:02] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -42.316448+0.000640j
[2025-08-27 14:16:07] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -42.327408-0.001653j
[2025-08-27 14:16:13] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -42.303397-0.001480j
[2025-08-27 14:16:19] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -42.299604-0.002999j
[2025-08-27 14:16:24] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -42.305648+0.002368j
[2025-08-27 14:16:30] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -42.300225-0.001734j
[2025-08-27 14:16:35] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -42.307347+0.000564j
[2025-08-27 14:16:41] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -42.307213-0.001523j
[2025-08-27 14:16:47] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -42.316403-0.004477j
[2025-08-27 14:16:52] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -42.302238+0.001833j
[2025-08-27 14:16:58] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -42.304946+0.001032j
[2025-08-27 14:17:03] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -42.318826+0.003181j
[2025-08-27 14:17:09] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -42.324345+0.003933j
[2025-08-27 14:17:15] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -42.304152+0.001692j
[2025-08-27 14:17:20] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -42.307787+0.001828j
[2025-08-27 14:17:26] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -42.312614+0.004900j
[2025-08-27 14:17:31] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -42.301742-0.002052j
[2025-08-27 14:17:37] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -42.340155+0.001586j
[2025-08-27 14:17:43] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -42.296453+0.002789j
[2025-08-27 14:17:48] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -42.317577+0.001357j
[2025-08-27 14:17:54] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -42.305239+0.003358j
[2025-08-27 14:18:00] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -42.322117+0.000744j
[2025-08-27 14:18:05] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -42.321054+0.000219j
[2025-08-27 14:18:11] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -42.312832-0.001920j
[2025-08-27 14:18:16] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -42.317298-0.002893j
[2025-08-27 14:18:22] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -42.311617-0.001481j
[2025-08-27 14:18:28] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -42.320127+0.004391j
[2025-08-27 14:18:33] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -42.327855+0.000477j
[2025-08-27 14:18:33] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 14:18:39] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -42.308915-0.000604j
[2025-08-27 14:18:45] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -42.315596+0.000792j
[2025-08-27 14:18:50] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -42.307201-0.002230j
[2025-08-27 14:18:56] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -42.331509-0.003323j
[2025-08-27 14:19:01] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -42.322467-0.002772j
[2025-08-27 14:19:07] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -42.303383+0.000113j
[2025-08-27 14:19:13] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -42.303510-0.006943j
[2025-08-27 14:19:18] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -42.311020-0.002791j
[2025-08-27 14:19:24] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -42.323063+0.001873j
[2025-08-27 14:19:29] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -42.315831-0.003982j
[2025-08-27 14:19:35] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -42.327638-0.002411j
[2025-08-27 14:19:41] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -42.322888+0.007836j
[2025-08-27 14:19:46] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -42.310059+0.002108j
[2025-08-27 14:19:52] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -42.304905+0.001670j
[2025-08-27 14:19:57] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -42.308348-0.002243j
[2025-08-27 14:20:03] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -42.301076+0.002473j
[2025-08-27 14:20:09] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -42.308674-0.000262j
[2025-08-27 14:20:14] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -42.307397-0.000347j
[2025-08-27 14:20:20] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -42.318014+0.002628j
[2025-08-27 14:20:26] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -42.307555+0.002344j
[2025-08-27 14:20:31] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -42.304577-0.001014j
[2025-08-27 14:20:37] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -42.308232+0.000806j
[2025-08-27 14:20:42] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -42.322637-0.002665j
[2025-08-27 14:20:48] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -42.316310+0.001377j
[2025-08-27 14:20:54] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -42.299559-0.002856j
[2025-08-27 14:20:59] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -42.315643+0.001452j
[2025-08-27 14:21:05] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -42.306735+0.000195j
[2025-08-27 14:21:10] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -42.295449+0.000898j
[2025-08-27 14:21:16] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -42.308123-0.000915j
[2025-08-27 14:21:22] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -42.325780-0.000106j
[2025-08-27 14:21:27] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -42.324917-0.003765j
[2025-08-27 14:21:33] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -42.299542+0.003243j
[2025-08-27 14:21:38] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -42.310346-0.001174j
[2025-08-27 14:21:44] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -42.335771-0.002881j
[2025-08-27 14:21:50] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -42.302834+0.007131j
[2025-08-27 14:21:55] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -42.303780+0.000482j
[2025-08-27 14:22:01] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -42.324970+0.000033j
[2025-08-27 14:22:06] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -42.300728-0.003305j
[2025-08-27 14:22:12] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -42.309900-0.002970j
[2025-08-27 14:22:18] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -42.317897+0.002868j
[2025-08-27 14:22:23] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -42.307104+0.001668j
[2025-08-27 14:22:29] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -42.308485+0.000797j
[2025-08-27 14:22:35] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -42.307821+0.002386j
[2025-08-27 14:22:40] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -42.319427-0.001225j
[2025-08-27 14:22:46] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -42.296620+0.000212j
[2025-08-27 14:22:51] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -42.320916-0.004315j
[2025-08-27 14:22:57] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -42.312087-0.002439j
[2025-08-27 14:23:03] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -42.321118-0.001584j
[2025-08-27 14:23:08] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -42.308212-0.004151j
[2025-08-27 14:23:14] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -42.315073-0.001300j
[2025-08-27 14:23:14] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 14:23:20] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -42.340340+0.000222j
[2025-08-27 14:23:25] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -42.300599-0.002261j
[2025-08-27 14:23:31] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -42.316456+0.001452j
[2025-08-27 14:23:36] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -42.311068+0.005701j
[2025-08-27 14:23:42] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -42.310398+0.001392j
[2025-08-27 14:23:48] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -42.321388+0.001623j
[2025-08-27 14:23:53] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -42.311406+0.001208j
[2025-08-27 14:23:59] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -42.319025-0.002675j
[2025-08-27 14:24:04] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -42.303900+0.000221j
[2025-08-27 14:24:10] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -42.325281-0.000838j
[2025-08-27 14:24:16] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -42.319171+0.000997j
[2025-08-27 14:24:21] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -42.318271-0.001371j
[2025-08-27 14:24:27] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -42.294463+0.000474j
[2025-08-27 14:24:32] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -42.317774+0.004985j
[2025-08-27 14:24:38] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -42.316028-0.002917j
[2025-08-27 14:24:44] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -42.295256+0.000598j
[2025-08-27 14:24:49] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -42.302368+0.002671j
[2025-08-27 14:24:55] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -42.318987-0.002299j
[2025-08-27 14:25:00] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -42.320113-0.003704j
[2025-08-27 14:25:06] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -42.302916+0.001482j
[2025-08-27 14:25:12] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -42.299946+0.001990j
[2025-08-27 14:25:17] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -42.307471+0.001646j
[2025-08-27 14:25:23] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -42.320645+0.000701j
[2025-08-27 14:25:29] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -42.319548+0.000730j
[2025-08-27 14:25:34] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -42.306175+0.000521j
[2025-08-27 14:25:40] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -42.324971-0.009607j
[2025-08-27 14:25:45] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -42.324657+0.004216j
[2025-08-27 14:25:51] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -42.306704-0.000998j
[2025-08-27 14:25:57] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -42.318576+0.007374j
[2025-08-27 14:26:02] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -42.304674-0.001852j
[2025-08-27 14:26:08] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -42.297099+0.007327j
[2025-08-27 14:26:14] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -42.288208-0.006482j
[2025-08-27 14:26:19] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -42.293135-0.000261j
[2025-08-27 14:26:25] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -42.316742+0.000216j
[2025-08-27 14:26:30] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -42.306419-0.002776j
[2025-08-27 14:26:36] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -42.310406+0.000990j
[2025-08-27 14:26:42] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -42.314599-0.002032j
[2025-08-27 14:26:47] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -42.312644-0.001649j
[2025-08-27 14:26:53] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -42.310495-0.000932j
[2025-08-27 14:26:58] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -42.318131-0.003075j
[2025-08-27 14:27:04] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -42.325197-0.003739j
[2025-08-27 14:27:10] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -42.307902+0.002063j
[2025-08-27 14:27:15] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -42.316373-0.004320j
[2025-08-27 14:27:21] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -42.312853-0.003254j
[2025-08-27 14:27:26] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -42.307450-0.001833j
[2025-08-27 14:27:32] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -42.311361-0.003649j
[2025-08-27 14:27:38] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -42.303482+0.000387j
[2025-08-27 14:27:43] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -42.304421-0.002225j
[2025-08-27 14:27:49] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -42.330326-0.000641j
[2025-08-27 14:27:54] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -42.295099+0.001387j
[2025-08-27 14:27:54] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 14:27:54] RESTART #1 | Period: 300
[2025-08-27 14:28:00] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -42.301824+0.000588j
[2025-08-27 14:28:06] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -42.320354-0.008671j
[2025-08-27 14:28:11] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -42.319564+0.002326j
[2025-08-27 14:28:17] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -42.300536+0.000419j
[2025-08-27 14:28:22] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -42.296853+0.001836j
[2025-08-27 14:28:28] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -42.327800+0.001422j
[2025-08-27 14:28:34] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -42.322903+0.000109j
[2025-08-27 14:28:39] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -42.305365-0.000905j
[2025-08-27 14:28:45] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -42.313695+0.003092j
[2025-08-27 14:28:50] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -42.309309-0.002719j
[2025-08-27 14:28:56] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -42.307835-0.005229j
[2025-08-27 14:29:02] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -42.302167+0.001465j
[2025-08-27 14:29:07] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -42.313646+0.001628j
[2025-08-27 14:29:13] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -42.313594-0.001201j
[2025-08-27 14:29:19] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -42.315120+0.006454j
[2025-08-27 14:29:24] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -42.304789+0.003911j
[2025-08-27 14:29:30] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -42.316411-0.003084j
[2025-08-27 14:29:35] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -42.300547+0.001528j
[2025-08-27 14:29:41] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -42.321160+0.000891j
[2025-08-27 14:29:47] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -42.306960-0.000026j
[2025-08-27 14:29:52] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -42.309246+0.003459j
[2025-08-27 14:29:58] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -42.299955-0.004010j
[2025-08-27 14:30:03] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -42.317615-0.000101j
[2025-08-27 14:30:09] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -42.313265+0.005815j
[2025-08-27 14:30:15] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -42.318307+0.001035j
[2025-08-27 14:30:20] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -42.322734+0.005463j
[2025-08-27 14:30:26] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -42.312834-0.001328j
[2025-08-27 14:30:31] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -42.311605-0.002160j
[2025-08-27 14:30:37] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -42.316732-0.004839j
[2025-08-27 14:30:43] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -42.309834+0.000566j
[2025-08-27 14:30:48] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -42.307850-0.004408j
[2025-08-27 14:30:54] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -42.306713-0.004181j
[2025-08-27 14:31:00] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -42.306452-0.000471j
[2025-08-27 14:31:05] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -42.303221+0.001244j
[2025-08-27 14:31:11] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -42.304926-0.002340j
[2025-08-27 14:31:16] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -42.321073-0.000841j
[2025-08-27 14:31:22] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -42.301112-0.003518j
[2025-08-27 14:31:28] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -42.315669+0.006729j
[2025-08-27 14:31:33] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -42.297715+0.000483j
[2025-08-27 14:31:39] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -42.314304-0.000381j
[2025-08-27 14:31:44] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -42.310436-0.000733j
[2025-08-27 14:31:50] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -42.301461-0.000383j
[2025-08-27 14:31:56] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -42.302636+0.000437j
[2025-08-27 14:32:01] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -42.321514-0.002586j
[2025-08-27 14:32:07] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -42.309334+0.002373j
[2025-08-27 14:32:12] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -42.320886-0.001470j
[2025-08-27 14:32:18] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -42.327264-0.004166j
[2025-08-27 14:32:24] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -42.326621-0.002228j
[2025-08-27 14:32:29] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -42.320552+0.001893j
[2025-08-27 14:32:35] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -42.320864-0.004597j
[2025-08-27 14:32:35] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 14:32:41] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -42.315351-0.001149j
[2025-08-27 14:32:46] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -42.324868-0.002263j
[2025-08-27 14:32:52] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -42.308901+0.003621j
[2025-08-27 14:32:57] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -42.301465+0.004152j
[2025-08-27 14:33:03] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -42.313485-0.004599j
[2025-08-27 14:33:09] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -42.311246-0.002773j
[2025-08-27 14:33:14] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -42.316689-0.002352j
[2025-08-27 14:33:20] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -42.320453+0.000264j
[2025-08-27 14:33:25] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -42.321231-0.001704j
[2025-08-27 14:33:31] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -42.310791+0.006109j
[2025-08-27 14:33:37] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -42.308135-0.003740j
[2025-08-27 14:33:42] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -42.317342-0.001970j
[2025-08-27 14:33:48] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -42.320438+0.003244j
[2025-08-27 14:33:53] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -42.321434-0.003007j
[2025-08-27 14:33:59] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -42.314573+0.001269j
[2025-08-27 14:34:05] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -42.306516-0.000510j
[2025-08-27 14:34:10] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -42.306095+0.001239j
[2025-08-27 14:34:16] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -42.316881+0.001585j
[2025-08-27 14:34:21] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -42.321803+0.001569j
[2025-08-27 14:34:27] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -42.333611+0.000834j
[2025-08-27 14:34:33] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -42.306415+0.003988j
[2025-08-27 14:34:38] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -42.305376+0.003210j
[2025-08-27 14:34:44] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -42.309519+0.000712j
[2025-08-27 14:34:50] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -42.320094+0.003643j
[2025-08-27 14:34:55] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -42.306454-0.000662j
[2025-08-27 14:35:01] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -42.309481-0.014475j
[2025-08-27 14:35:06] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -42.307276-0.000957j
[2025-08-27 14:35:12] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -42.304082+0.002821j
[2025-08-27 14:35:18] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -42.315271+0.001603j
[2025-08-27 14:35:23] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -42.300894+0.003187j
[2025-08-27 14:35:29] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -42.316510+0.000357j
[2025-08-27 14:35:34] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -42.283480+0.003839j
[2025-08-27 14:35:40] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -42.305662-0.001359j
[2025-08-27 14:35:46] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -42.322192-0.001107j
[2025-08-27 14:35:51] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -42.315906-0.002793j
[2025-08-27 14:35:57] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -42.312607-0.003016j
[2025-08-27 14:36:02] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -42.314758+0.002157j
[2025-08-27 14:36:08] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -42.301546+0.000187j
[2025-08-27 14:36:14] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -42.323679-0.000346j
[2025-08-27 14:36:19] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -42.313354-0.000041j
[2025-08-27 14:36:25] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -42.311076+0.000090j
[2025-08-27 14:36:30] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -42.304004+0.000363j
[2025-08-27 14:36:36] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -42.324192+0.004270j
[2025-08-27 14:36:42] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -42.325825-0.004527j
[2025-08-27 14:36:48] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -42.323599+0.005898j
[2025-08-27 14:36:54] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -42.313108-0.001800j
[2025-08-27 14:36:59] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -42.328657-0.001559j
[2025-08-27 14:37:05] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -42.310970+0.003970j
[2025-08-27 14:37:11] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -42.332734-0.003169j
[2025-08-27 14:37:16] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -42.328954-0.003600j
[2025-08-27 14:37:16] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 14:37:22] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -42.313007-0.000164j
[2025-08-27 14:37:27] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -42.300848+0.005948j
[2025-08-27 14:37:33] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -42.312398+0.000044j
[2025-08-27 14:37:39] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -42.287797-0.005158j
[2025-08-27 14:37:44] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -42.318904-0.003468j
[2025-08-27 14:37:50] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -42.319517+0.001616j
[2025-08-27 14:37:55] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -42.319379-0.002536j
[2025-08-27 14:38:01] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -42.306612-0.002701j
[2025-08-27 14:38:07] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -42.310742+0.003261j
[2025-08-27 14:38:12] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -42.309185+0.005052j
[2025-08-27 14:38:18] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -42.307996-0.003619j
[2025-08-27 14:38:23] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -42.313778-0.001813j
[2025-08-27 14:38:29] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -42.321802+0.006883j
[2025-08-27 14:38:35] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -42.318114+0.001145j
[2025-08-27 14:38:41] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -42.328794+0.006004j
[2025-08-27 14:38:46] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -42.320499+0.000975j
[2025-08-27 14:38:52] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -42.326455+0.001879j
[2025-08-27 14:38:57] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -42.320051-0.003444j
[2025-08-27 14:39:03] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -42.308484+0.002181j
[2025-08-27 14:39:09] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -42.325647-0.002959j
[2025-08-27 14:39:14] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -42.322486-0.000701j
[2025-08-27 14:39:20] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -42.300908+0.000621j
[2025-08-27 14:39:25] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -42.308055+0.008954j
[2025-08-27 14:39:31] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -42.308111-0.002002j
[2025-08-27 14:39:37] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -42.311168-0.005000j
[2025-08-27 14:39:42] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -42.318029+0.000406j
[2025-08-27 14:39:48] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -42.303735+0.005366j
[2025-08-27 14:39:53] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -42.309275-0.004741j
[2025-08-27 14:39:59] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -42.328206-0.002178j
[2025-08-27 14:40:05] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -42.308615+0.002925j
[2025-08-27 14:40:10] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -42.317923-0.001031j
[2025-08-27 14:40:16] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -42.314422-0.003899j
[2025-08-27 14:40:22] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -42.329017+0.004096j
[2025-08-27 14:40:27] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -42.304948-0.002983j
[2025-08-27 14:40:33] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -42.315227-0.001439j
[2025-08-27 14:40:38] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -42.316490-0.000518j
[2025-08-27 14:40:44] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -42.324075-0.009503j
[2025-08-27 14:40:50] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -42.310765-0.000330j
[2025-08-27 14:40:55] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -42.311935-0.001899j
[2025-08-27 14:41:01] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -42.312535-0.000731j
[2025-08-27 14:41:06] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -42.313709+0.000340j
[2025-08-27 14:41:12] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -42.310114+0.000633j
[2025-08-27 14:41:18] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -42.325032-0.000332j
[2025-08-27 14:41:23] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -42.303514+0.002089j
[2025-08-27 14:41:29] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -42.317496-0.001880j
[2025-08-27 14:41:34] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -42.312192-0.001261j
[2025-08-27 14:41:40] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -42.329069-0.001496j
[2025-08-27 14:41:46] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -42.319547-0.003739j
[2025-08-27 14:41:51] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -42.323802-0.000307j
[2025-08-27 14:41:57] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -42.312497+0.000071j
[2025-08-27 14:41:57] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 14:42:02] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -42.331623-0.000914j
[2025-08-27 14:42:08] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -42.309742+0.001665j
[2025-08-27 14:42:14] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -42.307085+0.001752j
[2025-08-27 14:42:19] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -42.322383-0.000846j
[2025-08-27 14:42:25] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -42.330253+0.000832j
[2025-08-27 14:42:30] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -42.319231-0.002230j
[2025-08-27 14:42:36] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -42.309250+0.001438j
[2025-08-27 14:42:42] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -42.322441-0.000939j
[2025-08-27 14:42:47] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -42.330667-0.000849j
[2025-08-27 14:42:53] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -42.306796+0.005531j
[2025-08-27 14:42:59] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -42.313102+0.000101j
[2025-08-27 14:43:04] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -42.307806-0.003404j
[2025-08-27 14:43:10] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -42.319730-0.000016j
[2025-08-27 14:43:15] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -42.312078-0.004020j
[2025-08-27 14:43:21] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -42.305835-0.001740j
[2025-08-27 14:43:27] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -42.300342-0.002917j
[2025-08-27 14:43:32] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -42.316082+0.000495j
[2025-08-27 14:43:38] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -42.318376+0.001443j
[2025-08-27 14:43:44] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -42.327378+0.001383j
[2025-08-27 14:43:49] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -42.320820+0.001685j
[2025-08-27 14:43:55] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -42.323515-0.000225j
[2025-08-27 14:44:00] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -42.313521-0.007871j
[2025-08-27 14:44:06] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -42.303849+0.002123j
[2025-08-27 14:44:12] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -42.311764-0.005425j
[2025-08-27 14:44:17] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -42.311565+0.002578j
[2025-08-27 14:44:23] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -42.317386-0.006482j
[2025-08-27 14:44:28] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -42.315373-0.000101j
[2025-08-27 14:44:34] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -42.306794-0.002167j
[2025-08-27 14:44:40] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -42.324963-0.003399j
[2025-08-27 14:44:45] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -42.317876+0.002866j
[2025-08-27 14:44:51] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -42.319313-0.000147j
[2025-08-27 14:44:56] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -42.302729-0.002055j
[2025-08-27 14:45:02] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -42.313822+0.000042j
[2025-08-27 14:45:08] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -42.294304+0.003176j
[2025-08-27 14:45:13] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -42.307680-0.000093j
[2025-08-27 14:45:19] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -42.318145-0.001130j
[2025-08-27 14:45:25] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -42.308561+0.004214j
[2025-08-27 14:45:30] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -42.323847-0.000740j
[2025-08-27 14:45:36] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -42.308409+0.006950j
[2025-08-27 14:45:41] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -42.325540-0.009189j
[2025-08-27 14:45:47] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -42.316660+0.002414j
[2025-08-27 14:45:53] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -42.318857-0.000857j
[2025-08-27 14:45:58] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -42.301695-0.003870j
[2025-08-27 14:46:04] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -42.332494+0.003962j
[2025-08-27 14:46:09] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -42.320975+0.001193j
[2025-08-27 14:46:15] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -42.320013-0.000434j
[2025-08-27 14:46:21] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -42.321405+0.002728j
[2025-08-27 14:46:26] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -42.295188+0.000838j
[2025-08-27 14:46:32] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -42.316261+0.001896j
[2025-08-27 14:46:37] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -42.328401-0.006199j
[2025-08-27 14:46:38] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 14:46:43] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -42.308075-0.003621j
[2025-08-27 14:46:49] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -42.314801-0.000631j
[2025-08-27 14:46:54] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -42.328545+0.001471j
[2025-08-27 14:47:00] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -42.327522+0.000181j
[2025-08-27 14:47:06] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -42.322894+0.001355j
[2025-08-27 14:47:11] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -42.314632-0.005266j
[2025-08-27 14:47:17] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -42.298792+0.001270j
[2025-08-27 14:47:22] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -42.299202-0.003302j
[2025-08-27 14:47:28] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -42.307736+0.001118j
[2025-08-27 14:47:34] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -42.309512-0.002882j
[2025-08-27 14:47:39] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -42.314297-0.004028j
[2025-08-27 14:47:45] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -42.320001+0.000531j
[2025-08-27 14:47:51] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -42.288292+0.001305j
[2025-08-27 14:47:56] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -42.321795+0.000975j
[2025-08-27 14:48:02] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -42.308089+0.002761j
[2025-08-27 14:48:07] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -42.314629+0.000514j
[2025-08-27 14:48:13] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -42.294160+0.003268j
[2025-08-27 14:48:19] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -42.309923-0.001273j
[2025-08-27 14:48:24] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -42.317129-0.001257j
[2025-08-27 14:48:30] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -42.313710-0.001577j
[2025-08-27 14:48:35] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -42.301883-0.003639j
[2025-08-27 14:48:41] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -42.307427+0.002671j
[2025-08-27 14:48:47] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -42.319855-0.003754j
[2025-08-27 14:48:52] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -42.320494+0.000627j
[2025-08-27 14:48:58] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -42.308909-0.000715j
[2025-08-27 14:49:03] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -42.311344+0.007416j
[2025-08-27 14:49:09] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -42.328524-0.004714j
[2025-08-27 14:49:15] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -42.311987-0.002115j
[2025-08-27 14:49:20] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -42.306306-0.001392j
[2025-08-27 14:49:26] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -42.313574+0.000754j
[2025-08-27 14:49:31] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -42.303823+0.001852j
[2025-08-27 14:49:37] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -42.311850+0.001427j
[2025-08-27 14:49:43] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -42.315551+0.003161j
[2025-08-27 14:49:48] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -42.305580-0.002617j
[2025-08-27 14:49:54] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -42.307402-0.001574j
[2025-08-27 14:50:00] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -42.311297+0.002294j
[2025-08-27 14:50:05] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -42.308965+0.005035j
[2025-08-27 14:50:11] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -42.317844+0.006994j
[2025-08-27 14:50:16] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -42.319946+0.001682j
[2025-08-27 14:50:22] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -42.327542-0.001984j
[2025-08-27 14:50:28] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -42.319461+0.005511j
[2025-08-27 14:50:33] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -42.313149+0.003309j
[2025-08-27 14:50:39] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -42.287893-0.011653j
[2025-08-27 14:50:44] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -42.309571-0.001953j
[2025-08-27 14:50:50] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -42.319594+0.004178j
[2025-08-27 14:50:56] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -42.316142+0.001032j
[2025-08-27 14:51:01] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -42.310440-0.001759j
[2025-08-27 14:51:07] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -42.305420+0.009420j
[2025-08-27 14:51:12] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -42.312942-0.003090j
[2025-08-27 14:51:18] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -42.310241-0.001896j
[2025-08-27 14:51:18] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 14:51:24] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -42.303712+0.002593j
[2025-08-27 14:51:29] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -42.320737+0.009199j
[2025-08-27 14:51:35] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -42.313314-0.002149j
[2025-08-27 14:51:41] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -42.323843+0.001708j
[2025-08-27 14:51:46] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -42.316245+0.000779j
[2025-08-27 14:51:52] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -42.311047-0.006759j
[2025-08-27 14:51:57] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -42.327527-0.000451j
[2025-08-27 14:52:03] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -42.323024-0.002099j
[2025-08-27 14:52:09] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -42.323229+0.005443j
[2025-08-27 14:52:14] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -42.321129-0.002614j
[2025-08-27 14:52:20] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -42.323130+0.000492j
[2025-08-27 14:52:26] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -42.325886+0.002338j
[2025-08-27 14:52:31] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -42.324466-0.000030j
[2025-08-27 14:52:37] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -42.308836+0.001357j
[2025-08-27 14:52:42] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -42.319750+0.006875j
[2025-08-27 14:52:48] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -42.307976-0.002501j
[2025-08-27 14:52:54] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -42.302202-0.000329j
[2025-08-27 14:52:59] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -42.320993-0.007616j
[2025-08-27 14:53:05] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -42.326393+0.000735j
[2025-08-27 14:53:10] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -42.330994-0.000718j
[2025-08-27 14:53:16] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -42.319151-0.004560j
[2025-08-27 14:53:22] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -42.316929+0.001599j
[2025-08-27 14:53:27] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -42.323405-0.004252j
[2025-08-27 14:53:33] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -42.313975+0.001418j
[2025-08-27 14:53:39] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -42.321961-0.000156j
[2025-08-27 14:53:44] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -42.311420-0.004595j
[2025-08-27 14:53:50] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -42.302501+0.000018j
[2025-08-27 14:53:55] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -42.295440-0.000412j
[2025-08-27 14:54:01] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -42.315617-0.000247j
[2025-08-27 14:54:07] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -42.295973+0.002277j
[2025-08-27 14:54:12] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -42.307279+0.001614j
[2025-08-27 14:54:18] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -42.337249-0.002206j
[2025-08-27 14:54:23] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -42.307816-0.000803j
[2025-08-27 14:54:29] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -42.316970-0.001125j
[2025-08-27 14:54:35] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -42.319454-0.000898j
[2025-08-27 14:54:40] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -42.327408-0.000042j
[2025-08-27 14:54:46] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -42.303795-0.002774j
[2025-08-27 14:54:51] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -42.320017+0.005024j
[2025-08-27 14:54:57] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -42.323721-0.001895j
[2025-08-27 14:55:03] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -42.318033+0.000548j
[2025-08-27 14:55:08] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -42.329159+0.004168j
[2025-08-27 14:55:14] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -42.323930-0.001521j
[2025-08-27 14:55:19] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -42.325577-0.004037j
[2025-08-27 14:55:25] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -42.327384-0.002219j
[2025-08-27 14:55:31] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -42.306155-0.000127j
[2025-08-27 14:55:36] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -42.326394-0.000895j
[2025-08-27 14:55:43] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -42.313262-0.006125j
[2025-08-27 14:55:48] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -42.313585+0.000148j
[2025-08-27 14:55:54] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -42.297322-0.003573j
[2025-08-27 14:55:59] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -42.301142-0.003088j
[2025-08-27 14:55:59] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 14:55:59] ✅ Training completed | Restarts: 1
[2025-08-27 14:55:59] ============================================================
[2025-08-27 14:55:59] Training completed | Runtime: 2573.7s
[2025-08-27 14:56:01] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 14:56:01] ============================================================
