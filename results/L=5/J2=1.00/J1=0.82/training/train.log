[2025-08-26 23:54:46] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.81/training/checkpoints/final_GCNN.pkl
[2025-08-26 23:54:46]   - 迭代次数: final
[2025-08-26 23:54:46]   - 能量: -45.483602+0.001030j ± 0.008089
[2025-08-26 23:54:46]   - 时间戳: 2025-08-26T23:54:28.395411+08:00
[2025-08-26 23:54:56] ✓ 变分状态参数已从checkpoint恢复
[2025-08-26 23:54:56] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-26 23:54:56] ==================================================
[2025-08-26 23:54:56] GCNN for Shastry-Sutherland Model
[2025-08-26 23:54:56] ==================================================
[2025-08-26 23:54:56] System parameters:
[2025-08-26 23:54:56]   - System size: L=5, N=100
[2025-08-26 23:54:56]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-08-26 23:54:56] --------------------------------------------------
[2025-08-26 23:54:56] Model parameters:
[2025-08-26 23:54:56]   - Number of layers = 4
[2025-08-26 23:54:56]   - Number of features = 4
[2025-08-26 23:54:56]   - Total parameters = 19628
[2025-08-26 23:54:56] --------------------------------------------------
[2025-08-26 23:54:56] Training parameters:
[2025-08-26 23:54:56]   - Learning rate: 0.015
[2025-08-26 23:54:56]   - Total iterations: 450
[2025-08-26 23:54:56]   - Annealing cycles: 2
[2025-08-26 23:54:56]   - Initial period: 150
[2025-08-26 23:54:56]   - Period multiplier: 2.0
[2025-08-26 23:54:56]   - Temperature range: 0.0-1.0
[2025-08-26 23:54:56]   - Samples: 4096
[2025-08-26 23:54:56]   - Discarded samples: 0
[2025-08-26 23:54:56]   - Chunk size: 2048
[2025-08-26 23:54:56]   - Diagonal shift: 0.2
[2025-08-26 23:54:56]   - Gradient clipping: 1.0
[2025-08-26 23:54:56]   - Checkpoint enabled: interval=50
[2025-08-26 23:54:56]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.82/training/checkpoints
[2025-08-26 23:54:56] --------------------------------------------------
[2025-08-26 23:54:56] Device status:
[2025-08-26 23:54:56]   - Devices model: NVIDIA H200 NVL
[2025-08-26 23:54:56]   - Number of devices: 1
[2025-08-26 23:54:56]   - Sharding: True
[2025-08-26 23:54:56] ============================================================
[2025-08-26 23:55:37] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -46.133216+0.008162j
[2025-08-26 23:56:06] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -46.129849-0.002202j
[2025-08-26 23:56:18] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -46.129412+0.005382j
[2025-08-26 23:56:31] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -46.142506-0.002633j
[2025-08-26 23:56:43] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -46.134131+0.007575j
[2025-08-26 23:56:55] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -46.115850-0.004206j
[2025-08-26 23:57:08] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -46.140818+0.006458j
[2025-08-26 23:57:20] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -46.126362-0.003662j
[2025-08-26 23:57:33] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -46.125370+0.002314j
[2025-08-26 23:57:45] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -46.130396-0.001703j
[2025-08-26 23:57:58] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -46.134870-0.000395j
[2025-08-26 23:58:10] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -46.133837-0.003079j
[2025-08-26 23:58:23] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -46.142403-0.002830j
[2025-08-26 23:58:35] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -46.130398+0.005352j
[2025-08-26 23:58:48] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -46.135400+0.002634j
[2025-08-26 23:59:00] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -46.146561+0.002647j
[2025-08-26 23:59:13] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -46.133364+0.000944j
[2025-08-26 23:59:25] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -46.134748-0.000459j
[2025-08-26 23:59:38] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -46.146250+0.003615j
[2025-08-26 23:59:50] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -46.139527+0.003002j
[2025-08-27 00:00:03] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -46.135779-0.000624j
[2025-08-27 00:00:15] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -46.137776-0.000625j
[2025-08-27 00:00:28] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -46.142148-0.001904j
[2025-08-27 00:00:40] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -46.129894+0.004923j
[2025-08-27 00:00:53] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -46.145329+0.003044j
[2025-08-27 00:01:05] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -46.148721-0.001707j
[2025-08-27 00:01:18] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -46.140376-0.000306j
[2025-08-27 00:01:30] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -46.126171-0.001375j
[2025-08-27 00:01:43] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -46.127492+0.002633j
[2025-08-27 00:01:55] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -46.144525-0.000062j
[2025-08-27 00:02:07] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -46.120797+0.001825j
[2025-08-27 00:02:20] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -46.148806+0.000343j
[2025-08-27 00:02:32] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -46.140758-0.003144j
[2025-08-27 00:02:45] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -46.125234-0.001423j
[2025-08-27 00:02:57] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -46.114275-0.000988j
[2025-08-27 00:03:10] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -46.129281-0.002645j
[2025-08-27 00:03:22] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -46.145649+0.002421j
[2025-08-27 00:03:35] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -46.138390+0.002756j
[2025-08-27 00:03:47] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -46.120296-0.000471j
[2025-08-27 00:04:00] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -46.124181+0.001737j
[2025-08-27 00:04:12] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -46.143549-0.001728j
[2025-08-27 00:04:25] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -46.143375-0.000191j
[2025-08-27 00:04:37] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -46.128336+0.003831j
[2025-08-27 00:04:50] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -46.130624-0.001910j
[2025-08-27 00:05:02] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -46.140061-0.000230j
[2025-08-27 00:05:15] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -46.141268+0.003826j
[2025-08-27 00:05:27] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -46.128104-0.001387j
[2025-08-27 00:05:39] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -46.121449+0.000366j
[2025-08-27 00:05:52] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -46.143947-0.004631j
[2025-08-27 00:06:04] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -46.138447-0.000663j
[2025-08-27 00:06:04] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 00:06:17] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -46.136536-0.001547j
[2025-08-27 00:06:29] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -46.147626-0.002273j
[2025-08-27 00:06:42] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -46.144386+0.000522j
[2025-08-27 00:06:54] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -46.142010+0.001531j
[2025-08-27 00:07:07] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -46.125385-0.000050j
[2025-08-27 00:07:19] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -46.143554-0.002827j
[2025-08-27 00:07:32] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -46.126292-0.002934j
[2025-08-27 00:07:44] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -46.126667+0.000914j
[2025-08-27 00:07:57] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -46.147061+0.000975j
[2025-08-27 00:08:09] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -46.137937-0.002763j
[2025-08-27 00:08:21] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -46.122362-0.003428j
[2025-08-27 00:08:34] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -46.133629+0.000106j
[2025-08-27 00:08:46] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -46.119859+0.000381j
[2025-08-27 00:08:59] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -46.128647-0.004873j
[2025-08-27 00:09:11] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -46.129499+0.003175j
[2025-08-27 00:09:24] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -46.137941+0.004388j
[2025-08-27 00:09:36] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -46.151476-0.000368j
[2025-08-27 00:09:49] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -46.125981-0.000448j
[2025-08-27 00:10:01] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -46.132191-0.001998j
[2025-08-27 00:10:14] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -46.120135-0.003340j
[2025-08-27 00:10:26] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -46.142172+0.000468j
[2025-08-27 00:10:39] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -46.142235+0.003850j
[2025-08-27 00:10:51] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -46.123870+0.003233j
[2025-08-27 00:11:04] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -46.148426-0.000639j
[2025-08-27 00:11:16] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -46.144120-0.000754j
[2025-08-27 00:11:28] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -46.131263-0.001897j
[2025-08-27 00:11:41] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -46.143439-0.003604j
[2025-08-27 00:11:53] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -46.133477-0.004457j
[2025-08-27 00:12:06] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -46.135217-0.002434j
[2025-08-27 00:12:18] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -46.119941-0.001383j
[2025-08-27 00:12:31] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -46.147411+0.004250j
[2025-08-27 00:12:43] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -46.149972+0.002006j
[2025-08-27 00:12:56] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -46.131260+0.003660j
[2025-08-27 00:13:08] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -46.133187+0.000410j
[2025-08-27 00:13:21] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -46.134626+0.001774j
[2025-08-27 00:13:33] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -46.135557+0.000918j
[2025-08-27 00:13:46] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -46.133909-0.001108j
[2025-08-27 00:13:58] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -46.138551-0.000446j
[2025-08-27 00:14:11] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -46.133135-0.001287j
[2025-08-27 00:14:23] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -46.117893+0.004054j
[2025-08-27 00:14:36] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -46.143444+0.007207j
[2025-08-27 00:14:48] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -46.134386-0.001708j
[2025-08-27 00:15:01] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -46.145041-0.000387j
[2025-08-27 00:15:13] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -46.126034+0.001456j
[2025-08-27 00:15:26] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -46.131382-0.004765j
[2025-08-27 00:15:38] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -46.124062+0.000862j
[2025-08-27 00:15:51] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -46.143289-0.001502j
[2025-08-27 00:16:03] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -46.131801-0.001111j
[2025-08-27 00:16:16] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -46.134700+0.000542j
[2025-08-27 00:16:28] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -46.122702-0.003091j
[2025-08-27 00:16:28] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 00:16:40] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -46.121427+0.003094j
[2025-08-27 00:16:53] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -46.140181-0.000908j
[2025-08-27 00:17:05] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -46.129896-0.002272j
[2025-08-27 00:17:18] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -46.135121-0.000689j
[2025-08-27 00:17:30] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -46.126254-0.001614j
[2025-08-27 00:17:43] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -46.144509+0.001767j
[2025-08-27 00:17:55] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -46.135536-0.004857j
[2025-08-27 00:18:08] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -46.147506-0.001970j
[2025-08-27 00:18:20] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -46.142887+0.000123j
[2025-08-27 00:18:33] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -46.153753+0.001011j
[2025-08-27 00:18:45] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -46.140111+0.000077j
[2025-08-27 00:18:58] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -46.142150+0.004701j
[2025-08-27 00:19:10] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -46.132000+0.001175j
[2025-08-27 00:19:23] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -46.142070+0.006144j
[2025-08-27 00:19:35] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -46.130052-0.000585j
[2025-08-27 00:19:48] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -46.125883-0.001851j
[2025-08-27 00:20:00] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -46.131773-0.000038j
[2025-08-27 00:20:13] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -46.134892+0.003340j
[2025-08-27 00:20:25] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -46.143020+0.002048j
[2025-08-27 00:20:38] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -46.155222+0.003087j
[2025-08-27 00:20:50] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -46.137474-0.000482j
[2025-08-27 00:21:02] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -46.130359+0.003735j
[2025-08-27 00:21:15] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -46.134534-0.000638j
[2025-08-27 00:21:27] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -46.143007+0.002908j
[2025-08-27 00:21:40] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -46.128622+0.001346j
[2025-08-27 00:21:52] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -46.133958-0.001450j
[2025-08-27 00:22:05] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -46.132714-0.000822j
[2025-08-27 00:22:17] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -46.143692-0.005453j
[2025-08-27 00:22:30] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -46.127223+0.007966j
[2025-08-27 00:22:42] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -46.138771-0.004524j
[2025-08-27 00:22:55] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -46.145197+0.004378j
[2025-08-27 00:23:07] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -46.137202+0.001974j
[2025-08-27 00:23:20] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -46.145575-0.005570j
[2025-08-27 00:23:32] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -46.134401+0.003960j
[2025-08-27 00:23:44] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -46.139247-0.001134j
[2025-08-27 00:23:57] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -46.127906-0.002854j
[2025-08-27 00:24:09] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -46.143343-0.000617j
[2025-08-27 00:24:22] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -46.131860+0.002148j
[2025-08-27 00:24:34] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -46.135192-0.001062j
[2025-08-27 00:24:47] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -46.144809-0.004548j
[2025-08-27 00:24:59] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -46.138884-0.004449j
[2025-08-27 00:25:12] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -46.154586-0.002450j
[2025-08-27 00:25:24] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -46.154595+0.000461j
[2025-08-27 00:25:37] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -46.131148-0.000999j
[2025-08-27 00:25:49] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -46.146064+0.002207j
[2025-08-27 00:26:02] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -46.140599+0.001167j
[2025-08-27 00:26:14] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -46.136977-0.007020j
[2025-08-27 00:26:27] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -46.139468+0.002176j
[2025-08-27 00:26:39] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -46.128381-0.001838j
[2025-08-27 00:26:51] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -46.139708+0.001291j
[2025-08-27 00:26:52] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 00:26:52] RESTART #1 | Period: 300
[2025-08-27 00:27:04] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -46.134238+0.000882j
[2025-08-27 00:27:17] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -46.139543-0.001485j
[2025-08-27 00:27:29] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -46.131533-0.003359j
[2025-08-27 00:27:41] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -46.144856+0.004023j
[2025-08-27 00:27:54] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -46.125098-0.000935j
[2025-08-27 00:28:06] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -46.141786+0.001527j
[2025-08-27 00:28:19] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -46.125214+0.002928j
[2025-08-27 00:28:31] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -46.148168-0.003064j
[2025-08-27 00:28:44] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -46.143949-0.000559j
[2025-08-27 00:28:56] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -46.139053+0.001660j
[2025-08-27 00:29:09] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -46.135997-0.001600j
[2025-08-27 00:29:21] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -46.143681-0.002195j
[2025-08-27 00:29:34] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -46.146743+0.000493j
[2025-08-27 00:29:46] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -46.137084-0.001010j
[2025-08-27 00:29:59] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -46.154806+0.001031j
[2025-08-27 00:30:11] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -46.137678-0.003168j
[2025-08-27 00:30:24] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -46.137741-0.009065j
[2025-08-27 00:30:36] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -46.136086+0.001505j
[2025-08-27 00:30:48] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -46.124842-0.000100j
[2025-08-27 00:31:01] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -46.146976+0.001807j
[2025-08-27 00:31:14] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -46.126989+0.002520j
[2025-08-27 00:31:26] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -46.129305-0.001120j
[2025-08-27 00:31:39] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -46.140155+0.001215j
[2025-08-27 00:31:51] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -46.132063+0.002257j
[2025-08-27 00:32:04] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -46.146398+0.004584j
[2025-08-27 00:32:16] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -46.168692-0.002179j
[2025-08-27 00:32:29] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -46.131584+0.001152j
[2025-08-27 00:32:41] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -46.141984-0.000909j
[2025-08-27 00:32:54] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -46.140748-0.003479j
[2025-08-27 00:33:06] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -46.125110+0.000886j
[2025-08-27 00:33:19] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -46.125413-0.000665j
[2025-08-27 00:33:31] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -46.139339-0.001570j
[2025-08-27 00:33:43] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -46.132188-0.006634j
[2025-08-27 00:33:56] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -46.131111-0.002999j
[2025-08-27 00:34:08] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -46.114447-0.003113j
[2025-08-27 00:34:21] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -46.125165-0.001680j
[2025-08-27 00:34:33] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -46.134848-0.000010j
[2025-08-27 00:34:46] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -46.137365+0.002842j
[2025-08-27 00:34:58] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -46.143000+0.000313j
[2025-08-27 00:35:11] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -46.107604-0.002921j
[2025-08-27 00:35:23] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -46.135941+0.002233j
[2025-08-27 00:35:36] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -46.141068+0.001544j
[2025-08-27 00:35:48] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -46.132186+0.000679j
[2025-08-27 00:36:01] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -46.137373-0.001257j
[2025-08-27 00:36:13] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -46.141364-0.003609j
[2025-08-27 00:36:26] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -46.137027+0.000635j
[2025-08-27 00:36:38] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -46.136760+0.003729j
[2025-08-27 00:36:51] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -46.133923-0.000808j
[2025-08-27 00:37:03] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -46.137778-0.002306j
[2025-08-27 00:37:16] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -46.146084+0.001197j
[2025-08-27 00:37:16] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 00:37:28] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -46.154157-0.000859j
[2025-08-27 00:37:41] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -46.147018+0.000936j
[2025-08-27 00:37:53] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -46.137264+0.003354j
[2025-08-27 00:38:06] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -46.135912+0.010602j
[2025-08-27 00:38:18] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -46.116346+0.000700j
[2025-08-27 00:38:30] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -46.140174-0.005977j
[2025-08-27 00:38:43] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -46.144164-0.003385j
[2025-08-27 00:38:55] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -46.142939-0.002539j
[2025-08-27 00:39:08] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -46.146411+0.001458j
[2025-08-27 00:39:20] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -46.120612+0.000983j
[2025-08-27 00:39:33] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -46.145574+0.003896j
[2025-08-27 00:39:45] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -46.135296-0.003357j
[2025-08-27 00:39:58] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -46.136926+0.001310j
[2025-08-27 00:40:10] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -46.142257-0.001562j
[2025-08-27 00:40:23] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -46.140386-0.000159j
[2025-08-27 00:40:35] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -46.144977-0.002211j
[2025-08-27 00:40:48] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -46.136596+0.001358j
[2025-08-27 00:41:00] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -46.135200+0.000784j
[2025-08-27 00:41:13] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -46.137557-0.002094j
[2025-08-27 00:41:25] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -46.139877+0.006810j
[2025-08-27 00:41:38] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -46.137973+0.001492j
[2025-08-27 00:41:50] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -46.133345+0.002683j
[2025-08-27 00:42:03] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -46.144312+0.002307j
[2025-08-27 00:42:15] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -46.139387-0.002325j
[2025-08-27 00:42:27] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -46.140189-0.001184j
[2025-08-27 00:42:40] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -46.139718+0.001997j
[2025-08-27 00:42:52] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -46.131591-0.001924j
[2025-08-27 00:43:05] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -46.128325+0.000196j
[2025-08-27 00:43:17] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -46.139080-0.002184j
[2025-08-27 00:43:30] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -46.136068-0.002706j
[2025-08-27 00:43:42] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -46.134344-0.006669j
[2025-08-27 00:43:55] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -46.143004+0.001049j
[2025-08-27 00:44:07] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -46.144402+0.003312j
[2025-08-27 00:44:20] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -46.128601-0.003624j
[2025-08-27 00:44:32] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -46.135815-0.000040j
[2025-08-27 00:44:45] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -46.143187-0.002322j
[2025-08-27 00:44:57] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -46.147042+0.004603j
[2025-08-27 00:45:10] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -46.129373+0.000807j
[2025-08-27 00:45:22] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -46.127032+0.002047j
[2025-08-27 00:45:34] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -46.139540-0.002204j
[2025-08-27 00:45:47] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -46.131832-0.002043j
[2025-08-27 00:45:59] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -46.139385+0.001526j
[2025-08-27 00:46:12] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -46.141615-0.000891j
[2025-08-27 00:46:24] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -46.133697+0.001634j
[2025-08-27 00:46:37] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -46.139971+0.001369j
[2025-08-27 00:46:49] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -46.124187+0.002058j
[2025-08-27 00:47:02] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -46.147918+0.002370j
[2025-08-27 00:47:14] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -46.149557-0.002129j
[2025-08-27 00:47:27] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -46.138688+0.002046j
[2025-08-27 00:47:39] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -46.140700-0.001152j
[2025-08-27 00:47:39] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 00:47:52] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -46.146685-0.005890j
[2025-08-27 00:48:04] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -46.136322+0.004146j
[2025-08-27 00:48:17] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -46.130707-0.000348j
[2025-08-27 00:48:29] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -46.140207-0.003696j
[2025-08-27 00:48:42] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -46.151245+0.001353j
[2025-08-27 00:48:54] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -46.137089+0.005289j
[2025-08-27 00:49:07] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -46.146303+0.003448j
[2025-08-27 00:49:19] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -46.128162+0.003639j
[2025-08-27 00:49:32] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -46.148649-0.000542j
[2025-08-27 00:49:44] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -46.130275-0.001472j
[2025-08-27 00:49:56] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -46.119395+0.000797j
[2025-08-27 00:50:09] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -46.134769-0.002264j
[2025-08-27 00:50:21] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -46.141871-0.001156j
[2025-08-27 00:50:34] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -46.142862+0.002725j
[2025-08-27 00:50:46] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -46.138200-0.001063j
[2025-08-27 00:50:59] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -46.140339+0.005085j
[2025-08-27 00:51:11] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -46.127066-0.001224j
[2025-08-27 00:51:24] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -46.132696-0.003071j
[2025-08-27 00:51:36] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -46.152177+0.000819j
[2025-08-27 00:51:49] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -46.137297+0.001622j
[2025-08-27 00:52:01] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -46.144652+0.000306j
[2025-08-27 00:52:14] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -46.138481-0.003586j
[2025-08-27 00:52:26] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -46.143738+0.001409j
[2025-08-27 00:52:39] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -46.143609-0.000150j
[2025-08-27 00:52:51] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -46.122199-0.002065j
[2025-08-27 00:53:04] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -46.135695+0.002406j
[2025-08-27 00:53:16] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -46.129869-0.003152j
[2025-08-27 00:53:29] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -46.137562-0.001605j
[2025-08-27 00:53:41] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -46.135862-0.002488j
[2025-08-27 00:53:54] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -46.138818-0.000790j
[2025-08-27 00:54:06] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -46.148135+0.000130j
[2025-08-27 00:54:19] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -46.129599-0.000053j
[2025-08-27 00:54:31] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -46.130048-0.000585j
[2025-08-27 00:54:43] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -46.122946-0.000464j
[2025-08-27 00:54:56] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -46.126788+0.003759j
[2025-08-27 00:55:08] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -46.128884-0.002488j
[2025-08-27 00:55:21] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -46.138336-0.004253j
[2025-08-27 00:55:33] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -46.139120+0.000028j
[2025-08-27 00:55:46] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -46.141414+0.003065j
[2025-08-27 00:55:58] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -46.144839-0.002221j
[2025-08-27 00:56:11] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -46.145087-0.000278j
[2025-08-27 00:56:23] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -46.138787+0.004020j
[2025-08-27 00:56:36] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -46.137962+0.002431j
[2025-08-27 00:56:48] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -46.131439-0.001186j
[2025-08-27 00:57:01] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -46.131833+0.003111j
[2025-08-27 00:57:13] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -46.131921+0.004080j
[2025-08-27 00:57:26] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -46.134978-0.002288j
[2025-08-27 00:57:38] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -46.131184+0.000344j
[2025-08-27 00:57:50] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -46.143166+0.001489j
[2025-08-27 00:58:03] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -46.131394-0.000535j
[2025-08-27 00:58:03] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 00:58:15] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -46.148160+0.002666j
[2025-08-27 00:58:28] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -46.151427+0.009020j
[2025-08-27 00:58:40] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -46.130944+0.000524j
[2025-08-27 00:58:53] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -46.137711+0.001754j
[2025-08-27 00:59:05] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -46.146521+0.001473j
[2025-08-27 00:59:18] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -46.134079+0.003274j
[2025-08-27 00:59:30] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -46.139254-0.003615j
[2025-08-27 00:59:43] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -46.147671-0.003296j
[2025-08-27 00:59:55] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -46.124502-0.000738j
[2025-08-27 01:00:08] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -46.148423-0.004879j
[2025-08-27 01:00:20] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -46.137654+0.005225j
[2025-08-27 01:00:33] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -46.150222+0.000076j
[2025-08-27 01:00:45] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -46.154409+0.000576j
[2025-08-27 01:00:58] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -46.128079+0.006206j
[2025-08-27 01:01:10] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -46.142519+0.000664j
[2025-08-27 01:01:23] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -46.146805-0.004048j
[2025-08-27 01:01:35] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -46.128535-0.002719j
[2025-08-27 01:01:47] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -46.142427-0.004696j
[2025-08-27 01:02:00] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -46.138733-0.002878j
[2025-08-27 01:02:12] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -46.126553+0.006951j
[2025-08-27 01:02:25] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -46.153463-0.005874j
[2025-08-27 01:02:38] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -46.134805+0.001136j
[2025-08-27 01:02:50] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -46.135513+0.002848j
[2025-08-27 01:03:02] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -46.142694+0.004397j
[2025-08-27 01:03:15] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -46.124759+0.001633j
[2025-08-27 01:03:27] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -46.143806+0.000999j
[2025-08-27 01:03:40] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -46.135040+0.001355j
[2025-08-27 01:03:52] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -46.140157+0.002330j
[2025-08-27 01:04:05] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -46.131916+0.001767j
[2025-08-27 01:04:17] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -46.140379+0.000674j
[2025-08-27 01:04:30] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -46.141798+0.001193j
[2025-08-27 01:04:42] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -46.139082-0.003469j
[2025-08-27 01:04:55] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -46.144010+0.004485j
[2025-08-27 01:05:07] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -46.129593-0.001232j
[2025-08-27 01:05:20] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -46.134770-0.001282j
[2025-08-27 01:05:32] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -46.135512+0.001897j
[2025-08-27 01:05:45] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -46.133418-0.004306j
[2025-08-27 01:05:57] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -46.130049+0.001385j
[2025-08-27 01:06:10] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -46.156589+0.000673j
[2025-08-27 01:06:22] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -46.137659+0.000009j
[2025-08-27 01:06:35] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -46.129206+0.003664j
[2025-08-27 01:06:47] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -46.138034-0.000642j
[2025-08-27 01:07:00] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -46.144873-0.004564j
[2025-08-27 01:07:12] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -46.133035+0.007001j
[2025-08-27 01:07:25] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -46.150865+0.003644j
[2025-08-27 01:07:37] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -46.142886-0.000134j
[2025-08-27 01:07:49] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -46.138116+0.001264j
[2025-08-27 01:08:02] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -46.129414-0.002629j
[2025-08-27 01:08:14] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -46.136057-0.001265j
[2025-08-27 01:08:27] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -46.129187-0.001850j
[2025-08-27 01:08:27] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 01:08:39] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -46.140339-0.004236j
[2025-08-27 01:08:52] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -46.144932+0.002111j
[2025-08-27 01:09:04] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -46.141105+0.001138j
[2025-08-27 01:09:17] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -46.151152+0.002351j
[2025-08-27 01:09:29] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -46.143508+0.001238j
[2025-08-27 01:09:42] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -46.135853+0.001202j
[2025-08-27 01:09:54] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -46.143936-0.001323j
[2025-08-27 01:10:07] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -46.143471-0.001085j
[2025-08-27 01:10:19] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -46.147872-0.002883j
[2025-08-27 01:10:32] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -46.136075-0.001350j
[2025-08-27 01:10:44] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -46.121012-0.003046j
[2025-08-27 01:10:57] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -46.140276+0.002294j
[2025-08-27 01:11:09] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -46.134374+0.003753j
[2025-08-27 01:11:22] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -46.126711+0.001141j
[2025-08-27 01:11:34] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -46.131217+0.001998j
[2025-08-27 01:11:47] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -46.133046+0.004002j
[2025-08-27 01:11:59] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -46.137029+0.008916j
[2025-08-27 01:12:12] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -46.146407-0.002962j
[2025-08-27 01:12:24] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -46.137891+0.001485j
[2025-08-27 01:12:36] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -46.140942-0.000951j
[2025-08-27 01:12:49] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -46.144634-0.009653j
[2025-08-27 01:13:01] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -46.151193+0.000588j
[2025-08-27 01:13:14] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -46.143023-0.000828j
[2025-08-27 01:13:26] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -46.131644-0.001686j
[2025-08-27 01:13:39] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -46.137623+0.001664j
[2025-08-27 01:13:51] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -46.133319-0.001258j
[2025-08-27 01:14:04] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -46.136995-0.001369j
[2025-08-27 01:14:16] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -46.133176+0.002935j
[2025-08-27 01:14:29] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -46.146241+0.002285j
[2025-08-27 01:14:41] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -46.141444-0.003761j
[2025-08-27 01:14:54] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -46.134973-0.000166j
[2025-08-27 01:15:06] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -46.150380+0.001189j
[2025-08-27 01:15:19] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -46.139895-0.000362j
[2025-08-27 01:15:31] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -46.138835-0.000442j
[2025-08-27 01:15:44] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -46.142853-0.001373j
[2025-08-27 01:15:56] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -46.147890-0.002218j
[2025-08-27 01:16:08] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -46.134632+0.001616j
[2025-08-27 01:16:21] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -46.145485+0.004037j
[2025-08-27 01:16:33] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -46.135231-0.001378j
[2025-08-27 01:16:46] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -46.147195+0.003084j
[2025-08-27 01:16:58] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -46.140012+0.000932j
[2025-08-27 01:17:11] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -46.137010+0.004592j
[2025-08-27 01:17:23] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -46.134201+0.000044j
[2025-08-27 01:17:36] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -46.133629+0.000858j
[2025-08-27 01:17:48] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -46.136598-0.000456j
[2025-08-27 01:18:01] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -46.129763-0.001316j
[2025-08-27 01:18:13] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -46.133428-0.000440j
[2025-08-27 01:18:26] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -46.141756-0.002225j
[2025-08-27 01:18:38] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -46.145511-0.004697j
[2025-08-27 01:18:51] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -46.134056-0.000634j
[2025-08-27 01:18:51] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 01:19:03] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -46.134651-0.000492j
[2025-08-27 01:19:16] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -46.130954+0.003896j
[2025-08-27 01:19:28] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -46.134041-0.004889j
[2025-08-27 01:19:41] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -46.127541-0.004202j
[2025-08-27 01:19:53] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -46.144559-0.001282j
[2025-08-27 01:20:06] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -46.152130-0.003040j
[2025-08-27 01:20:18] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -46.144795-0.004693j
[2025-08-27 01:20:31] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -46.140701-0.002132j
[2025-08-27 01:20:43] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -46.143226+0.003122j
[2025-08-27 01:20:56] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -46.153189-0.003680j
[2025-08-27 01:21:08] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -46.145179-0.002596j
[2025-08-27 01:21:21] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -46.143236-0.000685j
[2025-08-27 01:21:33] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -46.139006-0.002805j
[2025-08-27 01:21:46] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -46.138769+0.000276j
[2025-08-27 01:21:58] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -46.149369+0.004342j
[2025-08-27 01:22:10] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -46.145117-0.000856j
[2025-08-27 01:22:23] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -46.132606-0.003155j
[2025-08-27 01:22:35] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -46.133561-0.001104j
[2025-08-27 01:22:48] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -46.148717-0.002182j
[2025-08-27 01:23:00] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -46.139990-0.000566j
[2025-08-27 01:23:13] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -46.135774-0.001969j
[2025-08-27 01:23:25] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -46.131713-0.000329j
[2025-08-27 01:23:38] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -46.156597+0.000693j
[2025-08-27 01:23:50] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -46.147751+0.004609j
[2025-08-27 01:24:03] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -46.160903+0.005176j
[2025-08-27 01:24:15] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -46.130009+0.003829j
[2025-08-27 01:24:28] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -46.141672-0.000415j
[2025-08-27 01:24:40] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -46.125298-0.004274j
[2025-08-27 01:24:53] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -46.138252+0.000854j
[2025-08-27 01:25:05] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -46.133921+0.000708j
[2025-08-27 01:25:18] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -46.151513-0.001311j
[2025-08-27 01:25:30] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -46.138687-0.008938j
[2025-08-27 01:25:43] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -46.140123+0.001193j
[2025-08-27 01:25:55] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -46.142911+0.000723j
[2025-08-27 01:26:07] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -46.134414+0.004686j
[2025-08-27 01:26:20] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -46.137854+0.001818j
[2025-08-27 01:26:32] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -46.143687+0.002234j
[2025-08-27 01:26:45] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -46.138611-0.001375j
[2025-08-27 01:26:57] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -46.147185-0.001721j
[2025-08-27 01:27:10] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -46.130754-0.000533j
[2025-08-27 01:27:22] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -46.129907+0.005687j
[2025-08-27 01:27:35] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -46.126766-0.008608j
[2025-08-27 01:27:47] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -46.168163-0.000634j
[2025-08-27 01:28:00] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -46.160507+0.000687j
[2025-08-27 01:28:12] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -46.152871+0.002374j
[2025-08-27 01:28:25] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -46.146016-0.002085j
[2025-08-27 01:28:37] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -46.149399-0.001765j
[2025-08-27 01:28:46] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -46.130553+0.004488j
[2025-08-27 01:28:51] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -46.136673+0.000813j
[2025-08-27 01:28:57] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -46.138794-0.001425j
[2025-08-27 01:28:57] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 01:28:57] ✅ Training completed | Restarts: 1
[2025-08-27 01:28:57] ============================================================
[2025-08-27 01:28:57] Training completed | Runtime: 5640.8s
[2025-08-27 01:28:59] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 01:28:59] ============================================================
