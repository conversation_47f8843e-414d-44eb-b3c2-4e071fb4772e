[2025-08-27 19:41:45] 使用checkpoint文件: results/L=5/J2=1.00/J1=0.82/training/checkpoints/checkpoint_iter_000200.pkl
[2025-08-27 19:41:57] ✓ 从checkpoint加载参数: 200
[2025-08-27 19:41:57]   - 能量: -46.146084+0.001197j ± 0.009271
[2025-08-27 19:41:57] ================================================================================
[2025-08-27 19:41:57] 加载量子态: L=5, J2=1.00, J1=0.82, checkpoint=checkpoint_iter_000200
[2025-08-27 19:41:57] 设置样本数为: 1048576
[2025-08-27 19:41:57] 开始生成共享样本集...
[2025-08-27 19:44:53] 样本生成完成,耗时: 176.072 秒
[2025-08-27 19:44:53] ================================================================================
[2025-08-27 19:44:53] 开始计算自旋结构因子...
[2025-08-27 19:44:53] 初始化操作符缓存...
[2025-08-27 19:44:53] 预构建所有自旋相关操作符...
[2025-08-27 19:44:53] 开始计算自旋相关函数...
[2025-08-27 19:45:02] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.361s
[2025-08-27 19:45:13] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 10.922s
[2025-08-27 19:45:20] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.085s
[2025-08-27 19:45:26] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.089s
[2025-08-27 19:45:32] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.101s
[2025-08-27 19:45:38] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.086s
[2025-08-27 19:45:44] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.093s
[2025-08-27 19:45:50] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.121s
[2025-08-27 19:45:56] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.115s
[2025-08-27 19:46:02] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.097s
[2025-08-27 19:46:08] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.103s
[2025-08-27 19:46:14] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.103s
[2025-08-27 19:46:21] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.104s
[2025-08-27 19:46:27] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.088s
[2025-08-27 19:46:33] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.090s
[2025-08-27 19:46:39] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.093s
[2025-08-27 19:46:45] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.121s
[2025-08-27 19:46:51] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.089s
[2025-08-27 19:46:57] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.104s
[2025-08-27 19:47:03] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.120s
[2025-08-27 19:47:09] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.095s
[2025-08-27 19:47:16] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.103s
[2025-08-27 19:47:22] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.117s
[2025-08-27 19:47:28] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.121s
[2025-08-27 19:47:34] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.097s
[2025-08-27 19:47:40] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.117s
[2025-08-27 19:47:46] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.092s
[2025-08-27 19:47:52] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.119s
[2025-08-27 19:47:58] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.092s
[2025-08-27 19:48:04] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.121s
[2025-08-27 19:48:11] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.094s
[2025-08-27 19:48:17] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.092s
[2025-08-27 19:48:23] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.088s
[2025-08-27 19:48:29] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.121s
[2025-08-27 19:48:35] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.093s
[2025-08-27 19:48:41] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.089s
[2025-08-27 19:48:47] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.095s
[2025-08-27 19:48:53] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.093s
[2025-08-27 19:48:59] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.102s
[2025-08-27 19:49:05] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.102s
[2025-08-27 19:49:12] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.091s
[2025-08-27 19:49:18] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.102s
[2025-08-27 19:49:24] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.087s
[2025-08-27 19:49:30] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.118s
[2025-08-27 19:49:36] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.093s
[2025-08-27 19:49:42] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.104s
[2025-08-27 19:49:48] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.085s
[2025-08-27 19:49:54] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.088s
[2025-08-27 19:50:00] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.091s
[2025-08-27 19:50:06] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.104s
[2025-08-27 19:50:13] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.088s
[2025-08-27 19:50:19] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.092s
[2025-08-27 19:50:25] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.101s
[2025-08-27 19:50:31] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.087s
[2025-08-27 19:50:37] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.093s
[2025-08-27 19:50:43] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.089s
[2025-08-27 19:50:49] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.102s
[2025-08-27 19:50:55] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.096s
[2025-08-27 19:51:01] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.119s
[2025-08-27 19:51:07] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.090s
[2025-08-27 19:51:14] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.101s
[2025-08-27 19:51:20] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.090s
[2025-08-27 19:51:26] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.118s
[2025-08-27 19:51:32] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.089s
[2025-08-27 19:51:38] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.093s
[2025-08-27 19:51:44] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.088s
[2025-08-27 19:51:50] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.106s
[2025-08-27 19:51:56] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.089s
[2025-08-27 19:52:02] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.087s
[2025-08-27 19:52:09] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.117s
[2025-08-27 19:52:15] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.089s
[2025-08-27 19:52:21] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.119s
[2025-08-27 19:52:27] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.119s
[2025-08-27 19:52:33] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.089s
[2025-08-27 19:52:39] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.119s
[2025-08-27 19:52:45] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.094s
[2025-08-27 19:52:51] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.105s
[2025-08-27 19:52:57] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.087s
[2025-08-27 19:53:03] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.120s
[2025-08-27 19:53:10] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.088s
[2025-08-27 19:53:16] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.087s
[2025-08-27 19:53:22] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.089s
[2025-08-27 19:53:28] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.120s
[2025-08-27 19:53:34] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.092s
[2025-08-27 19:53:40] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.087s
[2025-08-27 19:53:46] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.120s
[2025-08-27 19:53:52] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.092s
[2025-08-27 19:53:58] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.089s
[2025-08-27 19:54:05] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.089s
[2025-08-27 19:54:11] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.124s
[2025-08-27 19:54:17] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.096s
[2025-08-27 19:54:23] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.106s
[2025-08-27 19:54:29] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.119s
[2025-08-27 19:54:35] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.097s
[2025-08-27 19:54:41] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.105s
[2025-08-27 19:54:47] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.099s
[2025-08-27 19:54:53] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.099s
[2025-08-27 19:54:59] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.088s
[2025-08-27 19:55:06] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.118s
[2025-08-27 19:55:12] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.090s
[2025-08-27 19:55:12] 自旋相关函数计算完成,总耗时 618.59 秒
[2025-08-27 19:55:12] 计算傅里叶变换...
[2025-08-27 19:55:13] 自旋结构因子计算完成
[2025-08-27 19:55:14] 自旋相关函数平均误差: 0.000592
