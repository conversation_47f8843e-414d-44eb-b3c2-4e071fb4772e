[2025-08-26 22:19:59] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-08-26 22:19:59]   - 迭代次数: final
[2025-08-26 22:19:59]   - 能量: -44.841680+0.001760j ± 0.005054
[2025-08-26 22:19:59]   - 时间戳: 2025-08-26T10:27:53.974121+08:00
[2025-08-26 22:20:10] ✓ 变分状态参数已从checkpoint恢复
[2025-08-26 22:20:10] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-26 22:20:10] ==================================================
[2025-08-26 22:20:10] GCNN for Shastry-Sutherland Model
[2025-08-26 22:20:10] ==================================================
[2025-08-26 22:20:10] System parameters:
[2025-08-26 22:20:10]   - System size: L=5, N=100
[2025-08-26 22:20:10]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-08-26 22:20:10] --------------------------------------------------
[2025-08-26 22:20:10] Model parameters:
[2025-08-26 22:20:10]   - Number of layers = 4
[2025-08-26 22:20:10]   - Number of features = 4
[2025-08-26 22:20:10]   - Total parameters = 19628
[2025-08-26 22:20:10] --------------------------------------------------
[2025-08-26 22:20:10] Training parameters:
[2025-08-26 22:20:10]   - Learning rate: 0.015
[2025-08-26 22:20:10]   - Total iterations: 450
[2025-08-26 22:20:10]   - Annealing cycles: 2
[2025-08-26 22:20:10]   - Initial period: 150
[2025-08-26 22:20:10]   - Period multiplier: 2.0
[2025-08-26 22:20:10]   - Temperature range: 0.0-1.0
[2025-08-26 22:20:10]   - Samples: 4096
[2025-08-26 22:20:10]   - Discarded samples: 0
[2025-08-26 22:20:10]   - Chunk size: 2048
[2025-08-26 22:20:10]   - Diagonal shift: 0.2
[2025-08-26 22:20:10]   - Gradient clipping: 1.0
[2025-08-26 22:20:10]   - Checkpoint enabled: interval=50
[2025-08-26 22:20:10]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.79/training/checkpoints
[2025-08-26 22:20:10] --------------------------------------------------
[2025-08-26 22:20:10] Device status:
[2025-08-26 22:20:10]   - Devices model: NVIDIA H200 NVL
[2025-08-26 22:20:10]   - Number of devices: 1
[2025-08-26 22:20:10]   - Sharding: True
[2025-08-26 22:20:10] ============================================================
[2025-08-26 22:20:52] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -44.145584+0.003991j
[2025-08-26 22:21:21] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -44.180526+0.002506j
[2025-08-26 22:21:33] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -44.216800-0.003069j
[2025-08-26 22:21:46] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -44.207369-0.000187j
[2025-08-26 22:21:58] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -44.195194+0.000314j
[2025-08-26 22:22:11] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -44.186176-0.000496j
[2025-08-26 22:22:23] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -44.204228+0.000914j
[2025-08-26 22:22:36] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -44.215667-0.001283j
[2025-08-26 22:22:48] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -44.205128+0.001406j
[2025-08-26 22:23:01] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -44.185959+0.003589j
[2025-08-26 22:23:13] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -44.195373+0.001524j
[2025-08-26 22:23:26] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -44.199574+0.000050j
[2025-08-26 22:23:38] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -44.191506+0.001930j
[2025-08-26 22:23:51] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -44.208077+0.001372j
[2025-08-26 22:24:03] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -44.205177-0.003947j
[2025-08-26 22:24:16] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -44.202407+0.003203j
[2025-08-26 22:24:28] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -44.209651+0.002202j
[2025-08-26 22:24:40] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -44.194952-0.000883j
[2025-08-26 22:24:53] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -44.220536-0.000545j
[2025-08-26 22:25:05] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -44.190838+0.003828j
[2025-08-26 22:25:18] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -44.207930+0.002026j
[2025-08-26 22:25:30] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -44.200064+0.001616j
[2025-08-26 22:25:43] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -44.205436+0.008025j
[2025-08-26 22:25:55] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -44.208776+0.012191j
[2025-08-26 22:26:08] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -44.185510-0.001079j
[2025-08-26 22:26:20] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -44.197380+0.000019j
[2025-08-26 22:26:33] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -44.196400-0.000837j
[2025-08-26 22:26:45] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -44.203410-0.001536j
[2025-08-26 22:26:58] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -44.216653-0.003865j
[2025-08-26 22:27:10] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -44.195755+0.001390j
[2025-08-26 22:27:23] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -44.206737+0.000461j
[2025-08-26 22:27:35] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -44.214640+0.005538j
[2025-08-26 22:27:48] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -44.201416+0.004739j
[2025-08-26 22:28:00] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -44.205976-0.004993j
[2025-08-26 22:28:13] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -44.203428+0.006637j
[2025-08-26 22:28:25] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -44.207624-0.006978j
[2025-08-26 22:28:38] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -44.197398+0.007826j
[2025-08-26 22:28:50] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -44.205831+0.004737j
[2025-08-26 22:29:03] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -44.214582+0.000662j
[2025-08-26 22:29:15] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -44.193516-0.001222j
[2025-08-26 22:29:28] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -44.211049-0.004674j
[2025-08-26 22:29:40] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -44.213083+0.002019j
[2025-08-26 22:29:52] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -44.195331+0.000129j
[2025-08-26 22:30:05] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -44.199294-0.002724j
[2025-08-26 22:30:17] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -44.190289+0.001723j
[2025-08-26 22:30:30] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -44.207441-0.001114j
[2025-08-26 22:30:42] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -44.197459-0.003099j
[2025-08-26 22:30:55] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -44.196366-0.005213j
[2025-08-26 22:31:07] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -44.206024-0.002536j
[2025-08-26 22:31:20] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -44.201009+0.000330j
[2025-08-26 22:31:20] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-26 22:31:32] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -44.206041-0.001630j
[2025-08-26 22:31:45] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -44.194226+0.004577j
[2025-08-26 22:31:57] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -44.217207+0.003145j
[2025-08-26 22:32:10] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -44.197023+0.001405j
[2025-08-26 22:32:22] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -44.215525-0.002631j
[2025-08-26 22:32:35] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -44.200881-0.002034j
[2025-08-26 22:32:47] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -44.196494+0.007070j
[2025-08-26 22:33:00] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -44.215838+0.002029j
[2025-08-26 22:33:12] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -44.204882-0.002379j
[2025-08-26 22:33:25] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -44.203398+0.002446j
[2025-08-26 22:33:37] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -44.211493-0.002977j
[2025-08-26 22:33:50] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -44.208118-0.001852j
[2025-08-26 22:34:02] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -44.204120-0.003266j
[2025-08-26 22:34:15] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -44.194502+0.000994j
[2025-08-26 22:34:27] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -44.218016+0.001181j
[2025-08-26 22:34:39] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -44.208385-0.003100j
[2025-08-26 22:34:52] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -44.201664+0.001694j
[2025-08-26 22:35:04] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -44.207125+0.001393j
[2025-08-26 22:35:17] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -44.216328+0.000773j
[2025-08-26 22:35:29] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -44.200573+0.006507j
[2025-08-26 22:35:42] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -44.195839+0.004200j
[2025-08-26 22:35:54] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -44.198826-0.008130j
[2025-08-26 22:36:07] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -44.203985+0.002184j
[2025-08-26 22:36:19] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -44.210158-0.000163j
[2025-08-26 22:36:32] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -44.216709-0.001960j
[2025-08-26 22:36:44] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -44.210722+0.004175j
[2025-08-26 22:36:57] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -44.201913+0.003603j
[2025-08-26 22:37:09] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -44.184989-0.003095j
[2025-08-26 22:37:22] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -44.199870-0.001762j
[2025-08-26 22:37:34] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -44.184578-0.002992j
[2025-08-26 22:37:47] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -44.213448+0.001363j
[2025-08-26 22:37:59] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -44.204907-0.000638j
[2025-08-26 22:38:12] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -44.210001+0.003514j
[2025-08-26 22:38:24] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -44.196693-0.003526j
[2025-08-26 22:38:37] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -44.204716+0.003170j
[2025-08-26 22:38:49] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -44.213899-0.002100j
[2025-08-26 22:39:02] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -44.199540+0.003322j
[2025-08-26 22:39:14] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -44.206656+0.003989j
[2025-08-26 22:39:27] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -44.205619-0.001774j
[2025-08-26 22:39:39] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -44.206136+0.004518j
[2025-08-26 22:39:52] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -44.198529-0.001514j
[2025-08-26 22:40:04] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -44.207513-0.001848j
[2025-08-26 22:40:17] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -44.214434-0.002361j
[2025-08-26 22:40:29] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -44.205569-0.000560j
[2025-08-26 22:40:42] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -44.204441+0.004203j
[2025-08-26 22:40:54] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -44.201635-0.000335j
[2025-08-26 22:41:06] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -44.203737+0.004708j
[2025-08-26 22:41:19] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -44.209587+0.001481j
[2025-08-26 22:41:31] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -44.210481-0.001562j
[2025-08-26 22:41:44] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -44.185252+0.001997j
[2025-08-26 22:41:44] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-26 22:41:56] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -44.183695-0.001639j
[2025-08-26 22:42:09] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -44.207827-0.001390j
[2025-08-26 22:42:21] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -44.206135+0.001101j
[2025-08-26 22:42:34] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -44.198142+0.000678j
[2025-08-26 22:42:46] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -44.210029+0.003070j
[2025-08-26 22:42:59] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -44.204937-0.002216j
[2025-08-26 22:43:11] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -44.199682-0.001464j
[2025-08-26 22:43:24] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -44.216338-0.007223j
[2025-08-26 22:43:36] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -44.203664-0.003233j
[2025-08-26 22:43:48] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -44.193158-0.001842j
[2025-08-26 22:44:01] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -44.234168+0.003516j
[2025-08-26 22:44:13] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -44.211241-0.001232j
[2025-08-26 22:44:26] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -44.199940+0.001111j
[2025-08-26 22:44:38] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -44.207804+0.002741j
[2025-08-26 22:44:51] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -44.209491-0.000833j
[2025-08-26 22:45:03] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -44.206937+0.001589j
[2025-08-26 22:45:16] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -44.197979+0.001072j
[2025-08-26 22:45:28] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -44.210748+0.003286j
[2025-08-26 22:45:41] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -44.205077-0.000720j
[2025-08-26 22:45:53] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -44.212409+0.003322j
[2025-08-26 22:46:06] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -44.203608-0.003263j
[2025-08-26 22:46:18] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -44.198872-0.001366j
[2025-08-26 22:46:30] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -44.199552+0.001996j
[2025-08-26 22:46:43] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -44.209323+0.004519j
[2025-08-26 22:46:55] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -44.196921-0.000862j
[2025-08-26 22:47:08] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -44.218358+0.003947j
[2025-08-26 22:47:20] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -44.207169+0.003830j
[2025-08-26 22:47:33] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -44.187827+0.001053j
[2025-08-26 22:47:45] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -44.204508-0.001179j
[2025-08-26 22:47:58] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -44.213313-0.001201j
[2025-08-26 22:48:10] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -44.191216-0.000240j
[2025-08-26 22:48:23] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -44.207293-0.000276j
[2025-08-26 22:48:35] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -44.207902+0.004474j
[2025-08-26 22:48:48] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -44.211316+0.003659j
[2025-08-26 22:49:00] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -44.210887+0.000439j
[2025-08-26 22:49:13] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -44.208344+0.000338j
[2025-08-26 22:49:25] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -44.193319-0.001853j
[2025-08-26 22:49:38] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -44.206388+0.003763j
[2025-08-26 22:49:50] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -44.189916+0.003841j
[2025-08-26 22:50:03] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -44.199128-0.000193j
[2025-08-26 22:50:15] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -44.204723-0.000358j
[2025-08-26 22:50:28] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -44.211413+0.003235j
[2025-08-26 22:50:40] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -44.201138+0.003988j
[2025-08-26 22:50:53] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -44.195514-0.001487j
[2025-08-26 22:51:05] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -44.205441-0.008116j
[2025-08-26 22:51:18] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -44.179192-0.005260j
[2025-08-26 22:51:30] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -44.209774-0.001886j
[2025-08-26 22:51:42] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -44.208178+0.000045j
[2025-08-26 22:51:55] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -44.218331+0.006237j
[2025-08-26 22:52:07] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -44.198600+0.003224j
[2025-08-26 22:52:07] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-26 22:52:07] RESTART #1 | Period: 300
[2025-08-26 22:52:20] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -44.204498+0.003606j
[2025-08-26 22:52:32] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -44.200557-0.000248j
[2025-08-26 22:52:45] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -44.185276-0.000259j
[2025-08-26 22:52:57] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -44.210693+0.003919j
[2025-08-26 22:53:10] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -44.199419+0.000393j
[2025-08-26 22:53:22] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -44.209014+0.003426j
[2025-08-26 22:53:35] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -44.203709+0.001598j
[2025-08-26 22:53:48] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -44.197140-0.002146j
[2025-08-26 22:54:00] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -44.197499+0.002261j
[2025-08-26 22:54:13] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -44.214875-0.000801j
[2025-08-26 22:54:25] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -44.198888-0.005914j
[2025-08-26 22:54:38] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -44.206604-0.001103j
[2025-08-26 22:54:50] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -44.198831+0.000926j
[2025-08-26 22:55:03] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -44.203256-0.001524j
[2025-08-26 22:55:15] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -44.207068+0.002690j
[2025-08-26 22:55:28] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -44.203849-0.001866j
[2025-08-26 22:55:40] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -44.220125-0.002677j
[2025-08-26 22:55:53] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -44.205106-0.002557j
[2025-08-26 22:56:05] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -44.204707+0.000561j
[2025-08-26 22:56:18] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -44.192421-0.002610j
[2025-08-26 22:56:30] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -44.198333-0.003615j
[2025-08-26 22:56:43] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -44.199058+0.000866j
[2025-08-26 22:56:55] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -44.190350-0.002356j
[2025-08-26 22:57:08] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -44.211236-0.002326j
[2025-08-26 22:57:20] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -44.224726-0.004680j
[2025-08-26 22:57:33] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -44.203257+0.001690j
[2025-08-26 22:57:46] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -44.206583+0.002101j
[2025-08-26 22:57:58] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -44.225757-0.003132j
[2025-08-26 22:58:11] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -44.207643+0.000953j
[2025-08-26 22:58:23] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -44.221190+0.003126j
[2025-08-26 22:58:36] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -44.200744+0.000351j
[2025-08-26 22:58:48] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -44.190907-0.001082j
[2025-08-26 22:59:01] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -44.205157-0.001748j
[2025-08-26 22:59:13] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -44.199565-0.008459j
[2025-08-26 22:59:26] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -44.202749+0.005362j
[2025-08-26 22:59:38] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -44.217598-0.002077j
[2025-08-26 22:59:51] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -44.193300+0.005504j
[2025-08-26 23:00:03] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -44.213539+0.000151j
[2025-08-26 23:00:16] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -44.209874+0.003747j
[2025-08-26 23:00:28] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -44.195805+0.001672j
[2025-08-26 23:00:41] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -44.218141+0.000714j
[2025-08-26 23:00:53] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -44.208569+0.002227j
[2025-08-26 23:01:06] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -44.199256+0.000882j
[2025-08-26 23:01:18] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -44.194074+0.006675j
[2025-08-26 23:01:31] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -44.201984-0.006308j
[2025-08-26 23:01:43] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -44.211920-0.002669j
[2025-08-26 23:01:56] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -44.211392-0.001476j
[2025-08-26 23:02:08] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -44.185230+0.000863j
[2025-08-26 23:02:21] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -44.219305-0.002441j
[2025-08-26 23:02:33] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -44.214704-0.000445j
[2025-08-26 23:02:33] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-26 23:02:46] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -44.215019+0.000274j
[2025-08-26 23:02:58] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -44.211306-0.002776j
[2025-08-26 23:03:11] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -44.208746-0.000478j
[2025-08-26 23:03:23] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -44.208994+0.005907j
[2025-08-26 23:03:36] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -44.212050-0.003274j
[2025-08-26 23:03:48] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -44.202262-0.003215j
[2025-08-26 23:04:01] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -44.201776-0.003252j
[2025-08-26 23:04:13] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -44.204793-0.002470j
[2025-08-26 23:04:26] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -44.219531+0.002971j
[2025-08-26 23:04:39] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -44.202295-0.003273j
[2025-08-26 23:04:51] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -44.209339+0.001038j
[2025-08-26 23:05:04] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -44.203950-0.000285j
[2025-08-26 23:05:16] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -44.214835+0.000958j
[2025-08-26 23:05:29] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -44.209771+0.000491j
[2025-08-26 23:05:41] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -44.199821+0.008445j
[2025-08-26 23:05:54] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -44.202415-0.000358j
[2025-08-26 23:06:06] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -44.206679+0.005780j
[2025-08-26 23:06:19] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -44.212125-0.002998j
[2025-08-26 23:06:31] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -44.215813-0.001994j
[2025-08-26 23:06:44] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -44.205620+0.004733j
[2025-08-26 23:06:56] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -44.230357+0.001186j
[2025-08-26 23:07:09] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -44.202889-0.000250j
[2025-08-26 23:07:21] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -44.208335-0.009338j
[2025-08-26 23:07:34] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -44.210057-0.004576j
[2025-08-26 23:07:46] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -44.197269+0.001296j
[2025-08-26 23:07:59] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -44.205426+0.001174j
[2025-08-26 23:08:11] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -44.199386+0.001384j
[2025-08-26 23:08:24] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -44.201286-0.003089j
[2025-08-26 23:08:36] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -44.200384+0.000756j
[2025-08-26 23:08:49] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -44.210561-0.001073j
[2025-08-26 23:09:01] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -44.201076-0.003482j
[2025-08-26 23:09:14] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -44.194477+0.000247j
[2025-08-26 23:09:26] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -44.223762+0.006318j
[2025-08-26 23:09:39] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -44.201393-0.004098j
[2025-08-26 23:09:51] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -44.198853+0.000471j
[2025-08-26 23:10:04] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -44.204698+0.002555j
[2025-08-26 23:10:16] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -44.197131+0.002061j
[2025-08-26 23:10:29] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -44.187915+0.003032j
[2025-08-26 23:10:42] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -44.223031+0.004082j
[2025-08-26 23:10:54] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -44.194525-0.000371j
[2025-08-26 23:11:07] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -44.196558+0.003248j
[2025-08-26 23:11:19] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -44.208893+0.000408j
[2025-08-26 23:11:32] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -44.195834-0.000978j
[2025-08-26 23:11:44] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -44.202147+0.005488j
[2025-08-26 23:11:57] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -44.197221+0.002429j
[2025-08-26 23:12:09] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -44.191371+0.000277j
[2025-08-26 23:12:22] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -44.223120-0.000605j
[2025-08-26 23:12:34] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -44.217427-0.000669j
[2025-08-26 23:12:46] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -44.203756-0.000491j
[2025-08-26 23:12:59] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -44.220304-0.003750j
[2025-08-26 23:12:59] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-26 23:13:12] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -44.195308-0.002237j
[2025-08-26 23:13:24] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -44.213647-0.002707j
[2025-08-26 23:13:37] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -44.218843+0.000785j
[2025-08-26 23:13:49] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -44.215302+0.008017j
[2025-08-26 23:14:02] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -44.213868+0.003524j
[2025-08-26 23:14:14] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -44.206232-0.003558j
[2025-08-26 23:14:27] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -44.206227+0.003433j
[2025-08-26 23:14:39] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -44.216102-0.005777j
[2025-08-26 23:14:52] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -44.215086+0.000457j
[2025-08-26 23:15:04] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -44.204639-0.001797j
[2025-08-26 23:15:17] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -44.220121-0.001642j
[2025-08-26 23:15:29] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -44.223780+0.003876j
[2025-08-26 23:15:42] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -44.225371+0.002335j
[2025-08-26 23:15:54] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -44.199305-0.002439j
[2025-08-26 23:16:07] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -44.215280+0.003361j
[2025-08-26 23:16:19] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -44.204425-0.000154j
[2025-08-26 23:16:32] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -44.213877-0.008171j
[2025-08-26 23:16:44] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -44.204897+0.000856j
[2025-08-26 23:16:57] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -44.201100-0.003267j
[2025-08-26 23:17:09] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -44.208557+0.001780j
[2025-08-26 23:17:22] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -44.200066-0.000959j
[2025-08-26 23:17:34] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -44.201253-0.003444j
[2025-08-26 23:17:47] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -44.193188-0.008252j
[2025-08-26 23:17:59] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -44.216190-0.000856j
[2025-08-26 23:18:12] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -44.197505-0.002734j
[2025-08-26 23:18:24] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -44.211961+0.004208j
[2025-08-26 23:18:37] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -44.189461+0.000065j
[2025-08-26 23:18:50] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -44.203767-0.006611j
[2025-08-26 23:19:02] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -44.198624-0.001443j
[2025-08-26 23:19:15] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -44.203389-0.000308j
[2025-08-26 23:19:27] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -44.208925+0.007397j
[2025-08-26 23:19:40] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -44.183567+0.001643j
[2025-08-26 23:19:52] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -44.197689-0.001677j
[2025-08-26 23:20:05] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -44.201179-0.002616j
[2025-08-26 23:20:17] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -44.198416+0.003409j
[2025-08-26 23:20:30] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -44.198031-0.004089j
[2025-08-26 23:20:42] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -44.196763+0.005918j
[2025-08-26 23:20:55] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -44.209891+0.000447j
[2025-08-26 23:21:07] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -44.206867-0.006848j
[2025-08-26 23:21:20] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -44.207156-0.004078j
[2025-08-26 23:21:32] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -44.206377-0.003066j
[2025-08-26 23:21:45] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -44.205220+0.002916j
[2025-08-26 23:21:57] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -44.191699+0.005215j
[2025-08-26 23:22:10] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -44.230040-0.005242j
[2025-08-26 23:22:22] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -44.196968-0.005589j
[2025-08-26 23:22:35] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -44.194685-0.005045j
[2025-08-26 23:22:47] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -44.203972+0.000245j
[2025-08-26 23:23:00] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -44.196328-0.000955j
[2025-08-26 23:23:12] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -44.206980-0.003707j
[2025-08-26 23:23:25] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -44.221548+0.002002j
[2025-08-26 23:23:25] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-26 23:23:37] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -44.208307+0.005777j
[2025-08-26 23:23:50] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -44.213140-0.003119j
[2025-08-26 23:24:02] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -44.210902+0.003300j
[2025-08-26 23:24:15] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -44.217828+0.000026j
[2025-08-26 23:24:27] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -44.213213+0.004202j
[2025-08-26 23:24:40] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -44.191827+0.000015j
[2025-08-26 23:24:52] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -44.203588+0.000867j
[2025-08-26 23:25:05] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -44.195992+0.000891j
[2025-08-26 23:25:17] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -44.204783+0.002030j
[2025-08-26 23:25:30] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -44.213942+0.000636j
[2025-08-26 23:25:42] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -44.207720+0.000980j
[2025-08-26 23:25:55] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -44.212923-0.001661j
[2025-08-26 23:26:07] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -44.208156+0.005037j
[2025-08-26 23:26:20] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -44.203586-0.002897j
[2025-08-26 23:26:32] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -44.221591-0.001296j
[2025-08-26 23:26:45] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -44.215479+0.006142j
[2025-08-26 23:26:57] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -44.202279+0.000814j
[2025-08-26 23:27:10] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -44.210138-0.000389j
[2025-08-26 23:27:22] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -44.212397+0.001484j
[2025-08-26 23:27:35] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -44.206259-0.005175j
[2025-08-26 23:27:47] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -44.224833+0.000389j
[2025-08-26 23:28:00] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -44.196449-0.003042j
[2025-08-26 23:28:12] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -44.207607+0.001356j
[2025-08-26 23:28:25] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -44.217697-0.004191j
[2025-08-26 23:28:37] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -44.198234+0.001117j
[2025-08-26 23:28:50] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -44.204764+0.000303j
[2025-08-26 23:29:02] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -44.215920-0.003887j
[2025-08-26 23:29:15] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -44.195493-0.004303j
[2025-08-26 23:29:27] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -44.202599-0.000719j
[2025-08-26 23:29:40] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -44.207532-0.002748j
[2025-08-26 23:29:53] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -44.216344+0.002587j
[2025-08-26 23:30:05] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -44.223492-0.001077j
[2025-08-26 23:30:18] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -44.209593+0.000975j
[2025-08-26 23:30:30] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -44.192248+0.002003j
[2025-08-26 23:30:43] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -44.207495+0.001310j
[2025-08-26 23:30:55] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -44.222086+0.003562j
[2025-08-26 23:31:08] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -44.218466-0.000545j
[2025-08-26 23:31:20] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -44.209367+0.000044j
[2025-08-26 23:31:33] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -44.204454+0.004318j
[2025-08-26 23:31:45] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -44.206760+0.000918j
[2025-08-26 23:31:58] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -44.192821-0.002531j
[2025-08-26 23:32:10] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -44.202552-0.002133j
[2025-08-26 23:32:23] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -44.213019-0.004956j
[2025-08-26 23:32:35] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -44.209352-0.003496j
[2025-08-26 23:32:48] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -44.189581+0.003112j
[2025-08-26 23:33:00] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -44.211969-0.002479j
[2025-08-26 23:33:13] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -44.221619+0.006228j
[2025-08-26 23:33:25] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -44.220480-0.002999j
[2025-08-26 23:33:37] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -44.207657-0.001053j
[2025-08-26 23:33:50] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -44.213990-0.002069j
[2025-08-26 23:33:50] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-26 23:34:02] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -44.208189+0.005218j
[2025-08-26 23:34:15] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -44.204369-0.001190j
[2025-08-26 23:34:27] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -44.197325+0.003358j
[2025-08-26 23:34:40] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -44.217786-0.000041j
[2025-08-26 23:34:52] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -44.226014-0.002441j
[2025-08-26 23:35:05] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -44.209208-0.003125j
[2025-08-26 23:35:18] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -44.217532+0.000517j
[2025-08-26 23:35:30] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -44.189890-0.000095j
[2025-08-26 23:35:43] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -44.217393+0.002589j
[2025-08-26 23:35:55] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -44.225404+0.000610j
[2025-08-26 23:36:08] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -44.206537+0.000700j
[2025-08-26 23:36:20] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -44.211656-0.000578j
[2025-08-26 23:36:33] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -44.216083-0.001549j
[2025-08-26 23:36:45] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -44.205561+0.007566j
[2025-08-26 23:36:58] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -44.208839+0.001378j
[2025-08-26 23:37:10] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -44.225394-0.003275j
[2025-08-26 23:37:23] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -44.191054-0.000053j
[2025-08-26 23:37:35] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -44.224685+0.002768j
[2025-08-26 23:37:48] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -44.201716+0.000889j
[2025-08-26 23:38:00] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -44.209216+0.002106j
[2025-08-26 23:38:13] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -44.194502+0.002113j
[2025-08-26 23:38:25] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -44.209239-0.001668j
[2025-08-26 23:38:38] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -44.214907-0.002499j
[2025-08-26 23:38:50] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -44.209753-0.001845j
[2025-08-26 23:39:03] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -44.199601-0.001288j
[2025-08-26 23:39:15] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -44.202360-0.002745j
[2025-08-26 23:39:28] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -44.204805+0.000372j
[2025-08-26 23:39:40] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -44.226327-0.002956j
[2025-08-26 23:39:53] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -44.209981-0.005889j
[2025-08-26 23:40:05] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -44.197654-0.004105j
[2025-08-26 23:40:18] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -44.196061+0.003024j
[2025-08-26 23:40:30] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -44.199483-0.001430j
[2025-08-26 23:40:43] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -44.213344-0.000286j
[2025-08-26 23:40:55] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -44.206604+0.006210j
[2025-08-26 23:41:08] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -44.198346+0.000081j
[2025-08-26 23:41:20] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -44.201644+0.002100j
[2025-08-26 23:41:33] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -44.218452-0.005548j
[2025-08-26 23:41:45] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -44.222908+0.001380j
[2025-08-26 23:41:58] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -44.189868+0.006436j
[2025-08-26 23:42:10] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -44.229143+0.001039j
[2025-08-26 23:42:23] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -44.215480+0.001119j
[2025-08-26 23:42:35] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -44.214091-0.002649j
[2025-08-26 23:42:48] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -44.191798-0.000674j
[2025-08-26 23:43:00] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -44.208231+0.000122j
[2025-08-26 23:43:13] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -44.200520-0.001865j
[2025-08-26 23:43:25] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -44.201106-0.004909j
[2025-08-26 23:43:37] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -44.195290+0.001698j
[2025-08-26 23:43:50] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -44.206431-0.004646j
[2025-08-26 23:44:00] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -44.207497+0.002076j
[2025-08-26 23:44:13] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -44.209469+0.002224j
[2025-08-26 23:44:13] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-26 23:44:26] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -44.212971-0.001112j
[2025-08-26 23:44:38] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -44.204737-0.002147j
[2025-08-26 23:44:51] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -44.216468-0.002589j
[2025-08-26 23:45:03] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -44.210038-0.003378j
[2025-08-26 23:45:16] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -44.229420+0.003198j
[2025-08-26 23:45:28] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -44.200820-0.001997j
[2025-08-26 23:45:41] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -44.225820-0.000465j
[2025-08-26 23:45:53] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -44.212384-0.000763j
[2025-08-26 23:46:06] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -44.222906+0.001672j
[2025-08-26 23:46:18] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -44.210387-0.000875j
[2025-08-26 23:46:31] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -44.229124+0.001912j
[2025-08-26 23:46:43] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -44.226459-0.001864j
[2025-08-26 23:46:56] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -44.203330-0.001296j
[2025-08-26 23:47:08] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -44.225073-0.000079j
[2025-08-26 23:47:21] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -44.208755+0.003715j
[2025-08-26 23:47:33] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -44.209295+0.002059j
[2025-08-26 23:47:46] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -44.225580-0.002722j
[2025-08-26 23:47:58] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -44.201730-0.001184j
[2025-08-26 23:48:11] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -44.204576+0.003445j
[2025-08-26 23:48:23] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -44.209327-0.002253j
[2025-08-26 23:48:36] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -44.202873-0.000511j
[2025-08-26 23:48:48] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -44.207148+0.001452j
[2025-08-26 23:49:01] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -44.208073-0.001839j
[2025-08-26 23:49:14] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -44.213528+0.002835j
[2025-08-26 23:49:26] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -44.220059-0.003403j
[2025-08-26 23:49:39] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -44.207232-0.001712j
[2025-08-26 23:49:51] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -44.219540+0.000687j
[2025-08-26 23:50:04] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -44.217843-0.002816j
[2025-08-26 23:50:16] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -44.220116-0.001578j
[2025-08-26 23:50:29] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -44.197638-0.000475j
[2025-08-26 23:50:41] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -44.197532-0.002051j
[2025-08-26 23:50:54] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -44.212803+0.001591j
[2025-08-26 23:51:06] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -44.220898-0.002286j
[2025-08-26 23:51:19] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -44.202736+0.005399j
[2025-08-26 23:51:31] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -44.209094-0.000422j
[2025-08-26 23:51:44] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -44.233769+0.006089j
[2025-08-26 23:51:56] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -44.208688-0.001541j
[2025-08-26 23:52:09] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -44.199668+0.001681j
[2025-08-26 23:52:21] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -44.209687+0.004961j
[2025-08-26 23:52:34] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -44.215489+0.000287j
[2025-08-26 23:52:46] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -44.215859+0.004002j
[2025-08-26 23:52:59] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -44.209003+0.000856j
[2025-08-26 23:53:11] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -44.214198-0.001887j
[2025-08-26 23:53:24] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -44.220072+0.002419j
[2025-08-26 23:53:36] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -44.187824-0.003754j
[2025-08-26 23:53:49] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -44.211001+0.000530j
[2025-08-26 23:54:01] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -44.184384+0.003640j
[2025-08-26 23:54:14] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -44.207511-0.002994j
[2025-08-26 23:54:26] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -44.220221-0.004445j
[2025-08-26 23:54:33] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -44.212738+0.000870j
[2025-08-26 23:54:33] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-26 23:54:33] ✅ Training completed | Restarts: 1
[2025-08-26 23:54:33] ============================================================
[2025-08-26 23:54:33] Training completed | Runtime: 5662.9s
[2025-08-26 23:54:35] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-26 23:54:35] ============================================================
