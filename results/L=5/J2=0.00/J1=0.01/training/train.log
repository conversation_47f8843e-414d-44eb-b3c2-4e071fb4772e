[2025-08-27 19:37:52] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.02/training/checkpoints/final_GCNN.pkl
[2025-08-27 19:37:52]   - 迭代次数: final
[2025-08-27 19:37:52]   - 能量: -83.709665+0.005496j ± 0.109355
[2025-08-27 19:37:52]   - 时间戳: 2025-08-27T19:36:57.771637+08:00
[2025-08-27 19:38:03] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 19:38:03] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 19:38:03] ==================================================
[2025-08-27 19:38:03] GCNN for Shastry-Sutherland Model
[2025-08-27 19:38:03] ==================================================
[2025-08-27 19:38:03] System parameters:
[2025-08-27 19:38:03]   - System size: L=5, N=100
[2025-08-27 19:38:03]   - System parameters: J1=0.01, J2=0.0, Q=1.0
[2025-08-27 19:38:03] --------------------------------------------------
[2025-08-27 19:38:03] Model parameters:
[2025-08-27 19:38:03]   - Number of layers = 4
[2025-08-27 19:38:03]   - Number of features = 4
[2025-08-27 19:38:03]   - Total parameters = 19628
[2025-08-27 19:38:03] --------------------------------------------------
[2025-08-27 19:38:03] Training parameters:
[2025-08-27 19:38:03]   - Learning rate: 0.015
[2025-08-27 19:38:03]   - Total iterations: 450
[2025-08-27 19:38:03]   - Annealing cycles: 2
[2025-08-27 19:38:03]   - Initial period: 150
[2025-08-27 19:38:03]   - Period multiplier: 2.0
[2025-08-27 19:38:03]   - Temperature range: 0.0-1.0
[2025-08-27 19:38:03]   - Samples: 4096
[2025-08-27 19:38:03]   - Discarded samples: 0
[2025-08-27 19:38:03]   - Chunk size: 2048
[2025-08-27 19:38:03]   - Diagonal shift: 0.2
[2025-08-27 19:38:03]   - Gradient clipping: 1.0
[2025-08-27 19:38:03]   - Checkpoint enabled: interval=50
[2025-08-27 19:38:03]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.01/training/checkpoints
[2025-08-27 19:38:03] --------------------------------------------------
[2025-08-27 19:38:03] Device status:
[2025-08-27 19:38:03]   - Devices model: NVIDIA H200 NVL
[2025-08-27 19:38:03]   - Number of devices: 1
[2025-08-27 19:38:03]   - Sharding: True
[2025-08-27 19:38:03] ============================================================
[2025-08-27 19:38:53] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -83.176536-0.001228j
[2025-08-27 19:39:31] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -83.185469+0.007928j
[2025-08-27 19:39:52] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -83.224739+0.013354j
[2025-08-27 19:40:12] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -83.239519+0.005375j
[2025-08-27 19:40:33] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -83.231311+0.001590j
[2025-08-27 19:40:53] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -83.318650+0.002383j
[2025-08-27 19:41:14] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -83.268433-0.001187j
[2025-08-27 19:41:34] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -83.363692+0.004934j
[2025-08-27 19:41:55] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -83.357547-0.000043j
[2025-08-27 19:42:15] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -83.169147+0.002255j
[2025-08-27 19:42:36] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -83.328744+0.001971j
[2025-08-27 19:42:56] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -83.406920+0.009912j
[2025-08-27 19:43:16] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -83.339346-0.001678j
[2025-08-27 19:43:37] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -83.285933+0.001944j
[2025-08-27 19:43:57] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -83.263432+0.000586j
[2025-08-27 19:44:18] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -83.222180-0.001734j
[2025-08-27 19:44:38] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -83.251144+0.005630j
[2025-08-27 19:44:59] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -83.266785+0.000356j
[2025-08-27 19:45:19] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -83.270528+0.000892j
[2025-08-27 19:45:40] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -83.137651-0.002685j
[2025-08-27 19:46:00] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -83.200056+0.010268j
[2025-08-27 19:46:21] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -83.157108-0.007791j
[2025-08-27 19:46:41] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -83.131379-0.007578j
[2025-08-27 19:47:02] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -83.052321-0.003197j
[2025-08-27 19:47:22] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -83.025719+0.001787j
[2025-08-27 19:47:43] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -83.134671-0.006671j
[2025-08-27 19:48:03] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -83.023371-0.004921j
[2025-08-27 19:48:23] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -83.071473-0.002579j
[2025-08-27 19:48:44] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -83.181848-0.003159j
[2025-08-27 19:49:04] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -83.014932-0.012428j
[2025-08-27 19:49:25] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -82.983730-0.001217j
[2025-08-27 19:49:45] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -83.090572+0.004413j
[2025-08-27 19:50:06] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -83.095084-0.010252j
[2025-08-27 19:50:26] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -83.096306+0.002683j
[2025-08-27 19:50:47] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -83.236062+0.004318j
[2025-08-27 19:51:07] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -83.154727-0.007519j
[2025-08-27 19:51:28] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -83.221252-0.008981j
[2025-08-27 19:51:48] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -83.252534-0.002131j
[2025-08-27 19:52:09] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -83.212959-0.004830j
[2025-08-27 19:52:29] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -83.212089-0.001220j
[2025-08-27 19:52:50] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -83.225236-0.002416j
[2025-08-27 19:53:10] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -83.271554+0.005195j
[2025-08-27 19:53:30] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -83.177000+0.010916j
[2025-08-27 19:53:51] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -83.272562+0.000401j
[2025-08-27 19:54:11] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -83.161826+0.001920j
[2025-08-27 19:54:32] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -83.271174+0.004742j
[2025-08-27 19:54:52] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -83.234688+0.000428j
[2025-08-27 19:55:13] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -83.312224+0.004292j
[2025-08-27 19:55:33] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -83.107193+0.006709j
[2025-08-27 19:55:54] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -83.257961-0.005609j
[2025-08-27 19:55:54] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 19:56:14] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -83.061768-0.005356j
[2025-08-27 19:56:35] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -83.082348-0.003477j
[2025-08-27 19:56:55] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -83.135130+0.001529j
[2025-08-27 19:57:16] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -83.253056-0.007573j
[2025-08-27 19:57:36] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -83.287052-0.001793j
[2025-08-27 19:57:57] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -83.246242-0.001777j
[2025-08-27 19:58:17] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -83.179153-0.004069j
[2025-08-27 19:58:37] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -83.307246+0.001095j
[2025-08-27 19:58:58] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -83.260984+0.005173j
[2025-08-27 19:59:18] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -83.256844-0.001365j
[2025-08-27 19:59:39] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -83.119863+0.002453j
[2025-08-27 19:59:59] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -83.146386-0.000422j
[2025-08-27 20:00:20] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -83.149569-0.005244j
[2025-08-27 20:00:40] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -83.140192+0.003797j
[2025-08-27 20:01:01] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -83.143990-0.005279j
[2025-08-27 20:01:21] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -83.048180-0.002535j
[2025-08-27 20:01:42] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -83.000641+0.006039j
[2025-08-27 20:02:02] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -83.096683+0.000300j
[2025-08-27 20:02:22] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -82.965266-0.000127j
[2025-08-27 20:02:43] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -83.036135+0.001806j
[2025-08-27 20:03:03] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -83.034654-0.001645j
[2025-08-27 20:03:24] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -83.030548+0.000240j
[2025-08-27 20:03:44] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -83.037652+0.004811j
[2025-08-27 20:04:05] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -83.080766+0.007155j
[2025-08-27 20:04:25] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -83.106841-0.008843j
[2025-08-27 20:04:46] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -82.956103+0.008763j
[2025-08-27 20:05:06] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -83.015077+0.005539j
[2025-08-27 20:05:27] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -83.042493-0.004584j
[2025-08-27 20:05:47] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -83.159504-0.010771j
[2025-08-27 20:06:07] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -83.035849+0.005641j
[2025-08-27 20:06:28] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -83.143938+0.003824j
[2025-08-27 20:06:48] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -83.099696+0.000406j
[2025-08-27 20:07:09] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -83.079003-0.005007j
[2025-08-27 20:07:29] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -83.174408-0.007850j
[2025-08-27 20:07:50] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -83.109821+0.001674j
[2025-08-27 20:08:10] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -82.946070+0.002294j
[2025-08-27 20:08:31] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -82.984652-0.007499j
[2025-08-27 20:08:51] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -82.938393-0.002740j
[2025-08-27 20:09:12] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -83.215993+0.003204j
[2025-08-27 20:09:33] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -83.102800+0.005704j
[2025-08-27 20:09:53] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -83.201798-0.003134j
[2025-08-27 20:10:14] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -83.227933-0.001559j
[2025-08-27 20:10:34] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -82.981479+0.005613j
[2025-08-27 20:10:55] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -83.168171+0.003439j
[2025-08-27 20:11:15] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -82.987125+0.001650j
[2025-08-27 20:11:36] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -83.195020+0.000687j
[2025-08-27 20:11:56] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -83.229577+0.005261j
[2025-08-27 20:12:16] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -83.158623+0.008977j
[2025-08-27 20:12:37] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -83.189146-0.003689j
[2025-08-27 20:12:57] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -83.260795-0.005185j
[2025-08-27 20:12:57] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 20:13:18] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -83.179523+0.008098j
[2025-08-27 20:13:38] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -83.304795+0.003537j
[2025-08-27 20:13:59] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -83.213348-0.010666j
[2025-08-27 20:14:19] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -83.116565+0.000906j
[2025-08-27 20:14:40] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -83.012806+0.000604j
[2025-08-27 20:15:00] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -83.140226-0.000344j
[2025-08-27 20:15:21] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -83.193820-0.000148j
[2025-08-27 20:15:41] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -83.209641+0.000889j
[2025-08-27 20:16:01] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -83.048319-0.005118j
[2025-08-27 20:16:22] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -83.212624-0.004959j
[2025-08-27 20:16:42] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -83.052291-0.001009j
[2025-08-27 20:17:03] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -82.950785-0.005181j
[2025-08-27 20:17:23] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -83.113422-0.003830j
[2025-08-27 20:17:44] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -83.153099-0.005459j
[2025-08-27 20:18:04] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -83.151714+0.005396j
[2025-08-27 20:18:25] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -83.103599-0.001128j
[2025-08-27 20:18:45] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -83.388048+0.006303j
[2025-08-27 20:19:06] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -83.181586-0.009226j
[2025-08-27 20:19:26] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -83.227706+0.009940j
[2025-08-27 20:19:47] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -83.183654+0.006473j
[2025-08-27 20:20:07] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -83.224616+0.011992j
[2025-08-27 20:20:27] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -83.220185+0.006693j
[2025-08-27 20:20:48] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -83.185927-0.006693j
[2025-08-27 20:21:08] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -83.131147+0.004157j
[2025-08-27 20:21:29] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -83.122907-0.000219j
[2025-08-27 20:21:49] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -83.165873+0.005632j
[2025-08-27 20:22:10] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -83.320001+0.000619j
[2025-08-27 20:22:30] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -83.385209-0.000541j
[2025-08-27 20:22:51] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -83.125456+0.010016j
[2025-08-27 20:23:11] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -83.295666-0.000316j
[2025-08-27 20:23:32] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -83.258858+0.005736j
[2025-08-27 20:23:52] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -83.290974-0.001598j
[2025-08-27 20:24:13] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -83.175751-0.009071j
[2025-08-27 20:24:33] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -83.184769+0.005440j
[2025-08-27 20:24:53] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -83.321192-0.003036j
[2025-08-27 20:25:14] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -83.024724-0.008958j
[2025-08-27 20:25:34] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -83.087946+0.001228j
[2025-08-27 20:25:55] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -83.034891+0.003914j
[2025-08-27 20:26:15] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -83.205181-0.005729j
[2025-08-27 20:26:36] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -83.240245+0.000036j
[2025-08-27 20:26:56] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -83.097575-0.002377j
[2025-08-27 20:27:17] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -83.040663-0.001940j
[2025-08-27 20:27:37] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -83.184552-0.004014j
[2025-08-27 20:27:58] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -83.342409-0.001719j
[2025-08-27 20:28:18] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -83.114904-0.004326j
[2025-08-27 20:28:39] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -83.147107-0.001549j
[2025-08-27 20:28:59] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -83.195007-0.004178j
[2025-08-27 20:29:19] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -83.195280-0.003223j
[2025-08-27 20:29:40] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -83.186420+0.005616j
[2025-08-27 20:30:00] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -83.001021-0.002708j
[2025-08-27 20:30:00] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 20:30:00] RESTART #1 | Period: 300
[2025-08-27 20:30:21] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -83.054065-0.005307j
[2025-08-27 20:30:41] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -83.160748-0.001536j
[2025-08-27 20:31:02] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -83.090484+0.008158j
[2025-08-27 20:31:22] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -82.986802+0.009548j
[2025-08-27 20:31:43] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -83.040835-0.001385j
[2025-08-27 20:32:03] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -82.837561-0.001059j
[2025-08-27 20:32:24] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -82.944037+0.008236j
[2025-08-27 20:32:44] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -82.875640-0.001105j
[2025-08-27 20:33:04] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -82.819390+0.009769j
[2025-08-27 20:33:25] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -82.897038-0.001338j
[2025-08-27 20:33:45] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -82.914680+0.001291j
[2025-08-27 20:34:06] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -83.064279-0.000135j
[2025-08-27 20:34:26] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -83.068845-0.006797j
[2025-08-27 20:34:47] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -82.988521+0.006278j
[2025-08-27 20:35:07] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -83.059613+0.005479j
[2025-08-27 20:35:28] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -83.043119+0.002973j
[2025-08-27 20:35:48] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -83.029068+0.001165j
[2025-08-27 20:36:09] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -83.025313+0.001382j
[2025-08-27 20:36:29] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -83.215682-0.003620j
[2025-08-27 20:36:50] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -83.156521+0.002740j
[2025-08-27 20:37:10] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -83.088826-0.001344j
[2025-08-27 20:37:31] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -83.220914-0.000790j
[2025-08-27 20:37:51] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -83.030923-0.003739j
[2025-08-27 20:38:11] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -83.070572+0.001031j
[2025-08-27 20:38:32] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -83.226750-0.011866j
[2025-08-27 20:38:52] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -83.314008+0.005245j
[2025-08-27 20:39:13] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -83.293246-0.003337j
[2025-08-27 20:39:33] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -83.336259-0.004866j
[2025-08-27 20:39:54] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -83.216165-0.004042j
[2025-08-27 20:40:14] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -83.332337+0.003682j
[2025-08-27 20:40:35] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -83.260698+0.003493j
[2025-08-27 20:40:55] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -83.131336+0.000582j
[2025-08-27 20:41:16] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -83.009850+0.003261j
[2025-08-27 20:41:36] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -83.029655-0.010405j
[2025-08-27 20:41:56] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -82.998787+0.005149j
[2025-08-27 20:42:17] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -83.010124+0.000637j
[2025-08-27 20:42:37] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -83.157006-0.004423j
[2025-08-27 20:42:58] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -83.077800-0.001144j
[2025-08-27 20:43:18] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -83.104151-0.002843j
[2025-08-27 20:43:39] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -82.963878+0.000380j
[2025-08-27 20:43:59] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -83.077315+0.009664j
[2025-08-27 20:44:20] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -83.005837-0.003450j
[2025-08-27 20:44:40] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -83.124005-0.001929j
[2025-08-27 20:45:01] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -83.068961+0.005033j
[2025-08-27 20:45:21] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -83.153155-0.008459j
[2025-08-27 20:45:41] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -83.337325-0.003685j
[2025-08-27 20:46:02] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -83.227129+0.004210j
[2025-08-27 20:46:22] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -83.238113+0.000834j
[2025-08-27 20:46:43] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -83.227782+0.004998j
[2025-08-27 20:47:03] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -83.132145-0.001972j
[2025-08-27 20:47:03] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 20:47:24] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -83.118741+0.001351j
[2025-08-27 20:47:44] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -83.201888+0.002773j
[2025-08-27 20:48:05] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -83.049916+0.000251j
[2025-08-27 20:48:25] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -83.021038+0.001468j
[2025-08-27 20:48:46] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -83.016591+0.001769j
[2025-08-27 20:49:06] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -83.089858-0.006442j
[2025-08-27 20:49:26] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -83.181243-0.002754j
[2025-08-27 20:49:47] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -83.160074-0.017134j
[2025-08-27 20:50:07] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -83.178580+0.001533j
[2025-08-27 20:50:28] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -83.095771-0.003514j
[2025-08-27 20:50:49] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -83.216914-0.000199j
[2025-08-27 20:51:09] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -83.184596+0.001902j
[2025-08-27 20:51:29] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -83.079153-0.003742j
[2025-08-27 20:51:50] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -83.053724+0.001052j
[2025-08-27 20:52:10] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -83.096881+0.001471j
[2025-08-27 20:52:31] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -83.121051+0.002799j
[2025-08-27 20:52:51] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -83.191820-0.006144j
[2025-08-27 20:53:12] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -83.085956-0.008995j
[2025-08-27 20:53:32] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -82.888033-0.001298j
[2025-08-27 20:53:53] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -83.106475-0.007584j
[2025-08-27 20:54:13] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -83.135239-0.000175j
[2025-08-27 20:54:34] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -83.137669-0.000582j
[2025-08-27 20:54:54] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -83.000795-0.002349j
[2025-08-27 20:55:15] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -83.134252-0.000267j
[2025-08-27 20:55:35] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -83.114558-0.002386j
[2025-08-27 20:55:56] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -83.302891-0.004330j
[2025-08-27 20:56:16] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -83.030160-0.002663j
[2025-08-27 20:56:36] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -83.057210+0.004447j
[2025-08-27 20:56:57] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -83.108744+0.006990j
[2025-08-27 20:57:17] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -83.046982-0.005084j
[2025-08-27 20:57:38] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -83.031726-0.002765j
[2025-08-27 20:57:58] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -83.076043-0.002366j
[2025-08-27 20:58:19] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -82.958293+0.004601j
[2025-08-27 20:58:39] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -83.080520-0.000121j
[2025-08-27 20:59:00] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -83.196852-0.011241j
[2025-08-27 20:59:20] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -83.092164+0.001753j
[2025-08-27 20:59:41] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -83.111848-0.001125j
[2025-08-27 21:00:01] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -83.126131-0.000331j
[2025-08-27 21:00:22] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -83.156245+0.006342j
[2025-08-27 21:00:42] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -83.084671-0.004844j
[2025-08-27 21:01:03] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -82.956271+0.001681j
[2025-08-27 21:01:23] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -83.039242+0.000711j
[2025-08-27 21:01:44] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -82.906201-0.002916j
[2025-08-27 21:02:04] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -83.132526-0.004866j
[2025-08-27 21:02:25] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -83.207426-0.000377j
[2025-08-27 21:02:45] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -83.242787+0.006838j
[2025-08-27 21:03:05] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -83.318813-0.003951j
[2025-08-27 21:03:26] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -83.451783-0.006847j
[2025-08-27 21:03:46] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -83.266790-0.000878j
[2025-08-27 21:04:07] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -83.224505-0.000261j
[2025-08-27 21:04:07] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 21:04:27] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -83.309752+0.005088j
[2025-08-27 21:04:47] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -83.251914+0.001457j
[2025-08-27 21:05:08] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -83.293265-0.013613j
[2025-08-27 21:05:28] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -83.213260-0.002419j
[2025-08-27 21:05:48] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -83.187889-0.010775j
[2025-08-27 21:06:09] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -83.246607+0.001273j
[2025-08-27 21:06:29] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -83.137236-0.002215j
[2025-08-27 21:06:50] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -83.103939-0.001809j
[2025-08-27 21:07:10] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -83.036771+0.003000j
[2025-08-27 21:07:31] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -83.149987-0.000805j
[2025-08-27 21:07:51] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -83.435054-0.004991j
[2025-08-27 21:08:12] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -83.295736-0.007708j
[2025-08-27 21:08:32] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -83.325747+0.008001j
[2025-08-27 21:08:53] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -83.123108-0.005856j
[2025-08-27 21:09:13] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -83.018561+0.005714j
[2025-08-27 21:09:34] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -83.110828-0.000714j
[2025-08-27 21:09:54] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -83.192698-0.006147j
[2025-08-27 21:10:15] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -83.237423+0.000321j
[2025-08-27 21:10:35] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -83.003177-0.001624j
[2025-08-27 21:10:55] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -83.031045+0.007574j
[2025-08-27 21:11:16] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -82.998547-0.001610j
[2025-08-27 21:11:36] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -83.163530-0.000300j
[2025-08-27 21:11:57] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -83.142687+0.000007j
[2025-08-27 21:12:17] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -83.058324+0.000471j
[2025-08-27 21:12:38] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -83.021889-0.007058j
[2025-08-27 21:12:58] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -83.008303+0.000018j
[2025-08-27 21:13:19] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -83.082209-0.007052j
[2025-08-27 21:13:39] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -83.130632-0.000151j
[2025-08-27 21:14:00] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -83.027125-0.001832j
[2025-08-27 21:14:20] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -83.081214-0.003030j
[2025-08-27 21:14:41] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -82.990335+0.000431j
[2025-08-27 21:15:01] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -83.196573+0.000943j
[2025-08-27 21:15:22] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -83.241986+0.002508j
[2025-08-27 21:15:42] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -83.022193+0.001814j
[2025-08-27 21:16:03] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -83.014991-0.004710j
[2025-08-27 21:16:23] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -83.017728+0.002638j
[2025-08-27 21:16:43] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -82.942522+0.005025j
[2025-08-27 21:17:04] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -83.009332-0.005749j
[2025-08-27 21:17:24] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -83.081601-0.003023j
[2025-08-27 21:17:45] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -83.005051+0.004308j
[2025-08-27 21:18:05] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -83.000894-0.007557j
[2025-08-27 21:18:26] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -82.969346-0.005145j
[2025-08-27 21:18:46] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -83.090492-0.007237j
[2025-08-27 21:19:07] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -83.095820-0.002174j
[2025-08-27 21:19:27] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -83.057534-0.007886j
[2025-08-27 21:19:48] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -82.946248-0.010718j
[2025-08-27 21:20:08] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -83.121749-0.000594j
[2025-08-27 21:20:28] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -83.131450+0.002029j
[2025-08-27 21:20:49] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -83.091276+0.001469j
[2025-08-27 21:21:09] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -83.046395+0.003809j
[2025-08-27 21:21:10] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 21:21:30] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -82.902478-0.000445j
[2025-08-27 21:21:51] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -83.093151-0.004435j
[2025-08-27 21:22:11] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -82.955014-0.002782j
[2025-08-27 21:22:31] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -83.117435-0.000422j
[2025-08-27 21:22:52] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -83.186340-0.001672j
[2025-08-27 21:23:12] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -83.174383-0.009567j
[2025-08-27 21:23:33] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -83.131132+0.001508j
[2025-08-27 21:23:53] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -83.146031-0.000518j
[2025-08-27 21:24:14] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -83.039929+0.001505j
[2025-08-27 21:24:34] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -83.064498+0.005955j
[2025-08-27 21:24:55] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -83.047678+0.003219j
[2025-08-27 21:25:15] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -83.018097+0.006030j
[2025-08-27 21:25:36] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -83.086950-0.001894j
[2025-08-27 21:25:56] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -82.963357+0.009385j
[2025-08-27 21:26:16] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -82.963700-0.000485j
[2025-08-27 21:26:37] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -83.027009+0.005251j
[2025-08-27 21:26:57] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -83.027314+0.003677j
[2025-08-27 21:27:18] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -83.037402-0.009889j
[2025-08-27 21:27:38] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -82.987147+0.001188j
[2025-08-27 21:27:59] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -83.023518-0.003957j
[2025-08-27 21:28:19] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -83.051169-0.004352j
[2025-08-27 21:28:40] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -83.086765-0.008443j
[2025-08-27 21:29:00] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -83.108918+0.004659j
[2025-08-27 21:29:21] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -83.051485+0.001261j
[2025-08-27 21:29:41] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -82.995137+0.001013j
[2025-08-27 21:30:02] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -83.164098+0.002204j
[2025-08-27 21:30:22] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -83.220277-0.002469j
[2025-08-27 21:30:43] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -83.205080-0.000443j
[2025-08-27 21:31:03] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -83.142081+0.010029j
[2025-08-27 21:31:23] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -83.113402+0.006743j
[2025-08-27 21:31:44] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -83.162991+0.010492j
[2025-08-27 21:32:04] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -83.265965+0.001708j
[2025-08-27 21:32:24] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -83.217915-0.004985j
[2025-08-27 21:32:45] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -83.229537-0.002288j
[2025-08-27 21:33:05] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -83.256771-0.000682j
[2025-08-27 21:33:26] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -83.089219-0.005453j
[2025-08-27 21:33:46] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -83.331621+0.003667j
[2025-08-27 21:34:07] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -83.211729+0.003061j
[2025-08-27 21:34:27] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -83.331082-0.003320j
[2025-08-27 21:34:48] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -83.159541+0.001621j
[2025-08-27 21:35:08] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -83.221391+0.004114j
[2025-08-27 21:35:29] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -83.179927-0.006030j
[2025-08-27 21:35:49] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -83.048236+0.004580j
[2025-08-27 21:36:09] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -83.052675+0.008212j
[2025-08-27 21:36:30] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -83.089772+0.003563j
[2025-08-27 21:36:50] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -83.178723+0.003951j
[2025-08-27 21:37:11] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -82.920945+0.002040j
[2025-08-27 21:37:31] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -83.030191-0.005352j
[2025-08-27 21:37:52] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -83.122337-0.005005j
[2025-08-27 21:38:12] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -82.973204-0.002165j
[2025-08-27 21:38:12] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 21:38:33] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -83.076486-0.007289j
[2025-08-27 21:38:53] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -83.126500-0.007875j
[2025-08-27 21:39:14] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -83.247113-0.005230j
[2025-08-27 21:39:34] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -83.203062+0.004139j
[2025-08-27 21:39:54] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -83.138225-0.004250j
[2025-08-27 21:40:15] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -83.053544+0.000995j
[2025-08-27 21:40:35] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -83.013149+0.002103j
[2025-08-27 21:40:56] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -83.016099-0.003975j
[2025-08-27 21:41:16] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -83.068609-0.000256j
[2025-08-27 21:41:37] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -83.005472+0.001004j
[2025-08-27 21:41:57] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -82.911927-0.000186j
[2025-08-27 21:42:18] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -82.964376-0.001559j
[2025-08-27 21:42:38] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -82.909954-0.004639j
[2025-08-27 21:42:59] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -82.948106+0.010676j
[2025-08-27 21:43:19] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -83.023951+0.003103j
[2025-08-27 21:43:39] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -82.984023-0.002012j
[2025-08-27 21:44:00] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -83.050097+0.003135j
[2025-08-27 21:44:20] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -83.069086-0.001435j
[2025-08-27 21:44:41] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -82.974180-0.006152j
[2025-08-27 21:45:01] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -83.020153-0.000456j
[2025-08-27 21:45:22] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -83.009281-0.009981j
[2025-08-27 21:45:42] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -83.127271+0.001090j
[2025-08-27 21:46:03] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -83.296387+0.004785j
[2025-08-27 21:46:23] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -83.167872-0.000856j
[2025-08-27 21:46:44] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -82.939743-0.002455j
[2025-08-27 21:47:04] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -82.838592+0.001492j
[2025-08-27 21:47:25] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -82.895685-0.003939j
[2025-08-27 21:47:45] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -82.965012-0.002267j
[2025-08-27 21:48:05] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -82.946086-0.000297j
[2025-08-27 21:48:26] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -83.087231+0.004825j
[2025-08-27 21:48:46] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -82.930232-0.000630j
[2025-08-27 21:49:07] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -82.927148+0.006083j
[2025-08-27 21:49:27] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -83.027914+0.003959j
[2025-08-27 21:49:48] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -82.923240+0.006038j
[2025-08-27 21:50:08] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -82.936950+0.000003j
[2025-08-27 21:50:29] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -82.933215-0.012473j
[2025-08-27 21:50:49] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -82.963576+0.009889j
[2025-08-27 21:51:10] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -83.126473+0.010730j
[2025-08-27 21:51:30] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -83.225982-0.004515j
[2025-08-27 21:51:51] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -83.105546-0.002268j
[2025-08-27 21:52:11] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -83.116886-0.000516j
[2025-08-27 21:52:32] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -83.173423-0.001443j
[2025-08-27 21:52:52] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -83.202132-0.004598j
[2025-08-27 21:53:13] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -83.130922-0.003578j
[2025-08-27 21:53:33] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -83.037057-0.002426j
[2025-08-27 21:53:54] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -83.037289+0.003383j
[2025-08-27 21:54:14] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -83.120564+0.010661j
[2025-08-27 21:54:34] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -83.138407-0.005318j
[2025-08-27 21:54:55] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -83.227459-0.006809j
[2025-08-27 21:55:15] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -83.339863-0.000362j
[2025-08-27 21:55:15] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 21:55:36] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -83.220138+0.000274j
[2025-08-27 21:55:56] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -83.086492-0.000580j
[2025-08-27 21:56:17] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -83.168331-0.000867j
[2025-08-27 21:56:37] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -83.220506-0.004577j
[2025-08-27 21:56:58] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -83.145908+0.000493j
[2025-08-27 21:57:18] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -83.050109-0.000060j
[2025-08-27 21:57:38] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -83.058808-0.006954j
[2025-08-27 21:57:59] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -83.130463-0.003373j
[2025-08-27 21:58:19] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -83.165784+0.006873j
[2025-08-27 21:58:40] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -83.034297-0.002154j
[2025-08-27 21:59:00] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -82.940177+0.001387j
[2025-08-27 21:59:21] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -83.001563-0.000065j
[2025-08-27 21:59:41] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -83.070989-0.009538j
[2025-08-27 22:00:01] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -82.902173+0.005051j
[2025-08-27 22:00:22] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -83.160435-0.003249j
[2025-08-27 22:00:42] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -83.193591+0.003203j
[2025-08-27 22:01:03] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -83.012444-0.000010j
[2025-08-27 22:01:23] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -83.008652-0.012145j
[2025-08-27 22:01:44] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -83.042042+0.007047j
[2025-08-27 22:02:04] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -82.937435-0.003158j
[2025-08-27 22:02:24] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -83.170118+0.003726j
[2025-08-27 22:02:45] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -83.109592-0.002035j
[2025-08-27 22:03:05] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -83.196132+0.000964j
[2025-08-27 22:03:26] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -83.249536+0.004097j
[2025-08-27 22:03:46] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -83.266151-0.000884j
[2025-08-27 22:04:07] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -83.412606-0.004019j
[2025-08-27 22:04:27] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -83.219409-0.005667j
[2025-08-27 22:04:48] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -83.203915+0.003825j
[2025-08-27 22:05:08] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -83.097149-0.003933j
[2025-08-27 22:05:29] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -82.965733-0.004521j
[2025-08-27 22:05:49] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -83.121923-0.002154j
[2025-08-27 22:06:10] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -83.152945-0.002175j
[2025-08-27 22:06:30] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -83.040613-0.003702j
[2025-08-27 22:06:50] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -83.077932-0.006610j
[2025-08-27 22:07:11] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -83.076452+0.006422j
[2025-08-27 22:07:31] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -83.261286-0.002675j
[2025-08-27 22:07:52] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -83.212684-0.001619j
[2025-08-27 22:08:12] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -83.248053+0.004626j
[2025-08-27 22:08:33] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -83.055101-0.005855j
[2025-08-27 22:08:53] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -83.170605-0.006013j
[2025-08-27 22:09:14] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -83.160771+0.007686j
[2025-08-27 22:09:34] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -83.328967-0.004149j
[2025-08-27 22:09:55] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -83.400942-0.001943j
[2025-08-27 22:10:15] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -83.188800-0.001796j
[2025-08-27 22:10:36] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -83.292258+0.006065j
[2025-08-27 22:10:56] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -83.206752-0.004080j
[2025-08-27 22:11:17] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -83.298271-0.006559j
[2025-08-27 22:11:37] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -83.086701-0.005203j
[2025-08-27 22:11:58] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -83.086285+0.001312j
[2025-08-27 22:12:18] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -83.106397+0.006627j
[2025-08-27 22:12:18] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 22:12:18] ✅ Training completed | Restarts: 1
[2025-08-27 22:12:18] ============================================================
[2025-08-27 22:12:18] Training completed | Runtime: 9255.3s
[2025-08-27 22:12:27] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 22:12:27] ============================================================
