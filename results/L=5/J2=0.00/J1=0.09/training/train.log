[2025-08-28 00:22:23] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.08/training/checkpoints/final_GCNN.pkl
[2025-08-28 00:22:23]   - 迭代次数: final
[2025-08-28 00:22:23]   - 能量: -87.285128-0.008725j ± 0.105511
[2025-08-28 00:22:23]   - 时间戳: 2025-08-28T00:22:12.034200+08:00
[2025-08-28 00:22:33] ✓ 变分状态参数已从checkpoint恢复
[2025-08-28 00:22:33] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-28 00:22:33] ==================================================
[2025-08-28 00:22:33] GCNN for Shastry-Sutherland Model
[2025-08-28 00:22:33] ==================================================
[2025-08-28 00:22:33] System parameters:
[2025-08-28 00:22:33]   - System size: L=5, N=100
[2025-08-28 00:22:33]   - System parameters: J1=0.09, J2=0.0, Q=1.0
[2025-08-28 00:22:33] --------------------------------------------------
[2025-08-28 00:22:33] Model parameters:
[2025-08-28 00:22:33]   - Number of layers = 4
[2025-08-28 00:22:33]   - Number of features = 4
[2025-08-28 00:22:33]   - Total parameters = 19628
[2025-08-28 00:22:33] --------------------------------------------------
[2025-08-28 00:22:33] Training parameters:
[2025-08-28 00:22:33]   - Learning rate: 0.015
[2025-08-28 00:22:33]   - Total iterations: 450
[2025-08-28 00:22:33]   - Annealing cycles: 2
[2025-08-28 00:22:33]   - Initial period: 150
[2025-08-28 00:22:33]   - Period multiplier: 2.0
[2025-08-28 00:22:33]   - Temperature range: 0.0-1.0
[2025-08-28 00:22:33]   - Samples: 4096
[2025-08-28 00:22:33]   - Discarded samples: 0
[2025-08-28 00:22:33]   - Chunk size: 2048
[2025-08-28 00:22:33]   - Diagonal shift: 0.2
[2025-08-28 00:22:33]   - Gradient clipping: 1.0
[2025-08-28 00:22:33]   - Checkpoint enabled: interval=50
[2025-08-28 00:22:33]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.09/training/checkpoints
[2025-08-28 00:22:33] --------------------------------------------------
[2025-08-28 00:22:33] Device status:
[2025-08-28 00:22:33]   - Devices model: NVIDIA H200 NVL
[2025-08-28 00:22:34]   - Number of devices: 1
[2025-08-28 00:22:34]   - Sharding: True
[2025-08-28 00:22:34] ============================================================
[2025-08-28 00:23:14] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -88.516789+0.000885j
[2025-08-28 00:23:41] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -88.381417+0.016990j
[2025-08-28 00:23:51] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -88.215980+0.010174j
[2025-08-28 00:24:00] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -88.275657+0.005865j
[2025-08-28 00:24:09] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -88.279388+0.001619j
[2025-08-28 00:24:18] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -88.226407+0.000121j
[2025-08-28 00:24:27] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -88.261301-0.001133j
[2025-08-28 00:24:37] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -88.110207+0.004887j
[2025-08-28 00:24:46] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -88.142482-0.000076j
[2025-08-28 00:24:55] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -88.137109+0.000646j
[2025-08-28 00:25:04] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -88.146209-0.004148j
[2025-08-28 00:25:13] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -88.107619-0.000647j
[2025-08-28 00:25:23] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -88.082506+0.001347j
[2025-08-28 00:25:32] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -88.022187+0.001134j
[2025-08-28 00:25:41] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -88.026673-0.002745j
[2025-08-28 00:25:50] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -88.007052+0.007285j
[2025-08-28 00:25:59] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -88.119008-0.003348j
[2025-08-28 00:26:09] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -88.100816+0.002995j
[2025-08-28 00:26:18] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -88.244738+0.002172j
[2025-08-28 00:26:27] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -88.170925-0.008015j
[2025-08-28 00:26:36] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -88.159981+0.003603j
[2025-08-28 00:26:46] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -88.203239+0.003778j
[2025-08-28 00:26:55] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -88.168286+0.003972j
[2025-08-28 00:27:04] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -88.242520+0.000060j
[2025-08-28 00:27:13] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -88.221892-0.003640j
[2025-08-28 00:27:22] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -88.172997-0.006954j
[2025-08-28 00:27:32] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -88.342996-0.001227j
[2025-08-28 00:27:41] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -88.227990-0.005412j
[2025-08-28 00:27:50] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -88.196648-0.000154j
[2025-08-28 00:27:59] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -88.207467-0.000311j
[2025-08-28 00:28:09] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -88.186179-0.001752j
[2025-08-28 00:28:18] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -88.076583-0.000541j
[2025-08-28 00:28:27] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -88.181009+0.006892j
[2025-08-28 00:28:36] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -88.083136+0.002833j
[2025-08-28 00:28:45] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -88.093495-0.003335j
[2025-08-28 00:28:55] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -88.250035+0.000806j
[2025-08-28 00:29:04] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -88.019617+0.004493j
[2025-08-28 00:29:13] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -88.163884-0.000728j
[2025-08-28 00:29:22] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -88.117748+0.007899j
[2025-08-28 00:29:31] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -88.247676+0.002833j
[2025-08-28 00:29:41] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -88.167518+0.005001j
[2025-08-28 00:29:50] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -88.388430+0.004774j
[2025-08-28 00:29:59] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -88.234391-0.001416j
[2025-08-28 00:30:08] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -88.166394+0.001590j
[2025-08-28 00:30:18] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -88.185379-0.003514j
[2025-08-28 00:30:27] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -88.190952+0.001020j
[2025-08-28 00:30:36] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -88.130252-0.005279j
[2025-08-28 00:30:45] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -88.163534-0.002396j
[2025-08-28 00:30:54] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -88.152695+0.006938j
[2025-08-28 00:31:04] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -88.028702-0.008623j
[2025-08-28 00:31:04] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-28 00:31:13] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -88.039684+0.004574j
[2025-08-28 00:31:22] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -87.948882-0.000991j
[2025-08-28 00:31:31] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -87.984216-0.004516j
[2025-08-28 00:31:41] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -87.966312+0.004848j
[2025-08-28 00:31:50] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -87.861099-0.005216j
[2025-08-28 00:31:59] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -87.850044+0.003747j
[2025-08-28 00:32:08] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -87.913874+0.008325j
[2025-08-28 00:32:18] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -87.994130+0.001799j
[2025-08-28 00:32:27] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -88.023241+0.001826j
[2025-08-28 00:32:36] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -88.014267-0.001420j
[2025-08-28 00:32:45] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -88.131559-0.006020j
[2025-08-28 00:32:54] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -88.199566+0.000273j
[2025-08-28 00:33:04] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -88.131907+0.005178j
[2025-08-28 00:33:13] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -88.188917-0.008990j
[2025-08-28 00:33:22] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -88.030193+0.001857j
[2025-08-28 00:33:31] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -88.144149+0.003398j
[2025-08-28 00:33:41] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -88.015610+0.002635j
[2025-08-28 00:33:50] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -88.063396+0.004020j
[2025-08-28 00:33:59] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -88.052421-0.002813j
[2025-08-28 00:34:08] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -88.016785-0.000041j
[2025-08-28 00:34:17] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -87.981071-0.000860j
[2025-08-28 00:34:27] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -88.045626+0.002231j
[2025-08-28 00:34:36] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -88.042542+0.006218j
[2025-08-28 00:34:45] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -88.088479+0.005991j
[2025-08-28 00:34:54] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -87.977666-0.000894j
[2025-08-28 00:35:04] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -88.128004-0.000296j
[2025-08-28 00:35:13] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -88.000434-0.004756j
[2025-08-28 00:35:22] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -88.098619+0.001876j
[2025-08-28 00:35:31] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -88.111986+0.005699j
[2025-08-28 00:35:40] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -88.026455+0.001544j
[2025-08-28 00:35:50] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -88.158754+0.003627j
[2025-08-28 00:35:59] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -88.061593+0.000978j
[2025-08-28 00:36:08] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -88.052212-0.001297j
[2025-08-28 00:36:17] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -88.105942+0.002020j
[2025-08-28 00:36:26] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -88.143987-0.009024j
[2025-08-28 00:36:36] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -88.235288-0.002397j
[2025-08-28 00:36:45] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -88.300030+0.003902j
[2025-08-28 00:36:54] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -88.265534-0.002973j
[2025-08-28 00:37:03] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -88.245360+0.005016j
[2025-08-28 00:37:13] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -88.131627+0.001811j
[2025-08-28 00:37:22] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -88.120753+0.003685j
[2025-08-28 00:37:31] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -88.133835+0.005023j
[2025-08-28 00:37:40] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -88.099852-0.000675j
[2025-08-28 00:37:49] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -88.329840-0.008033j
[2025-08-28 00:37:59] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -88.115082+0.002463j
[2025-08-28 00:38:08] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -88.109613+0.004025j
[2025-08-28 00:38:17] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -88.010051-0.000839j
[2025-08-28 00:38:26] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -88.025009-0.002503j
[2025-08-28 00:38:35] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -87.905909+0.007590j
[2025-08-28 00:38:45] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -87.942076+0.009800j
[2025-08-28 00:38:45] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-28 00:38:54] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -87.878676-0.002238j
[2025-08-28 00:39:03] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -88.130444+0.004330j
[2025-08-28 00:39:13] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -88.084754-0.000073j
[2025-08-28 00:39:22] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -88.095458+0.000665j
[2025-08-28 00:39:31] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -88.344286-0.003210j
[2025-08-28 00:39:40] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -88.032294+0.005611j
[2025-08-28 00:39:49] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -88.023267-0.003024j
[2025-08-28 00:39:59] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -88.052197-0.002647j
[2025-08-28 00:40:08] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -88.094707-0.001186j
[2025-08-28 00:40:17] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -87.880897-0.005813j
[2025-08-28 00:40:26] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -88.172968+0.001913j
[2025-08-28 00:40:35] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -88.268201+0.011063j
[2025-08-28 00:40:45] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -88.180389+0.004312j
[2025-08-28 00:40:54] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -88.259213+0.001798j
[2025-08-28 00:41:03] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -88.228642-0.004242j
[2025-08-28 00:41:12] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -88.166356+0.003642j
[2025-08-28 00:41:22] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -88.225264-0.000702j
[2025-08-28 00:41:31] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -88.070002+0.007110j
[2025-08-28 00:41:40] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -87.912765-0.008078j
[2025-08-28 00:41:49] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -88.011640+0.008303j
[2025-08-28 00:41:58] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -87.989812+0.001557j
[2025-08-28 00:42:08] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -88.176553+0.002342j
[2025-08-28 00:42:17] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -88.211740-0.000509j
[2025-08-28 00:42:26] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -88.402594-0.002644j
[2025-08-28 00:42:35] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -88.146728-0.006704j
[2025-08-28 00:42:44] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -88.147295-0.006323j
[2025-08-28 00:42:54] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -88.030289-0.002122j
[2025-08-28 00:43:03] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -88.114577+0.000070j
[2025-08-28 00:43:12] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -88.110618+0.006517j
[2025-08-28 00:43:21] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -88.198884-0.001764j
[2025-08-28 00:43:30] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -87.996400+0.004598j
[2025-08-28 00:43:40] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -87.956361+0.004637j
[2025-08-28 00:43:49] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -87.905541+0.000584j
[2025-08-28 00:43:58] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -88.075778+0.002664j
[2025-08-28 00:44:07] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -88.098879-0.005326j
[2025-08-28 00:44:17] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -88.170904-0.001121j
[2025-08-28 00:44:26] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -88.067914-0.005541j
[2025-08-28 00:44:35] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -88.080715+0.009941j
[2025-08-28 00:44:44] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -88.091634+0.000644j
[2025-08-28 00:44:53] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -88.155225+0.001019j
[2025-08-28 00:45:03] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -88.088555+0.008146j
[2025-08-28 00:45:12] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -88.058394+0.004073j
[2025-08-28 00:45:21] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -88.074417+0.000180j
[2025-08-28 00:45:30] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -87.983057-0.004847j
[2025-08-28 00:45:39] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -87.966567+0.001649j
[2025-08-28 00:45:49] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -88.150384+0.003046j
[2025-08-28 00:45:58] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -88.155466+0.002803j
[2025-08-28 00:46:07] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -88.273296-0.009251j
[2025-08-28 00:46:16] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -88.369548+0.004224j
[2025-08-28 00:46:25] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -88.199330+0.005164j
[2025-08-28 00:46:26] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-28 00:46:26] RESTART #1 | Period: 300
[2025-08-28 00:46:35] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -88.218921-0.001558j
[2025-08-28 00:46:44] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -88.205394-0.002878j
[2025-08-28 00:46:53] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -88.150667-0.002345j
[2025-08-28 00:47:02] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -88.003130-0.000043j
[2025-08-28 00:47:12] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -88.138184+0.002805j
[2025-08-28 00:47:21] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -87.941022+0.000835j
[2025-08-28 00:47:30] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -87.936242-0.004799j
[2025-08-28 00:47:39] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -87.983380-0.002924j
[2025-08-28 00:47:49] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -88.058935-0.002754j
[2025-08-28 00:47:58] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -88.233527-0.000373j
[2025-08-28 00:48:07] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -88.122034-0.013794j
[2025-08-28 00:48:16] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -88.169311+0.003170j
[2025-08-28 00:48:25] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -88.113602-0.003981j
[2025-08-28 00:48:35] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -87.955911-0.003002j
[2025-08-28 00:48:44] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -88.119216-0.021781j
[2025-08-28 00:48:53] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -88.144202-0.000784j
[2025-08-28 00:49:02] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -88.124661+0.000644j
[2025-08-28 00:49:11] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -87.979482-0.004817j
[2025-08-28 00:49:21] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -87.905606+0.001392j
[2025-08-28 00:49:30] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -88.060469+0.001320j
[2025-08-28 00:49:39] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -87.979850-0.000384j
[2025-08-28 00:49:48] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -88.163020+0.007500j
[2025-08-28 00:49:58] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -88.305207+0.002792j
[2025-08-28 00:50:07] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -88.235417+0.007087j
[2025-08-28 00:50:16] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -88.058333+0.000871j
[2025-08-28 00:50:25] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -87.979833-0.015662j
[2025-08-28 00:50:34] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -88.023746+0.006667j
[2025-08-28 00:50:44] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -87.982439+0.006516j
[2025-08-28 00:50:53] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -87.958786+0.001122j
[2025-08-28 00:51:02] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -87.992980-0.004011j
[2025-08-28 00:51:11] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -87.942723-0.002926j
[2025-08-28 00:51:20] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -87.988308-0.002826j
[2025-08-28 00:51:30] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -87.955670+0.001706j
[2025-08-28 00:51:39] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -88.117629+0.004020j
[2025-08-28 00:51:48] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -88.244742+0.004748j
[2025-08-28 00:51:57] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -88.402382-0.004003j
[2025-08-28 00:52:06] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -88.230051+0.005418j
[2025-08-28 00:52:16] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -88.114155-0.003070j
[2025-08-28 00:52:25] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -88.199169+0.004323j
[2025-08-28 00:52:34] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -88.186702+0.003440j
[2025-08-28 00:52:43] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -88.235860-0.001393j
[2025-08-28 00:52:53] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -88.130077+0.004869j
[2025-08-28 00:53:02] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -88.264581+0.007371j
[2025-08-28 00:53:11] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -88.085410+0.001743j
[2025-08-28 00:53:20] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -88.381613-0.000888j
[2025-08-28 00:53:29] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -88.202733+0.002342j
[2025-08-28 00:53:39] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -88.353027-0.007528j
[2025-08-28 00:53:48] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -88.252296-0.000674j
[2025-08-28 00:53:57] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -88.116801-0.004532j
[2025-08-28 00:54:06] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -88.243182+0.005984j
[2025-08-28 00:54:06] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-28 00:54:16] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -88.335029-0.002434j
[2025-08-28 00:54:25] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -88.156050-0.002575j
[2025-08-28 00:54:34] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -88.297998-0.005661j
[2025-08-28 00:54:43] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -88.235472+0.001351j
[2025-08-28 00:54:52] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -88.279099+0.000621j
[2025-08-28 00:55:02] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -88.284706-0.001580j
[2025-08-28 00:55:11] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -88.355994+0.002814j
[2025-08-28 00:55:20] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -88.310524-0.001530j
[2025-08-28 00:55:29] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -88.277744-0.001535j
[2025-08-28 00:55:38] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -88.292597-0.001031j
[2025-08-28 00:55:48] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -88.231677-0.001262j
[2025-08-28 00:55:57] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -88.268534-0.000120j
[2025-08-28 00:56:06] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -88.199568+0.003174j
[2025-08-28 00:56:15] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -88.129119-0.004182j
[2025-08-28 00:56:25] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -87.943160+0.002353j
[2025-08-28 00:56:34] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -88.070711+0.006154j
[2025-08-28 00:56:43] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -88.144440-0.003792j
[2025-08-28 00:56:52] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -88.173623-0.004283j
[2025-08-28 00:57:01] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -88.209119+0.006274j
[2025-08-28 00:57:11] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -88.132064-0.007141j
[2025-08-28 00:57:20] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -88.245169+0.008406j
[2025-08-28 00:57:29] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -88.204860+0.000638j
[2025-08-28 00:57:38] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -88.079225-0.002422j
[2025-08-28 00:57:47] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -87.896163+0.004636j
[2025-08-28 00:57:57] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -87.994667-0.001796j
[2025-08-28 00:58:06] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -88.109042-0.006469j
[2025-08-28 00:58:15] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -88.080367+0.006455j
[2025-08-28 00:58:24] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -88.113353+0.000578j
[2025-08-28 00:58:34] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -88.095804-0.001313j
[2025-08-28 00:58:43] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -87.959954-0.001104j
[2025-08-28 00:58:52] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -88.041963+0.002816j
[2025-08-28 00:59:01] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -88.182992+0.006342j
[2025-08-28 00:59:10] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -88.116763-0.004038j
[2025-08-28 00:59:20] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -87.984862+0.000333j
[2025-08-28 00:59:29] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -87.973068-0.001535j
[2025-08-28 00:59:38] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -88.029915+0.009212j
[2025-08-28 00:59:47] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -88.033184+0.002674j
[2025-08-28 00:59:56] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -88.040252-0.000350j
[2025-08-28 01:00:06] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -88.219029-0.002256j
[2025-08-28 01:00:15] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -88.171729-0.000615j
[2025-08-28 01:00:24] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -88.073482-0.008784j
[2025-08-28 01:00:33] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -87.978057-0.000635j
[2025-08-28 01:00:42] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -88.189110+0.004650j
[2025-08-28 01:00:52] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -88.071622-0.005902j
[2025-08-28 01:01:01] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -88.179766+0.002179j
[2025-08-28 01:01:10] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -88.209320-0.003673j
[2025-08-28 01:01:19] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -88.087581+0.007090j
[2025-08-28 01:01:29] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -88.117765+0.003980j
[2025-08-28 01:01:38] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -88.086646+0.001855j
[2025-08-28 01:01:47] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -88.000352+0.004921j
[2025-08-28 01:01:47] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-28 01:01:56] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -88.133902-0.007308j
[2025-08-28 01:02:06] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -88.190676+0.000601j
[2025-08-28 01:02:15] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -88.322589-0.002961j
[2025-08-28 01:02:24] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -88.171930+0.004138j
[2025-08-28 01:02:33] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -88.286769+0.000470j
[2025-08-28 01:02:42] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -88.305279-0.004492j
[2025-08-28 01:02:52] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -88.118101+0.006744j
[2025-08-28 01:03:01] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -88.199154-0.005360j
[2025-08-28 01:03:10] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -88.082913+0.004060j
[2025-08-28 01:03:19] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -88.096176-0.005335j
[2025-08-28 01:03:28] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -88.122053+0.004965j
[2025-08-28 01:03:38] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -88.042564-0.007997j
[2025-08-28 01:03:47] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -88.100513-0.002544j
[2025-08-28 01:03:56] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -88.224485+0.006569j
[2025-08-28 01:04:05] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -88.111288-0.002935j
[2025-08-28 01:04:15] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -88.009204+0.003877j
[2025-08-28 01:04:24] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -88.074028-0.002352j
[2025-08-28 01:04:33] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -87.998599-0.004758j
[2025-08-28 01:04:42] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -88.039614+0.006910j
[2025-08-28 01:04:51] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -87.955848-0.008933j
[2025-08-28 01:05:01] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -88.121810+0.004232j
[2025-08-28 01:05:10] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -87.966167+0.008683j
[2025-08-28 01:05:19] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -88.022483+0.006615j
[2025-08-28 01:05:28] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -88.019923+0.006818j
[2025-08-28 01:05:37] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -88.174462-0.001026j
[2025-08-28 01:05:47] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -87.936185-0.004569j
[2025-08-28 01:05:56] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -87.982048-0.005799j
[2025-08-28 01:06:05] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -88.103365-0.002831j
[2025-08-28 01:06:14] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -88.078804+0.004808j
[2025-08-28 01:06:23] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -87.998640+0.001667j
[2025-08-28 01:06:33] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -87.975228+0.001908j
[2025-08-28 01:06:42] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -88.096101-0.007587j
[2025-08-28 01:06:51] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -88.205342-0.001777j
[2025-08-28 01:07:00] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -88.301798-0.000769j
[2025-08-28 01:07:10] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -88.139326+0.006855j
[2025-08-28 01:07:19] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -88.163508-0.008301j
[2025-08-28 01:07:28] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -88.158159-0.002216j
[2025-08-28 01:07:37] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -88.125002-0.000347j
[2025-08-28 01:07:46] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -88.105408-0.006976j
[2025-08-28 01:07:56] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -88.003510-0.000905j
[2025-08-28 01:08:05] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -88.111363-0.003816j
[2025-08-28 01:08:14] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -88.000539+0.007904j
[2025-08-28 01:08:23] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -87.997328-0.008911j
[2025-08-28 01:08:32] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -88.045433-0.002164j
[2025-08-28 01:08:42] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -88.039409-0.002059j
[2025-08-28 01:08:51] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -88.115362+0.002193j
[2025-08-28 01:09:00] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -88.160573+0.002719j
[2025-08-28 01:09:09] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -88.121448+0.002242j
[2025-08-28 01:09:18] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -88.139197-0.000273j
[2025-08-28 01:09:28] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -88.132226-0.003757j
[2025-08-28 01:09:28] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-28 01:09:37] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -88.164110+0.001881j
[2025-08-28 01:09:46] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -88.185659+0.001771j
[2025-08-28 01:09:55] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -88.078626-0.002848j
[2025-08-28 01:10:05] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -88.116253-0.003432j
[2025-08-28 01:10:14] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -88.191793+0.003009j
[2025-08-28 01:10:23] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -88.270632-0.000578j
[2025-08-28 01:10:32] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -88.120716+0.001540j
[2025-08-28 01:10:41] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -88.347962-0.006974j
[2025-08-28 01:10:51] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -88.195964-0.002104j
[2025-08-28 01:11:00] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -88.038170+0.003935j
[2025-08-28 01:11:09] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -87.962954-0.000007j
[2025-08-28 01:11:18] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -88.008591+0.001029j
[2025-08-28 01:11:27] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -88.147167-0.004377j
[2025-08-28 01:11:37] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -88.085236+0.000928j
[2025-08-28 01:11:46] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -88.066569-0.001397j
[2025-08-28 01:11:55] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -88.278208+0.001352j
[2025-08-28 01:12:04] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -87.901987-0.001975j
[2025-08-28 01:12:14] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -87.892656-0.001905j
[2025-08-28 01:12:23] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -88.026108-0.005568j
[2025-08-28 01:12:32] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -88.187519+0.001436j
[2025-08-28 01:12:41] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -88.000656+0.000244j
[2025-08-28 01:12:50] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -88.153327+0.006260j
[2025-08-28 01:13:00] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -88.208519+0.002579j
[2025-08-28 01:13:09] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -88.203327-0.002777j
[2025-08-28 01:13:18] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -88.172918-0.004736j
[2025-08-28 01:13:27] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -88.196611+0.003080j
[2025-08-28 01:13:36] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -87.953574-0.001322j
[2025-08-28 01:13:46] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -88.075548-0.008178j
[2025-08-28 01:13:55] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -87.994213-0.003513j
[2025-08-28 01:14:04] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -88.133550+0.000904j
[2025-08-28 01:14:13] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -88.143133-0.004623j
[2025-08-28 01:14:22] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -88.238397+0.008261j
[2025-08-28 01:14:32] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -88.124591+0.006828j
[2025-08-28 01:14:41] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -88.166731-0.008062j
[2025-08-28 01:14:50] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -88.219360-0.004570j
[2025-08-28 01:14:59] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -88.117652-0.003169j
[2025-08-28 01:15:08] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -88.314150-0.002262j
[2025-08-28 01:15:18] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -88.176017-0.004320j
[2025-08-28 01:15:27] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -88.314921-0.000518j
[2025-08-28 01:15:36] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -87.974435+0.003463j
[2025-08-28 01:15:45] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -87.998088-0.010092j
[2025-08-28 01:15:55] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -88.170029+0.007955j
[2025-08-28 01:16:04] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -88.123860+0.003491j
[2025-08-28 01:16:13] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -88.057759+0.002547j
[2025-08-28 01:16:22] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -88.198584+0.004880j
[2025-08-28 01:16:31] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -88.102587+0.001111j
[2025-08-28 01:16:41] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -88.133917+0.002066j
[2025-08-28 01:16:50] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -88.062664-0.003984j
[2025-08-28 01:16:59] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -88.101559+0.007615j
[2025-08-28 01:17:08] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -88.196751+0.010115j
[2025-08-28 01:17:08] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-28 01:17:18] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -88.145852+0.000369j
[2025-08-28 01:17:27] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -87.971256-0.001671j
[2025-08-28 01:17:36] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -88.014576-0.009296j
[2025-08-28 01:17:45] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -88.014774+0.002468j
[2025-08-28 01:17:54] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -88.054309-0.005665j
[2025-08-28 01:18:04] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -88.034540-0.004563j
[2025-08-28 01:18:13] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -87.919425-0.009199j
[2025-08-28 01:18:22] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -87.991647-0.000257j
[2025-08-28 01:18:31] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -88.096497-0.004631j
[2025-08-28 01:18:40] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -88.231391-0.003879j
[2025-08-28 01:18:50] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -88.139232-0.001588j
[2025-08-28 01:18:59] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -88.147260+0.002265j
[2025-08-28 01:19:08] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -88.218065-0.007713j
[2025-08-28 01:19:17] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -88.131167+0.001496j
[2025-08-28 01:19:27] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -88.085618-0.006955j
[2025-08-28 01:19:36] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -88.011532-0.000169j
[2025-08-28 01:19:45] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -88.042186-0.001394j
[2025-08-28 01:19:54] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -88.091447+0.001656j
[2025-08-28 01:20:03] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -88.159027-0.008538j
[2025-08-28 01:20:13] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -88.029298-0.007248j
[2025-08-28 01:20:22] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -88.070070-0.000239j
[2025-08-28 01:20:31] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -88.112192+0.004121j
[2025-08-28 01:20:40] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -88.035362-0.002172j
[2025-08-28 01:20:49] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -88.129628+0.002912j
[2025-08-28 01:20:59] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -88.084403+0.001826j
[2025-08-28 01:21:08] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -88.024452-0.001953j
[2025-08-28 01:21:17] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -88.083128+0.003617j
[2025-08-28 01:21:26] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -88.026265+0.003816j
[2025-08-28 01:21:36] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -88.060890-0.008405j
[2025-08-28 01:21:45] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -88.136322-0.002202j
[2025-08-28 01:21:54] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -88.091824+0.002808j
[2025-08-28 01:22:03] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -87.957513-0.003361j
[2025-08-28 01:22:12] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -88.110385-0.005159j
[2025-08-28 01:22:22] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -88.089908+0.004528j
[2025-08-28 01:22:31] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -88.032523-0.007876j
[2025-08-28 01:22:40] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -87.935228-0.000521j
[2025-08-28 01:22:49] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -87.921006+0.000598j
[2025-08-28 01:22:58] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -87.948161-0.006308j
[2025-08-28 01:23:08] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -88.017049+0.005065j
[2025-08-28 01:23:17] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -88.061623-0.001079j
[2025-08-28 01:23:26] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -88.092336-0.005147j
[2025-08-28 01:23:35] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -87.993237-0.001486j
[2025-08-28 01:23:45] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -88.029420-0.001730j
[2025-08-28 01:23:54] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -87.818033+0.004265j
[2025-08-28 01:24:03] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -87.872350+0.000895j
[2025-08-28 01:24:12] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -87.981580+0.006304j
[2025-08-28 01:24:21] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -87.979510-0.006143j
[2025-08-28 01:24:31] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -87.946462+0.000414j
[2025-08-28 01:24:40] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -88.007407-0.004720j
[2025-08-28 01:24:49] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -87.869722-0.004811j
[2025-08-28 01:24:49] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-28 01:24:58] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -87.978417-0.001846j
[2025-08-28 01:25:08] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -88.072819-0.000420j
[2025-08-28 01:25:17] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -87.982748-0.002660j
[2025-08-28 01:25:26] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -88.021341-0.010051j
[2025-08-28 01:25:35] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -88.130035+0.001145j
[2025-08-28 01:25:44] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -88.197884-0.000056j
[2025-08-28 01:25:54] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -88.262220+0.009296j
[2025-08-28 01:26:03] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -88.042184-0.000209j
[2025-08-28 01:26:12] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -88.163545+0.002819j
[2025-08-28 01:26:21] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -88.115115-0.001294j
[2025-08-28 01:26:31] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -88.050392-0.002890j
[2025-08-28 01:26:40] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -88.088682-0.003555j
[2025-08-28 01:26:49] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -88.145508-0.000566j
[2025-08-28 01:26:58] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -88.104426+0.001771j
[2025-08-28 01:27:07] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -88.106579-0.002109j
[2025-08-28 01:27:17] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -88.178625-0.007386j
[2025-08-28 01:27:26] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -88.131311+0.000912j
[2025-08-28 01:27:35] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -88.159224+0.000255j
[2025-08-28 01:27:44] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -88.195339-0.002418j
[2025-08-28 01:27:53] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -88.088755-0.002795j
[2025-08-28 01:28:03] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -88.051867+0.001041j
[2025-08-28 01:28:12] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -88.057273+0.003396j
[2025-08-28 01:28:21] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -88.177039-0.000046j
[2025-08-28 01:28:30] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -88.260478+0.003219j
[2025-08-28 01:28:39] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -88.208437-0.000922j
[2025-08-28 01:28:49] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -88.131782+0.004687j
[2025-08-28 01:28:58] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -88.216886-0.002761j
[2025-08-28 01:29:07] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -88.421106-0.005195j
[2025-08-28 01:29:16] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -88.321617+0.004153j
[2025-08-28 01:29:26] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -88.238677+0.002207j
[2025-08-28 01:29:35] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -88.255674-0.001824j
[2025-08-28 01:29:44] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -88.252006+0.004827j
[2025-08-28 01:29:53] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -88.144389-0.006843j
[2025-08-28 01:30:02] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -88.168551-0.000533j
[2025-08-28 01:30:12] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -88.097903-0.005424j
[2025-08-28 01:30:21] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -88.175708+0.003709j
[2025-08-28 01:30:30] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -88.175169+0.008484j
[2025-08-28 01:30:39] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -87.977585-0.004405j
[2025-08-28 01:30:48] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -87.933369-0.003958j
[2025-08-28 01:30:58] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -87.964040+0.003811j
[2025-08-28 01:31:07] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -87.872060-0.004103j
[2025-08-28 01:31:16] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -88.007574-0.007478j
[2025-08-28 01:31:25] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -88.155102-0.005364j
[2025-08-28 01:31:35] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -88.177179-0.010122j
[2025-08-28 01:31:44] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -88.163313-0.002473j
[2025-08-28 01:31:53] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -88.137559-0.001718j
[2025-08-28 01:32:02] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -87.993117-0.008177j
[2025-08-28 01:32:11] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -88.054385+0.004930j
[2025-08-28 01:32:21] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -87.892603-0.004941j
[2025-08-28 01:32:30] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -87.902256-0.001437j
[2025-08-28 01:32:30] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-28 01:32:30] ✅ Training completed | Restarts: 1
[2025-08-28 01:32:30] ============================================================
[2025-08-28 01:32:30] Training completed | Runtime: 4196.6s
[2025-08-28 01:32:34] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-28 01:32:34] ============================================================
