[2025-08-27 22:13:14] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.07/training/checkpoints/final_GCNN.pkl
[2025-08-27 22:13:14]   - 迭代次数: final
[2025-08-27 22:13:14]   - 能量: -86.692187+0.002484j ± 0.105381
[2025-08-27 22:13:14]   - 时间戳: 2025-08-27T22:13:04.561357+08:00
[2025-08-27 22:13:24] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 22:13:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 22:13:24] ==================================================
[2025-08-27 22:13:24] GCNN for Shastry-Sutherland Model
[2025-08-27 22:13:24] ==================================================
[2025-08-27 22:13:24] System parameters:
[2025-08-27 22:13:24]   - System size: L=5, N=100
[2025-08-27 22:13:24]   - System parameters: J1=0.08, J2=0.0, Q=1.0
[2025-08-27 22:13:25] --------------------------------------------------
[2025-08-27 22:13:25] Model parameters:
[2025-08-27 22:13:25]   - Number of layers = 4
[2025-08-27 22:13:25]   - Number of features = 4
[2025-08-27 22:13:25]   - Total parameters = 19628
[2025-08-27 22:13:25] --------------------------------------------------
[2025-08-27 22:13:25] Training parameters:
[2025-08-27 22:13:25]   - Learning rate: 0.015
[2025-08-27 22:13:25]   - Total iterations: 450
[2025-08-27 22:13:25]   - Annealing cycles: 2
[2025-08-27 22:13:25]   - Initial period: 150
[2025-08-27 22:13:25]   - Period multiplier: 2.0
[2025-08-27 22:13:25]   - Temperature range: 0.0-1.0
[2025-08-27 22:13:25]   - Samples: 4096
[2025-08-27 22:13:25]   - Discarded samples: 0
[2025-08-27 22:13:25]   - Chunk size: 2048
[2025-08-27 22:13:25]   - Diagonal shift: 0.2
[2025-08-27 22:13:25]   - Gradient clipping: 1.0
[2025-08-27 22:13:25]   - Checkpoint enabled: interval=50
[2025-08-27 22:13:25]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.08/training/checkpoints
[2025-08-27 22:13:25] --------------------------------------------------
[2025-08-27 22:13:25] Device status:
[2025-08-27 22:13:25]   - Devices model: NVIDIA H200 NVL
[2025-08-27 22:13:25]   - Number of devices: 1
[2025-08-27 22:13:25]   - Sharding: True
[2025-08-27 22:13:25] ============================================================
[2025-08-27 22:14:07] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -87.568928-0.007003j
[2025-08-27 22:14:34] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -87.493770+0.003461j
[2025-08-27 22:14:52] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -87.551349+0.006075j
[2025-08-27 22:15:12] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -87.482569-0.003000j
[2025-08-27 22:15:33] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -87.358728-0.002336j
[2025-08-27 22:15:54] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -87.443321-0.003518j
[2025-08-27 22:16:15] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -87.371300-0.004473j
[2025-08-27 22:16:35] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -87.363446+0.000197j
[2025-08-27 22:16:56] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -87.479912+0.000355j
[2025-08-27 22:17:17] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -87.404886+0.005349j
[2025-08-27 22:17:38] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -87.559568+0.007945j
[2025-08-27 22:17:58] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -87.454692+0.002346j
[2025-08-27 22:18:19] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -87.476691-0.005407j
[2025-08-27 22:18:40] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -87.482938-0.002989j
[2025-08-27 22:19:01] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -87.422300-0.000647j
[2025-08-27 22:19:21] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -87.603763-0.003827j
[2025-08-27 22:19:42] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -87.414602-0.002691j
[2025-08-27 22:20:03] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -87.289488+0.004727j
[2025-08-27 22:20:23] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -87.407768+0.001756j
[2025-08-27 22:20:44] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -87.532783+0.004214j
[2025-08-27 22:21:05] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -87.495898+0.001703j
[2025-08-27 22:21:26] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -87.389735-0.001665j
[2025-08-27 22:21:46] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -87.486330-0.010519j
[2025-08-27 22:22:07] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -87.687003+0.004078j
[2025-08-27 22:22:28] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -87.532038+0.000435j
[2025-08-27 22:22:49] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -87.401951+0.003270j
[2025-08-27 22:23:09] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -87.571132-0.001498j
[2025-08-27 22:23:30] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -87.498383+0.003480j
[2025-08-27 22:23:51] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -87.501589-0.006238j
[2025-08-27 22:24:12] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -87.452326-0.008573j
[2025-08-27 22:24:32] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -87.623357+0.005064j
[2025-08-27 22:24:53] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -87.649802-0.001578j
[2025-08-27 22:25:14] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -87.503907-0.004083j
[2025-08-27 22:25:35] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -87.623172-0.004707j
[2025-08-27 22:25:55] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -87.793082-0.002583j
[2025-08-27 22:26:16] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -87.592693+0.002615j
[2025-08-27 22:26:37] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -87.672916-0.001671j
[2025-08-27 22:26:57] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -87.550074+0.008296j
[2025-08-27 22:27:18] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -87.527727+0.003319j
[2025-08-27 22:27:38] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -87.551934-0.002631j
[2025-08-27 22:27:59] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -87.602886+0.005644j
[2025-08-27 22:28:20] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -87.622575+0.006256j
[2025-08-27 22:28:40] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -87.612727+0.000895j
[2025-08-27 22:29:01] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -87.650197+0.004552j
[2025-08-27 22:29:22] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -87.629688-0.002706j
[2025-08-27 22:29:42] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -87.485260+0.010950j
[2025-08-27 22:30:03] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -87.671050-0.001532j
[2025-08-27 22:30:24] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -87.644657-0.006970j
[2025-08-27 22:30:45] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -87.655260-0.004238j
[2025-08-27 22:31:06] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -87.579767-0.002450j
[2025-08-27 22:31:06] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 22:31:26] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -87.431474+0.000989j
[2025-08-27 22:31:47] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -87.550182-0.002151j
[2025-08-27 22:32:08] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -87.460546-0.004445j
[2025-08-27 22:32:29] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -87.481696-0.004363j
[2025-08-27 22:32:49] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -87.327123-0.009803j
[2025-08-27 22:33:10] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -87.265526+0.001130j
[2025-08-27 22:33:31] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -87.284510+0.005050j
[2025-08-27 22:33:52] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -87.252015+0.004519j
[2025-08-27 22:34:12] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -87.195310-0.001523j
[2025-08-27 22:34:33] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -87.288693+0.005945j
[2025-08-27 22:34:54] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -87.312779+0.007508j
[2025-08-27 22:35:15] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -87.414683+0.007613j
[2025-08-27 22:35:35] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -87.407720-0.004875j
[2025-08-27 22:35:56] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -87.481418+0.001812j
[2025-08-27 22:36:17] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -87.538153+0.000270j
[2025-08-27 22:36:38] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -87.501729+0.005042j
[2025-08-27 22:36:58] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -87.420387-0.004879j
[2025-08-27 22:37:19] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -87.490830+0.002121j
[2025-08-27 22:37:40] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -87.374684+0.000515j
[2025-08-27 22:38:00] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -87.346641+0.003446j
[2025-08-27 22:38:21] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -87.355862+0.001701j
[2025-08-27 22:38:42] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -87.384951+0.001783j
[2025-08-27 22:39:03] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -87.351964+0.006444j
[2025-08-27 22:39:23] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -87.355725+0.003678j
[2025-08-27 22:39:44] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -87.403231-0.001961j
[2025-08-27 22:40:05] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -87.441032-0.000938j
[2025-08-27 22:40:26] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -87.445606-0.001177j
[2025-08-27 22:40:46] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -87.343732+0.000296j
[2025-08-27 22:41:07] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -87.404134-0.005663j
[2025-08-27 22:41:27] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -87.399275-0.004071j
[2025-08-27 22:41:48] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -87.473142-0.002859j
[2025-08-27 22:42:09] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -87.450208-0.004596j
[2025-08-27 22:42:29] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -87.485923-0.000447j
[2025-08-27 22:42:50] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -87.486757-0.014145j
[2025-08-27 22:43:11] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -87.496112-0.002896j
[2025-08-27 22:43:32] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -87.530390-0.003423j
[2025-08-27 22:43:52] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -87.519065+0.000360j
[2025-08-27 22:44:13] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -87.592932-0.006835j
[2025-08-27 22:44:34] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -87.562094-0.007106j
[2025-08-27 22:44:54] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -87.321669+0.002169j
[2025-08-27 22:45:15] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -87.383861-0.010617j
[2025-08-27 22:45:36] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -87.393871+0.000999j
[2025-08-27 22:45:57] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -87.444632-0.005051j
[2025-08-27 22:46:17] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -87.494390-0.001608j
[2025-08-27 22:46:38] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -87.543001-0.002401j
[2025-08-27 22:46:59] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -87.663029-0.003504j
[2025-08-27 22:47:20] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -87.527741+0.004836j
[2025-08-27 22:47:40] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -87.463636-0.000216j
[2025-08-27 22:48:01] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -87.428045+0.000741j
[2025-08-27 22:48:22] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -87.440746-0.013044j
[2025-08-27 22:48:22] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 22:48:43] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -87.422202+0.000364j
[2025-08-27 22:49:04] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -87.402585+0.004263j
[2025-08-27 22:49:24] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -87.558131-0.000367j
[2025-08-27 22:49:45] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -87.432191-0.000933j
[2025-08-27 22:50:06] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -87.598536-0.010049j
[2025-08-27 22:50:26] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -87.707612+0.001404j
[2025-08-27 22:50:47] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -87.590689-0.005378j
[2025-08-27 22:51:08] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -87.541440+0.003808j
[2025-08-27 22:51:29] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -87.560226-0.005712j
[2025-08-27 22:51:49] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -87.474851+0.007091j
[2025-08-27 22:52:10] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -87.467226+0.000477j
[2025-08-27 22:52:31] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -87.587664+0.010362j
[2025-08-27 22:52:52] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -87.522313+0.012805j
[2025-08-27 22:53:12] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -87.489080+0.000210j
[2025-08-27 22:53:33] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -87.509547+0.008069j
[2025-08-27 22:53:54] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -87.270014-0.002970j
[2025-08-27 22:54:15] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -87.345536+0.004920j
[2025-08-27 22:54:35] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -87.537819-0.002428j
[2025-08-27 22:54:56] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -87.433276-0.000228j
[2025-08-27 22:55:17] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -87.234445+0.000169j
[2025-08-27 22:55:38] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -87.310296+0.000598j
[2025-08-27 22:55:58] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -87.311629-0.002529j
[2025-08-27 22:56:19] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -87.332013-0.004076j
[2025-08-27 22:56:40] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -87.394335-0.001609j
[2025-08-27 22:57:00] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -87.258408+0.001056j
[2025-08-27 22:57:21] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -87.424640-0.009320j
[2025-08-27 22:57:42] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -87.377343-0.002749j
[2025-08-27 22:58:03] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -87.616506+0.003307j
[2025-08-27 22:58:23] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -87.563703-0.008079j
[2025-08-27 22:58:44] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -87.552022-0.004011j
[2025-08-27 22:59:05] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -87.576602+0.002594j
[2025-08-27 22:59:26] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -87.611247-0.003087j
[2025-08-27 22:59:46] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -87.587754-0.000901j
[2025-08-27 23:00:07] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -87.569391+0.007907j
[2025-08-27 23:00:28] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -87.618926-0.001359j
[2025-08-27 23:00:49] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -87.417352-0.008326j
[2025-08-27 23:01:09] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -87.479834+0.010906j
[2025-08-27 23:01:30] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -87.429868+0.005423j
[2025-08-27 23:01:51] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -87.446034+0.006169j
[2025-08-27 23:02:11] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -87.423738-0.002751j
[2025-08-27 23:02:32] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -87.484267+0.000594j
[2025-08-27 23:02:53] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -87.551895+0.009250j
[2025-08-27 23:03:14] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -87.744588-0.005479j
[2025-08-27 23:03:34] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -87.617397-0.003166j
[2025-08-27 23:03:55] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -87.581828-0.001394j
[2025-08-27 23:04:16] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -87.545541-0.003787j
[2025-08-27 23:04:37] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -87.371658-0.001970j
[2025-08-27 23:04:57] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -87.289833+0.004140j
[2025-08-27 23:05:18] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -87.191135+0.002515j
[2025-08-27 23:05:39] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -87.360716-0.006209j
[2025-08-27 23:05:39] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 23:05:39] RESTART #1 | Period: 300
[2025-08-27 23:06:00] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -87.287442+0.004140j
[2025-08-27 23:06:20] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -87.488674+0.001328j
[2025-08-27 23:06:41] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -87.178299-0.013261j
[2025-08-27 23:07:02] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -87.431251+0.006105j
[2025-08-27 23:07:23] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -87.565757-0.004279j
[2025-08-27 23:07:43] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -87.488897-0.000917j
[2025-08-27 23:08:04] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -87.590268-0.001715j
[2025-08-27 23:08:25] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -87.470431-0.001908j
[2025-08-27 23:08:46] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -87.418254+0.001236j
[2025-08-27 23:09:07] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -87.489493-0.001341j
[2025-08-27 23:09:27] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -87.325089+0.002171j
[2025-08-27 23:09:48] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -87.379041+0.002411j
[2025-08-27 23:10:09] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -87.332856+0.003627j
[2025-08-27 23:10:29] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -87.362192+0.000219j
[2025-08-27 23:10:50] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -87.338053-0.000573j
[2025-08-27 23:11:11] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -87.351189-0.003381j
[2025-08-27 23:11:32] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -87.446034-0.005059j
[2025-08-27 23:11:52] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -87.498073+0.003276j
[2025-08-27 23:12:13] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -87.551626-0.007679j
[2025-08-27 23:12:34] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -87.338220+0.004757j
[2025-08-27 23:12:54] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -87.444968-0.007649j
[2025-08-27 23:13:15] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -87.657480+0.000261j
[2025-08-27 23:13:36] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -87.607088-0.006271j
[2025-08-27 23:13:57] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -87.543597+0.002606j
[2025-08-27 23:14:17] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -87.496785-0.001720j
[2025-08-27 23:14:38] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -87.597595-0.002690j
[2025-08-27 23:14:59] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -87.586049-0.001035j
[2025-08-27 23:15:20] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -87.548755+0.001889j
[2025-08-27 23:15:40] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -87.344269+0.001192j
[2025-08-27 23:16:01] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -87.458072-0.003471j
[2025-08-27 23:16:22] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -87.452337-0.007684j
[2025-08-27 23:16:43] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -87.353112-0.005434j
[2025-08-27 23:17:03] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -87.252432-0.003573j
[2025-08-27 23:17:24] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -87.362334+0.005663j
[2025-08-27 23:17:45] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -87.499626+0.000764j
[2025-08-27 23:18:06] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -87.443264+0.009317j
[2025-08-27 23:18:26] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -87.462438+0.000674j
[2025-08-27 23:18:47] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -87.372140-0.000778j
[2025-08-27 23:19:08] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -87.378148+0.003361j
[2025-08-27 23:19:29] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -87.295332-0.002576j
[2025-08-27 23:19:49] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -87.219011-0.000889j
[2025-08-27 23:20:10] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -87.197555+0.000250j
[2025-08-27 23:20:31] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -87.477016-0.007581j
[2025-08-27 23:20:52] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -87.562328-0.002907j
[2025-08-27 23:21:12] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -87.621612+0.000498j
[2025-08-27 23:21:33] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -87.416384-0.002212j
[2025-08-27 23:21:54] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -87.489796-0.004871j
[2025-08-27 23:22:15] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -87.416767+0.000358j
[2025-08-27 23:22:35] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -87.531172+0.002826j
[2025-08-27 23:22:56] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -87.572663+0.003841j
[2025-08-27 23:22:56] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 23:23:17] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -87.466527-0.003202j
[2025-08-27 23:23:38] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -87.573038+0.008779j
[2025-08-27 23:23:58] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -87.430507+0.002136j
[2025-08-27 23:24:19] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -87.476007-0.001208j
[2025-08-27 23:24:40] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -87.530993-0.003384j
[2025-08-27 23:25:01] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -87.381429-0.001694j
[2025-08-27 23:25:21] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -87.593556-0.004454j
[2025-08-27 23:25:42] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -87.445426-0.005388j
[2025-08-27 23:26:03] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -87.466122+0.003056j
[2025-08-27 23:26:24] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -87.567279-0.005789j
[2025-08-27 23:26:44] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -87.368625+0.006985j
[2025-08-27 23:27:05] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -87.435786-0.002845j
[2025-08-27 23:27:26] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -87.361552+0.010176j
[2025-08-27 23:27:46] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -87.346582+0.006973j
[2025-08-27 23:28:07] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -87.403106+0.000635j
[2025-08-27 23:28:28] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -87.370710+0.010512j
[2025-08-27 23:28:49] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -87.397836+0.005944j
[2025-08-27 23:29:09] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -87.589910-0.006269j
[2025-08-27 23:29:30] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -87.415890+0.001824j
[2025-08-27 23:29:51] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -87.547956-0.001039j
[2025-08-27 23:30:12] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -87.639960-0.004642j
[2025-08-27 23:30:32] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -87.680542+0.003099j
[2025-08-27 23:30:53] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -87.617351+0.005964j
[2025-08-27 23:31:14] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -87.492712-0.007223j
[2025-08-27 23:31:35] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -87.483045+0.008403j
[2025-08-27 23:31:55] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -87.488229+0.002557j
[2025-08-27 23:32:16] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -87.378364+0.002034j
[2025-08-27 23:32:37] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -87.310089-0.000771j
[2025-08-27 23:32:58] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -87.371689+0.008640j
[2025-08-27 23:33:18] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -87.430473-0.007421j
[2025-08-27 23:33:39] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -87.490041+0.004291j
[2025-08-27 23:34:00] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -87.342972+0.000835j
[2025-08-27 23:34:20] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -87.284902+0.002792j
[2025-08-27 23:34:41] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -87.174517+0.000373j
[2025-08-27 23:35:02] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -87.320967-0.000653j
[2025-08-27 23:35:23] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -87.424558+0.003089j
[2025-08-27 23:35:43] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -87.301570+0.001107j
[2025-08-27 23:36:04] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -87.421041-0.004482j
[2025-08-27 23:36:24] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -87.504286+0.005434j
[2025-08-27 23:36:45] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -87.454055-0.001601j
[2025-08-27 23:37:06] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -87.413813-0.002924j
[2025-08-27 23:37:26] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -87.476472+0.004167j
[2025-08-27 23:37:47] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -87.517566+0.004684j
[2025-08-27 23:38:08] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -87.575991+0.008993j
[2025-08-27 23:38:29] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -87.429034-0.004017j
[2025-08-27 23:38:49] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -87.422022+0.006787j
[2025-08-27 23:39:10] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -87.522649-0.000710j
[2025-08-27 23:39:31] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -87.333212-0.003838j
[2025-08-27 23:39:52] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -87.494464-0.000055j
[2025-08-27 23:40:12] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -87.475788+0.001922j
[2025-08-27 23:40:13] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 23:40:33] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -87.450269+0.002392j
[2025-08-27 23:40:54] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -87.436616+0.007301j
[2025-08-27 23:41:15] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -87.492044-0.007700j
[2025-08-27 23:41:36] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -87.485282-0.003129j
[2025-08-27 23:41:56] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -87.425015+0.002595j
[2025-08-27 23:42:17] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -87.479450-0.000497j
[2025-08-27 23:42:38] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -87.365705+0.006834j
[2025-08-27 23:42:59] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -87.373036+0.000900j
[2025-08-27 23:43:19] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -87.579287+0.000883j
[2025-08-27 23:43:40] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -87.442374-0.000835j
[2025-08-27 23:44:01] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -87.531280-0.001308j
[2025-08-27 23:44:22] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -87.601896-0.004080j
[2025-08-27 23:44:42] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -87.536561+0.000631j
[2025-08-27 23:45:03] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -87.499125+0.003311j
[2025-08-27 23:45:24] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -87.596507+0.001519j
[2025-08-27 23:45:44] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -87.564112-0.001776j
[2025-08-27 23:46:05] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -87.535396-0.001862j
[2025-08-27 23:46:26] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -87.635682+0.008043j
[2025-08-27 23:46:47] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -87.585290-0.000265j
[2025-08-27 23:47:07] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -87.514580-0.006421j
[2025-08-27 23:47:28] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -87.585845-0.003866j
[2025-08-27 23:47:49] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -87.407518-0.003362j
[2025-08-27 23:48:09] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -87.411321-0.001694j
[2025-08-27 23:48:30] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -87.434734-0.004047j
[2025-08-27 23:48:51] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -87.322146-0.000324j
[2025-08-27 23:49:12] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -87.413101-0.010837j
[2025-08-27 23:49:32] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -87.529123-0.001078j
[2025-08-27 23:49:52] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -87.549658+0.000423j
[2025-08-27 23:50:13] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -87.498303-0.001620j
[2025-08-27 23:50:34] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -87.454780+0.002482j
[2025-08-27 23:50:54] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -87.576382+0.000637j
[2025-08-27 23:51:15] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -87.606943+0.006350j
[2025-08-27 23:51:36] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -87.710628+0.003368j
[2025-08-27 23:51:57] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -87.407675-0.005719j
[2025-08-27 23:52:17] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -87.635880+0.005991j
[2025-08-27 23:52:38] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -87.477090+0.005718j
[2025-08-27 23:52:59] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -87.530350-0.000111j
[2025-08-27 23:53:20] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -87.434516-0.006158j
[2025-08-27 23:53:40] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -87.482934+0.004175j
[2025-08-27 23:54:01] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -87.490842-0.000290j
[2025-08-27 23:54:22] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -87.343730+0.007710j
[2025-08-27 23:54:43] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -87.486609-0.010328j
[2025-08-27 23:55:03] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -87.486235-0.003867j
[2025-08-27 23:55:24] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -87.517693+0.001984j
[2025-08-27 23:55:45] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -87.549759+0.007358j
[2025-08-27 23:56:06] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -87.443222-0.005743j
[2025-08-27 23:56:26] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -87.416080-0.000730j
[2025-08-27 23:56:47] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -87.390195-0.003709j
[2025-08-27 23:57:08] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -87.450021+0.000113j
[2025-08-27 23:57:28] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -87.263707-0.005204j
[2025-08-27 23:57:29] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 23:57:50] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -87.356567+0.003518j
[2025-08-27 23:58:10] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -87.358987+0.006081j
[2025-08-27 23:58:31] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -87.553507+0.012503j
[2025-08-27 23:58:52] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -87.600233+0.009235j
[2025-08-27 23:59:13] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -87.677962+0.006210j
[2025-08-27 23:59:33] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -87.797048+0.003610j
[2025-08-27 23:59:54] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -87.763176+0.000109j
[2025-08-28 00:00:03] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -87.666012+0.009161j
[2025-08-28 00:00:12] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -87.669875+0.003977j
[2025-08-28 00:00:22] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -87.532777+0.003947j
[2025-08-28 00:00:31] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -87.415820+0.001752j
[2025-08-28 00:00:40] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -87.417997+0.007333j
[2025-08-28 00:00:50] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -87.595987-0.005798j
[2025-08-28 00:00:59] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -87.551542-0.003094j
[2025-08-28 00:01:08] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -87.419730-0.003247j
[2025-08-28 00:01:18] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -87.575297+0.000071j
[2025-08-28 00:01:27] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -87.403395+0.002480j
[2025-08-28 00:01:36] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -87.449088-0.007768j
[2025-08-28 00:01:46] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -87.349669-0.002295j
[2025-08-28 00:01:55] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -87.488865+0.001991j
[2025-08-28 00:02:04] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -87.484696-0.001079j
[2025-08-28 00:02:14] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -87.405565-0.006253j
[2025-08-28 00:02:23] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -87.334993+0.000647j
[2025-08-28 00:02:32] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -87.259291+0.002136j
[2025-08-28 00:02:41] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -87.423432+0.001490j
[2025-08-28 00:02:51] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -87.517931+0.005580j
[2025-08-28 00:03:00] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -87.477637+0.003490j
[2025-08-28 00:03:09] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -87.536516+0.000690j
[2025-08-28 00:03:19] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -87.521694-0.000446j
[2025-08-28 00:03:28] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -87.634789+0.002368j
[2025-08-28 00:03:37] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -87.574132+0.000854j
[2025-08-28 00:03:48] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -87.651492+0.003477j
[2025-08-28 00:03:57] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -87.468365-0.001973j
[2025-08-28 00:04:06] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -87.431740+0.000953j
[2025-08-28 00:04:15] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -87.386539-0.000795j
[2025-08-28 00:04:25] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -87.457792+0.000929j
[2025-08-28 00:04:34] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -87.521483+0.007445j
[2025-08-28 00:04:43] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -87.288869-0.000812j
[2025-08-28 00:04:53] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -87.486068-0.003754j
[2025-08-28 00:05:02] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -87.326546+0.000569j
[2025-08-28 00:05:11] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -87.351180+0.007032j
[2025-08-28 00:05:21] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -87.309112+0.002611j
[2025-08-28 00:05:30] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -87.337311-0.002577j
[2025-08-28 00:05:39] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -87.533137+0.008018j
[2025-08-28 00:05:49] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -87.612136-0.000746j
[2025-08-28 00:05:58] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -87.520420-0.007849j
[2025-08-28 00:06:07] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -87.497818+0.004437j
[2025-08-28 00:06:17] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -87.511702-0.004807j
[2025-08-28 00:06:26] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -87.460581-0.001599j
[2025-08-28 00:06:35] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -87.417781-0.002976j
[2025-08-28 00:06:35] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-28 00:06:45] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -87.364882+0.009863j
[2025-08-28 00:06:54] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -87.421001+0.004661j
[2025-08-28 00:07:03] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -87.402878-0.009240j
[2025-08-28 00:07:13] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -87.375515+0.005329j
[2025-08-28 00:07:22] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -87.605571-0.009238j
[2025-08-28 00:07:31] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -87.619945-0.003463j
[2025-08-28 00:07:41] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -87.635420-0.002209j
[2025-08-28 00:07:50] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -87.485573-0.002420j
[2025-08-28 00:07:59] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -87.584973-0.002861j
[2025-08-28 00:08:09] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -87.527709-0.010823j
[2025-08-28 00:08:18] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -87.643403-0.006426j
[2025-08-28 00:08:27] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -87.642558+0.004373j
[2025-08-28 00:08:37] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -87.569738+0.000989j
[2025-08-28 00:08:46] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -87.564934-0.000432j
[2025-08-28 00:08:55] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -87.480375+0.001921j
[2025-08-28 00:09:05] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -87.397044+0.000996j
[2025-08-28 00:09:14] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -87.444817+0.003519j
[2025-08-28 00:09:23] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -87.315963-0.009169j
[2025-08-28 00:09:33] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -87.511983-0.008914j
[2025-08-28 00:09:42] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -87.236389+0.005616j
[2025-08-28 00:09:51] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -87.355581+0.004465j
[2025-08-28 00:10:00] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -87.444505+0.005753j
[2025-08-28 00:10:10] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -87.441895+0.008340j
[2025-08-28 00:10:19] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -87.437995-0.001417j
[2025-08-28 00:10:28] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -87.428407-0.000983j
[2025-08-28 00:10:38] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -87.444261+0.007091j
[2025-08-28 00:10:47] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -87.591247+0.005229j
[2025-08-28 00:10:56] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -87.594624-0.001441j
[2025-08-28 00:11:06] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -87.631503+0.004826j
[2025-08-28 00:11:15] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -87.564338+0.003766j
[2025-08-28 00:11:24] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -87.570400+0.008454j
[2025-08-28 00:11:34] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -87.543350+0.004943j
[2025-08-28 00:11:43] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -87.370334-0.003898j
[2025-08-28 00:11:52] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -87.286839-0.003392j
[2025-08-28 00:12:02] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -87.259632-0.010227j
[2025-08-28 00:12:11] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -87.417898-0.008547j
[2025-08-28 00:12:20] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -87.357956-0.000809j
[2025-08-28 00:12:30] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -87.318005-0.003605j
[2025-08-28 00:12:39] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -87.459662-0.005553j
[2025-08-28 00:12:48] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -87.378853-0.002365j
[2025-08-28 00:12:58] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -87.260603-0.004122j
[2025-08-28 00:13:07] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -87.506632+0.000346j
[2025-08-28 00:13:16] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -87.488233+0.005085j
[2025-08-28 00:13:26] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -87.552068+0.000376j
[2025-08-28 00:13:35] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -87.376627+0.003181j
[2025-08-28 00:13:44] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -87.356391-0.002550j
[2025-08-28 00:13:53] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -87.378787+0.008457j
[2025-08-28 00:14:03] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -87.451438-0.002412j
[2025-08-28 00:14:12] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -87.504674+0.001249j
[2025-08-28 00:14:21] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -87.391624+0.007674j
[2025-08-28 00:14:22] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-28 00:14:31] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -87.401689+0.004206j
[2025-08-28 00:14:40] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -87.535899-0.003150j
[2025-08-28 00:14:50] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -87.391521-0.005227j
[2025-08-28 00:14:59] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -87.632531-0.000177j
[2025-08-28 00:15:08] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -87.509672-0.003387j
[2025-08-28 00:15:17] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -87.443550+0.002203j
[2025-08-28 00:15:27] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -87.480760+0.003080j
[2025-08-28 00:15:36] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -87.601053-0.006355j
[2025-08-28 00:15:45] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -87.691064+0.002351j
[2025-08-28 00:15:55] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -87.669578+0.000668j
[2025-08-28 00:16:04] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -87.552181+0.000250j
[2025-08-28 00:16:13] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -87.580123+0.001108j
[2025-08-28 00:16:23] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -87.358162+0.003247j
[2025-08-28 00:16:32] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -87.546399-0.005784j
[2025-08-28 00:16:41] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -87.514477-0.001441j
[2025-08-28 00:16:51] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -87.456531-0.003334j
[2025-08-28 00:17:00] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -87.522631+0.003152j
[2025-08-28 00:17:09] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -87.641834+0.000619j
[2025-08-28 00:17:19] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -87.434726-0.001709j
[2025-08-28 00:17:28] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -87.497659+0.005413j
[2025-08-28 00:17:37] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -87.329463+0.001106j
[2025-08-28 00:17:47] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -87.349866+0.002875j
[2025-08-28 00:17:56] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -87.377670+0.002823j
[2025-08-28 00:18:05] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -87.423166+0.007967j
[2025-08-28 00:18:15] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -87.458729+0.004646j
[2025-08-28 00:18:24] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -87.490448-0.001603j
[2025-08-28 00:18:33] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -87.421583+0.000225j
[2025-08-28 00:18:43] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -87.370812+0.000427j
[2025-08-28 00:18:52] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -87.343848+0.002268j
[2025-08-28 00:19:01] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -87.279759-0.001353j
[2025-08-28 00:19:11] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -87.347988+0.003405j
[2025-08-28 00:19:20] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -87.344408+0.005818j
[2025-08-28 00:19:29] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -87.508899+0.001793j
[2025-08-28 00:19:39] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -87.365334+0.002284j
[2025-08-28 00:19:48] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -87.394936-0.001673j
[2025-08-28 00:19:57] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -87.426442+0.008812j
[2025-08-28 00:20:07] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -87.397097-0.000855j
[2025-08-28 00:20:16] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -87.259126+0.001537j
[2025-08-28 00:20:25] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -87.499171-0.006048j
[2025-08-28 00:20:34] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -87.404933+0.010677j
[2025-08-28 00:20:44] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -87.335270-0.000546j
[2025-08-28 00:20:53] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -87.478971+0.003701j
[2025-08-28 00:21:02] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -87.488397+0.004438j
[2025-08-28 00:21:12] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -87.493812+0.000725j
[2025-08-28 00:21:21] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -87.540798+0.003294j
[2025-08-28 00:21:30] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -87.595728+0.003661j
[2025-08-28 00:21:40] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -87.423755-0.002122j
[2025-08-28 00:21:49] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -87.396905-0.004669j
[2025-08-28 00:21:58] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -87.300054+0.003974j
[2025-08-28 00:22:08] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -87.285128-0.008725j
[2025-08-28 00:22:08] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-28 00:22:08] ✅ Training completed | Restarts: 1
[2025-08-28 00:22:08] ============================================================
[2025-08-28 00:22:08] Training completed | Runtime: 7723.0s
[2025-08-28 00:22:12] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-28 00:22:12] ============================================================
