[2025-08-27 19:37:52] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-08-27 19:37:52]   - 迭代次数: final
[2025-08-27 19:37:52]   - 能量: -86.085151-0.002514j ± 0.107819
[2025-08-27 19:37:52]   - 时间戳: 2025-08-27T19:37:37.469672+08:00
[2025-08-27 19:38:02] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 19:38:02] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 19:38:02] ==================================================
[2025-08-27 19:38:02] GCNN for Shastry-Sutherland Model
[2025-08-27 19:38:02] ==================================================
[2025-08-27 19:38:02] System parameters:
[2025-08-27 19:38:02]   - System size: L=5, N=100
[2025-08-27 19:38:02]   - System parameters: J1=0.07, J2=0.0, Q=1.0
[2025-08-27 19:38:02] --------------------------------------------------
[2025-08-27 19:38:02] Model parameters:
[2025-08-27 19:38:02]   - Number of layers = 4
[2025-08-27 19:38:02]   - Number of features = 4
[2025-08-27 19:38:02]   - Total parameters = 19628
[2025-08-27 19:38:02] --------------------------------------------------
[2025-08-27 19:38:02] Training parameters:
[2025-08-27 19:38:02]   - Learning rate: 0.015
[2025-08-27 19:38:02]   - Total iterations: 450
[2025-08-27 19:38:02]   - Annealing cycles: 2
[2025-08-27 19:38:02]   - Initial period: 150
[2025-08-27 19:38:02]   - Period multiplier: 2.0
[2025-08-27 19:38:02]   - Temperature range: 0.0-1.0
[2025-08-27 19:38:02]   - Samples: 4096
[2025-08-27 19:38:02]   - Discarded samples: 0
[2025-08-27 19:38:02]   - Chunk size: 2048
[2025-08-27 19:38:02]   - Diagonal shift: 0.2
[2025-08-27 19:38:02]   - Gradient clipping: 1.0
[2025-08-27 19:38:02]   - Checkpoint enabled: interval=50
[2025-08-27 19:38:02]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.07/training/checkpoints
[2025-08-27 19:38:02] --------------------------------------------------
[2025-08-27 19:38:02] Device status:
[2025-08-27 19:38:02]   - Devices model: NVIDIA H200 NVL
[2025-08-27 19:38:02]   - Number of devices: 1
[2025-08-27 19:38:03]   - Sharding: True
[2025-08-27 19:38:03] ============================================================
[2025-08-27 19:38:53] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -87.061244-0.001745j
[2025-08-27 19:39:31] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -86.986937+0.004312j
[2025-08-27 19:39:52] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -86.896893-0.009478j
[2025-08-27 19:40:13] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -86.958894-0.005577j
[2025-08-27 19:40:33] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -86.979781+0.006531j
[2025-08-27 19:40:54] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -86.939035-0.005816j
[2025-08-27 19:41:15] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -86.833205+0.000574j
[2025-08-27 19:41:35] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -86.954251+0.008490j
[2025-08-27 19:41:57] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -86.895305+0.004691j
[2025-08-27 19:42:18] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -86.827622+0.005892j
[2025-08-27 19:42:38] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -86.849878-0.002684j
[2025-08-27 19:42:59] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -86.814353+0.007493j
[2025-08-27 19:43:19] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -86.853609+0.004751j
[2025-08-27 19:43:40] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -86.894483-0.002802j
[2025-08-27 19:44:01] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -86.880279-0.001806j
[2025-08-27 19:44:21] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -86.970793+0.003434j
[2025-08-27 19:44:42] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -86.914117+0.005242j
[2025-08-27 19:45:03] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -86.976730-0.000753j
[2025-08-27 19:45:23] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -87.039163+0.009626j
[2025-08-27 19:45:44] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -86.764013+0.000277j
[2025-08-27 19:46:05] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -86.758417-0.005392j
[2025-08-27 19:46:25] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -86.940665-0.013121j
[2025-08-27 19:46:46] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -86.835455+0.005746j
[2025-08-27 19:47:07] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -86.899619-0.012352j
[2025-08-27 19:47:27] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -86.765408-0.004352j
[2025-08-27 19:47:48] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -86.854929+0.002274j
[2025-08-27 19:48:09] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -87.015038-0.000365j
[2025-08-27 19:48:29] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -87.061878+0.006010j
[2025-08-27 19:48:50] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -86.894213+0.003888j
[2025-08-27 19:49:10] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -86.968765-0.004896j
[2025-08-27 19:49:31] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -86.871502-0.005985j
[2025-08-27 19:49:52] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -86.902150-0.000331j
[2025-08-27 19:50:12] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -87.010847+0.002652j
[2025-08-27 19:50:33] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -86.934733-0.001072j
[2025-08-27 19:50:54] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -86.790111+0.005081j
[2025-08-27 19:51:14] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -86.865138+0.017420j
[2025-08-27 19:51:35] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -86.879102+0.000135j
[2025-08-27 19:51:56] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -86.740179-0.004026j
[2025-08-27 19:52:16] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -86.818533-0.006969j
[2025-08-27 19:52:37] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -86.956859+0.002810j
[2025-08-27 19:52:58] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -86.806308+0.000762j
[2025-08-27 19:53:18] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -86.834664+0.001139j
[2025-08-27 19:53:39] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -86.905025-0.007974j
[2025-08-27 19:53:59] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -86.757542-0.008873j
[2025-08-27 19:54:20] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -86.817745-0.003926j
[2025-08-27 19:54:41] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -86.973617-0.006065j
[2025-08-27 19:55:01] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -86.912860-0.002821j
[2025-08-27 19:55:22] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -87.001476+0.000902j
[2025-08-27 19:55:43] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -86.800340+0.002962j
[2025-08-27 19:56:03] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -86.948732+0.003978j
[2025-08-27 19:56:03] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 19:56:24] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -86.908565-0.001170j
[2025-08-27 19:56:45] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -86.872505-0.001329j
[2025-08-27 19:57:05] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -86.747413+0.007796j
[2025-08-27 19:57:26] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -86.761342-0.001176j
[2025-08-27 19:57:47] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -86.809393-0.005220j
[2025-08-27 19:58:07] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -86.843583-0.004602j
[2025-08-27 19:58:28] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -86.723474-0.003800j
[2025-08-27 19:58:49] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -86.779491-0.003678j
[2025-08-27 19:59:09] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -86.783700-0.006763j
[2025-08-27 19:59:30] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -86.998359-0.008753j
[2025-08-27 19:59:51] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -86.843437-0.002274j
[2025-08-27 20:00:11] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -86.666837+0.001911j
[2025-08-27 20:00:32] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -86.767255+0.003963j
[2025-08-27 20:00:53] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -86.797294+0.000930j
[2025-08-27 20:01:13] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -86.750983+0.005400j
[2025-08-27 20:01:34] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -86.811541-0.005967j
[2025-08-27 20:01:55] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -86.763722+0.002663j
[2025-08-27 20:02:15] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -86.717047+0.000756j
[2025-08-27 20:02:36] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -86.715953+0.001200j
[2025-08-27 20:02:56] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -86.874503-0.000948j
[2025-08-27 20:03:17] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -86.827147-0.007681j
[2025-08-27 20:03:38] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -86.940141-0.003572j
[2025-08-27 20:03:58] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -86.784878+0.004398j
[2025-08-27 20:04:19] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -86.859391+0.002737j
[2025-08-27 20:04:40] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -86.992689-0.005575j
[2025-08-27 20:05:00] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -86.891763-0.002264j
[2025-08-27 20:05:21] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -86.891654+0.003536j
[2025-08-27 20:05:42] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -86.728061-0.003178j
[2025-08-27 20:06:02] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -86.935357-0.001540j
[2025-08-27 20:06:23] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -86.991313-0.008916j
[2025-08-27 20:06:44] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -87.137975-0.010001j
[2025-08-27 20:07:04] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -87.114696-0.002227j
[2025-08-27 20:07:25] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -86.978507-0.004942j
[2025-08-27 20:07:45] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -86.813736-0.002091j
[2025-08-27 20:08:06] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -86.761614+0.004990j
[2025-08-27 20:08:27] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -86.903310+0.006551j
[2025-08-27 20:08:47] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -87.042947+0.011738j
[2025-08-27 20:09:07] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -86.995606+0.004679j
[2025-08-27 20:09:28] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -87.050174+0.003312j
[2025-08-27 20:09:48] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -86.939545-0.002216j
[2025-08-27 20:10:09] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -86.921888-0.003197j
[2025-08-27 20:10:30] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -87.027671+0.001787j
[2025-08-27 20:10:50] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -86.902730+0.001937j
[2025-08-27 20:11:11] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -86.893219+0.001454j
[2025-08-27 20:11:32] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -86.935448-0.003190j
[2025-08-27 20:11:52] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -86.978911+0.006454j
[2025-08-27 20:12:13] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -86.816081+0.010518j
[2025-08-27 20:12:34] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -86.772827+0.000109j
[2025-08-27 20:12:54] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -86.786900-0.000813j
[2025-08-27 20:13:15] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -86.884443-0.006495j
[2025-08-27 20:13:15] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 20:13:36] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -86.675653+0.003214j
[2025-08-27 20:13:56] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -86.876061+0.002406j
[2025-08-27 20:14:17] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -86.922695+0.001472j
[2025-08-27 20:14:38] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -86.725103+0.001830j
[2025-08-27 20:14:58] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -86.722325-0.000688j
[2025-08-27 20:15:19] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -86.757132+0.000724j
[2025-08-27 20:15:39] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -86.810611+0.004729j
[2025-08-27 20:16:00] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -86.741883+0.001895j
[2025-08-27 20:16:21] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -86.710553+0.003562j
[2025-08-27 20:16:42] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -86.823429-0.006892j
[2025-08-27 20:17:02] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -86.864092+0.000672j
[2025-08-27 20:17:23] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -86.743478+0.002204j
[2025-08-27 20:17:43] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -86.752465+0.000958j
[2025-08-27 20:18:04] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -86.809294+0.004442j
[2025-08-27 20:18:25] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -86.902139+0.002999j
[2025-08-27 20:18:45] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -86.872064+0.001223j
[2025-08-27 20:19:06] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -86.951769+0.010319j
[2025-08-27 20:19:27] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -86.882736-0.004729j
[2025-08-27 20:19:47] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -86.816832-0.002087j
[2025-08-27 20:20:08] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -86.726695-0.006456j
[2025-08-27 20:20:29] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -86.829642-0.002889j
[2025-08-27 20:20:49] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -86.739696+0.005526j
[2025-08-27 20:21:10] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -86.849125-0.009473j
[2025-08-27 20:21:31] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -86.769377-0.003151j
[2025-08-27 20:21:51] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -86.920312+0.003656j
[2025-08-27 20:22:12] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -86.719084-0.001900j
[2025-08-27 20:22:33] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -86.839326+0.001275j
[2025-08-27 20:22:53] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -86.992956+0.010496j
[2025-08-27 20:23:14] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -86.647389+0.004565j
[2025-08-27 20:23:35] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -86.949730+0.003275j
[2025-08-27 20:23:55] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -86.734331+0.004557j
[2025-08-27 20:24:16] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -86.811085+0.002485j
[2025-08-27 20:24:37] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -86.865006-0.000085j
[2025-08-27 20:24:57] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -86.676975+0.000708j
[2025-08-27 20:25:18] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -86.819101-0.000531j
[2025-08-27 20:25:38] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -86.847708-0.000868j
[2025-08-27 20:25:59] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -86.849396-0.002851j
[2025-08-27 20:26:20] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -86.665719-0.004122j
[2025-08-27 20:26:40] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -86.956065+0.006985j
[2025-08-27 20:27:01] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -86.803707-0.004432j
[2025-08-27 20:27:22] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -86.840106+0.007643j
[2025-08-27 20:27:42] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -86.847695-0.003731j
[2025-08-27 20:28:03] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -86.975441-0.000682j
[2025-08-27 20:28:23] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -86.997574+0.000211j
[2025-08-27 20:28:44] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -86.876160-0.003330j
[2025-08-27 20:29:05] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -86.846480-0.003605j
[2025-08-27 20:29:25] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -86.717945-0.014235j
[2025-08-27 20:29:46] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -86.886255-0.002777j
[2025-08-27 20:30:07] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -86.900087-0.005523j
[2025-08-27 20:30:27] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -86.744021-0.001750j
[2025-08-27 20:30:27] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 20:30:27] RESTART #1 | Period: 300
[2025-08-27 20:30:48] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -86.745268+0.002915j
[2025-08-27 20:31:09] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -86.798486-0.000075j
[2025-08-27 20:31:29] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -86.907804-0.007603j
[2025-08-27 20:31:50] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -86.877164+0.002693j
[2025-08-27 20:32:11] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -87.009330+0.004037j
[2025-08-27 20:32:31] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -86.819251-0.010849j
[2025-08-27 20:32:52] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -86.897220-0.003786j
[2025-08-27 20:33:13] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -86.826842-0.005606j
[2025-08-27 20:33:33] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -86.822667+0.000809j
[2025-08-27 20:33:54] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -86.927216-0.002857j
[2025-08-27 20:34:15] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -87.012838-0.007218j
[2025-08-27 20:34:35] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -86.894707+0.013286j
[2025-08-27 20:34:56] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -87.095551-0.001021j
[2025-08-27 20:35:16] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -86.920932+0.001687j
[2025-08-27 20:35:37] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -86.982168+0.002708j
[2025-08-27 20:35:58] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -87.020151-0.001710j
[2025-08-27 20:36:18] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -87.157951+0.004099j
[2025-08-27 20:36:39] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -87.063712+0.000735j
[2025-08-27 20:37:00] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -87.083531-0.006546j
[2025-08-27 20:37:20] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -86.916500+0.002550j
[2025-08-27 20:37:41] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -86.994000+0.004200j
[2025-08-27 20:38:02] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -87.185514-0.004117j
[2025-08-27 20:38:22] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -86.885767-0.006291j
[2025-08-27 20:38:43] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -86.903368-0.008227j
[2025-08-27 20:39:04] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -86.759200+0.013900j
[2025-08-27 20:39:24] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -86.806114+0.001968j
[2025-08-27 20:39:45] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -86.887052-0.005103j
[2025-08-27 20:40:06] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -86.761791-0.001938j
[2025-08-27 20:40:26] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -86.718003-0.007570j
[2025-08-27 20:40:47] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -86.758634+0.008111j
[2025-08-27 20:41:08] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -86.677601-0.001663j
[2025-08-27 20:41:28] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -86.794571-0.002580j
[2025-08-27 20:41:49] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -86.670979-0.005234j
[2025-08-27 20:42:10] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -86.793025+0.001630j
[2025-08-27 20:42:30] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -86.739076-0.004724j
[2025-08-27 20:42:51] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -86.901397+0.008381j
[2025-08-27 20:43:11] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -86.872840-0.014382j
[2025-08-27 20:43:32] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -86.802893-0.006848j
[2025-08-27 20:43:53] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -86.863954+0.004395j
[2025-08-27 20:44:13] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -86.887595+0.004176j
[2025-08-27 20:44:34] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -86.692187+0.000369j
[2025-08-27 20:44:55] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -86.534812-0.003150j
[2025-08-27 20:45:15] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -86.546739+0.000702j
[2025-08-27 20:45:36] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -86.675375+0.007674j
[2025-08-27 20:45:57] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -86.705855-0.005686j
[2025-08-27 20:46:17] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -86.801135-0.003647j
[2025-08-27 20:46:38] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -86.654289+0.003024j
[2025-08-27 20:46:59] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -86.784064+0.003880j
[2025-08-27 20:47:19] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -86.715586+0.002311j
[2025-08-27 20:47:40] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -86.902561+0.000165j
[2025-08-27 20:47:40] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 20:48:00] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -86.776701+0.003339j
[2025-08-27 20:48:21] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -86.783552-0.007323j
[2025-08-27 20:48:42] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -86.688511+0.000244j
[2025-08-27 20:49:02] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -86.812892-0.001582j
[2025-08-27 20:49:23] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -86.775477-0.002421j
[2025-08-27 20:49:44] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -86.948482+0.003845j
[2025-08-27 20:50:04] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -86.752241+0.007568j
[2025-08-27 20:50:25] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -86.830414+0.007892j
[2025-08-27 20:50:45] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -86.730054+0.001118j
[2025-08-27 20:51:06] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -86.928006-0.005022j
[2025-08-27 20:51:26] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -86.819910+0.002209j
[2025-08-27 20:51:47] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -86.822511+0.004178j
[2025-08-27 20:52:08] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -86.762801-0.009546j
[2025-08-27 20:52:28] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -86.771350+0.004441j
[2025-08-27 20:52:49] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -86.813633+0.004271j
[2025-08-27 20:53:10] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -86.661041-0.001898j
[2025-08-27 20:53:30] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -86.795159+0.003236j
[2025-08-27 20:53:51] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -86.714920-0.004518j
[2025-08-27 20:54:11] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -86.627509-0.000858j
[2025-08-27 20:54:32] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -86.759195+0.000172j
[2025-08-27 20:54:53] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -86.767091-0.007057j
[2025-08-27 20:55:13] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -86.771908-0.000909j
[2025-08-27 20:55:34] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -86.812020-0.001349j
[2025-08-27 20:55:55] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -86.820631+0.003330j
[2025-08-27 20:56:15] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -86.911809-0.002120j
[2025-08-27 20:56:36] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -86.926585+0.000635j
[2025-08-27 20:56:57] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -86.885474+0.005178j
[2025-08-27 20:57:17] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -86.614776-0.003975j
[2025-08-27 20:57:38] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -86.865507+0.004099j
[2025-08-27 20:57:59] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -86.759610+0.002161j
[2025-08-27 20:58:19] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -86.883416+0.002435j
[2025-08-27 20:58:40] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -87.061556-0.006939j
[2025-08-27 20:59:00] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -86.809192+0.005435j
[2025-08-27 20:59:21] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -86.888694+0.008095j
[2025-08-27 20:59:42] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -86.930630+0.004497j
[2025-08-27 21:00:02] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -86.842758-0.001457j
[2025-08-27 21:00:23] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -86.709904-0.001881j
[2025-08-27 21:00:44] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -86.904761-0.000253j
[2025-08-27 21:01:04] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -86.889025-0.007000j
[2025-08-27 21:01:25] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -86.751782+0.005829j
[2025-08-27 21:01:46] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -86.759308+0.002770j
[2025-08-27 21:02:06] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -86.803155+0.004982j
[2025-08-27 21:02:27] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -86.855971-0.001581j
[2025-08-27 21:02:48] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -86.777924-0.009165j
[2025-08-27 21:03:08] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -86.907106+0.003928j
[2025-08-27 21:03:29] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -86.851386+0.001208j
[2025-08-27 21:03:50] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -86.754571+0.008247j
[2025-08-27 21:04:10] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -86.789381+0.002803j
[2025-08-27 21:04:31] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -86.935869-0.003534j
[2025-08-27 21:04:52] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -86.890203-0.000222j
[2025-08-27 21:04:52] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 21:05:13] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -86.910135-0.009329j
[2025-08-27 21:05:33] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -86.769565-0.000648j
[2025-08-27 21:05:54] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -86.867969+0.004650j
[2025-08-27 21:06:15] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -86.817881-0.000055j
[2025-08-27 21:06:35] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -86.766531+0.006277j
[2025-08-27 21:06:56] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -86.820784+0.009361j
[2025-08-27 21:07:17] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -86.823143-0.009905j
[2025-08-27 21:07:37] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -86.730153-0.000395j
[2025-08-27 21:07:58] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -86.930631-0.001751j
[2025-08-27 21:08:19] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -86.848585+0.001420j
[2025-08-27 21:08:39] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -86.813446+0.001630j
[2025-08-27 21:09:00] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -86.964068-0.002340j
[2025-08-27 21:09:20] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -86.893692+0.010463j
[2025-08-27 21:09:41] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -86.952540+0.004151j
[2025-08-27 21:10:02] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -87.137274-0.005995j
[2025-08-27 21:10:22] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -86.921765+0.007198j
[2025-08-27 21:10:43] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -86.789937-0.001606j
[2025-08-27 21:11:04] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -86.869618-0.001838j
[2025-08-27 21:11:24] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -86.834766+0.007425j
[2025-08-27 21:11:45] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -86.845769-0.010052j
[2025-08-27 21:12:06] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -86.924229-0.005608j
[2025-08-27 21:12:26] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -86.697443-0.001193j
[2025-08-27 21:12:47] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -86.874134+0.003641j
[2025-08-27 21:13:07] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -86.924820-0.009369j
[2025-08-27 21:13:28] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -86.903029-0.006762j
[2025-08-27 21:13:49] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -86.906973-0.005651j
[2025-08-27 21:14:09] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -86.890192+0.004487j
[2025-08-27 21:14:30] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -86.816329-0.001960j
[2025-08-27 21:14:51] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -86.925287-0.003889j
[2025-08-27 21:15:11] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -86.837713+0.007502j
[2025-08-27 21:15:32] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -86.913941+0.004144j
[2025-08-27 21:15:53] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -86.759706+0.007768j
[2025-08-27 21:16:13] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -86.912236+0.001857j
[2025-08-27 21:16:34] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -86.999658-0.000355j
[2025-08-27 21:16:55] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -86.938999-0.006501j
[2025-08-27 21:17:15] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -86.966981-0.001148j
[2025-08-27 21:17:36] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -86.947546+0.000251j
[2025-08-27 21:17:57] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -86.943184-0.004548j
[2025-08-27 21:18:17] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -86.765917+0.002010j
[2025-08-27 21:18:38] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -86.874140-0.000348j
[2025-08-27 21:18:58] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -86.786398+0.006977j
[2025-08-27 21:19:19] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -86.869231+0.001278j
[2025-08-27 21:19:40] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -86.836817-0.001928j
[2025-08-27 21:20:00] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -86.988033-0.005960j
[2025-08-27 21:20:21] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -86.825598+0.002794j
[2025-08-27 21:20:42] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -87.000442-0.006897j
[2025-08-27 21:21:02] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -86.982839+0.002777j
[2025-08-27 21:21:23] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -86.849526+0.006749j
[2025-08-27 21:21:43] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -86.910549-0.002498j
[2025-08-27 21:22:04] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -86.932215+0.001800j
[2025-08-27 21:22:04] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 21:22:25] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -87.026053+0.002335j
[2025-08-27 21:22:45] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -87.113735-0.010328j
[2025-08-27 21:23:06] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -87.068106+0.000652j
[2025-08-27 21:23:27] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -86.973899-0.010296j
[2025-08-27 21:23:47] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -87.022738-0.001769j
[2025-08-27 21:24:08] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -87.001529+0.003241j
[2025-08-27 21:24:28] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -86.916077+0.000036j
[2025-08-27 21:24:49] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -86.961160-0.001205j
[2025-08-27 21:25:10] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -86.866604-0.001534j
[2025-08-27 21:25:30] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -86.804485+0.000662j
[2025-08-27 21:25:51] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -86.681228+0.000201j
[2025-08-27 21:26:12] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -86.711379+0.002370j
[2025-08-27 21:26:32] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -86.559652+0.008375j
[2025-08-27 21:26:53] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -86.639611+0.013617j
[2025-08-27 21:27:14] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -86.703287-0.005310j
[2025-08-27 21:27:34] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -86.723262-0.003491j
[2025-08-27 21:27:55] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -86.917823-0.002340j
[2025-08-27 21:28:15] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -86.965184-0.006800j
[2025-08-27 21:28:36] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -86.855293+0.003049j
[2025-08-27 21:28:57] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -86.701734+0.002055j
[2025-08-27 21:29:17] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -86.716568-0.001880j
[2025-08-27 21:29:38] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -86.839559+0.001906j
[2025-08-27 21:29:59] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -86.776722+0.004750j
[2025-08-27 21:30:19] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -86.853633-0.002123j
[2025-08-27 21:30:40] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -86.874912-0.014457j
[2025-08-27 21:31:01] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -86.988870-0.005798j
[2025-08-27 21:31:21] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -87.109315+0.003328j
[2025-08-27 21:31:42] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -87.041301+0.004052j
[2025-08-27 21:32:03] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -87.099842+0.004381j
[2025-08-27 21:32:24] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -86.835009+0.005735j
[2025-08-27 21:32:44] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -86.932269+0.002732j
[2025-08-27 21:33:05] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -86.967030-0.008614j
[2025-08-27 21:33:26] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -87.017823-0.000306j
[2025-08-27 21:33:46] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -87.096090-0.002579j
[2025-08-27 21:34:07] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -87.050898+0.004768j
[2025-08-27 21:34:27] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -86.798080-0.000369j
[2025-08-27 21:34:48] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -86.839279+0.001456j
[2025-08-27 21:35:09] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -86.870517+0.002307j
[2025-08-27 21:35:29] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -86.842096-0.010902j
[2025-08-27 21:35:50] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -86.819621+0.000219j
[2025-08-27 21:36:11] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -86.609216+0.007403j
[2025-08-27 21:36:31] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -86.579097-0.001036j
[2025-08-27 21:36:52] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -86.713183+0.000145j
[2025-08-27 21:37:13] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -86.620621-0.003419j
[2025-08-27 21:37:33] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -86.648673+0.005985j
[2025-08-27 21:37:54] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -86.897868-0.002613j
[2025-08-27 21:38:14] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -86.829003+0.003527j
[2025-08-27 21:38:35] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -86.908473-0.009287j
[2025-08-27 21:38:56] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -86.928341-0.001635j
[2025-08-27 21:39:16] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -87.049954+0.005993j
[2025-08-27 21:39:17] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 21:39:37] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -86.867920+0.004693j
[2025-08-27 21:39:58] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -86.848474-0.005941j
[2025-08-27 21:40:19] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -86.824194-0.005478j
[2025-08-27 21:40:39] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -87.006051+0.005966j
[2025-08-27 21:41:00] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -86.880647-0.001872j
[2025-08-27 21:41:21] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -86.926147+0.003037j
[2025-08-27 21:41:41] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -86.735417+0.004088j
[2025-08-27 21:42:02] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -86.851901-0.001223j
[2025-08-27 21:42:23] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -86.763011-0.004894j
[2025-08-27 21:42:43] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -86.751282-0.001688j
[2025-08-27 21:43:04] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -86.765303+0.000033j
[2025-08-27 21:43:24] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -86.899982-0.004238j
[2025-08-27 21:43:45] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -86.735406-0.001219j
[2025-08-27 21:44:06] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -86.848935-0.005493j
[2025-08-27 21:44:26] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -86.747874-0.003128j
[2025-08-27 21:44:47] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -86.899192-0.000675j
[2025-08-27 21:45:08] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -86.861057-0.002941j
[2025-08-27 21:45:28] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -86.818092+0.001924j
[2025-08-27 21:45:49] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -86.790559+0.000833j
[2025-08-27 21:46:09] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -86.908606+0.002993j
[2025-08-27 21:46:30] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -86.721490-0.005049j
[2025-08-27 21:46:51] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -86.685518-0.003057j
[2025-08-27 21:47:11] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -86.580298+0.000529j
[2025-08-27 21:47:32] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -86.724719-0.009659j
[2025-08-27 21:47:53] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -86.594548+0.003314j
[2025-08-27 21:48:13] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -86.607824+0.005163j
[2025-08-27 21:48:34] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -86.724182+0.010520j
[2025-08-27 21:48:55] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -86.775929+0.001245j
[2025-08-27 21:49:15] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -86.674994-0.005930j
[2025-08-27 21:49:36] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -86.627897+0.004695j
[2025-08-27 21:49:57] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -86.804265-0.005495j
[2025-08-27 21:50:17] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -86.740181-0.001625j
[2025-08-27 21:50:38] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -86.687020-0.009065j
[2025-08-27 21:50:58] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -86.894522+0.007575j
[2025-08-27 21:51:19] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -86.785201-0.000040j
[2025-08-27 21:51:40] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -86.802176+0.010346j
[2025-08-27 21:52:00] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -86.694099+0.001913j
[2025-08-27 21:52:21] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -86.666360-0.006686j
[2025-08-27 21:52:42] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -86.689276-0.001775j
[2025-08-27 21:53:02] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -86.712135-0.004811j
[2025-08-27 21:53:23] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -86.713219-0.003092j
[2025-08-27 21:53:44] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -86.798242+0.006809j
[2025-08-27 21:54:04] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -86.684505-0.002063j
[2025-08-27 21:54:25] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -86.806055-0.006494j
[2025-08-27 21:54:46] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -86.754501+0.001037j
[2025-08-27 21:55:06] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -86.776094-0.006425j
[2025-08-27 21:55:27] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -86.671506+0.003718j
[2025-08-27 21:55:48] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -86.633524-0.007969j
[2025-08-27 21:56:08] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -86.784197+0.003148j
[2025-08-27 21:56:29] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -86.745110-0.005549j
[2025-08-27 21:56:29] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 21:56:50] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -86.850610-0.003711j
[2025-08-27 21:57:10] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -86.839446+0.004976j
[2025-08-27 21:57:31] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -86.783276-0.004270j
[2025-08-27 21:57:52] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -86.754721-0.001651j
[2025-08-27 21:58:12] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -86.735378+0.003461j
[2025-08-27 21:58:33] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -86.850737+0.002140j
[2025-08-27 21:58:53] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -86.799859+0.008658j
[2025-08-27 21:59:14] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -86.855303+0.003764j
[2025-08-27 21:59:35] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -86.867517+0.008981j
[2025-08-27 21:59:56] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -86.886912-0.012365j
[2025-08-27 22:00:16] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -86.798736-0.001833j
[2025-08-27 22:00:37] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -86.818753-0.006843j
[2025-08-27 22:00:58] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -86.868560-0.007790j
[2025-08-27 22:01:18] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -86.878083-0.003545j
[2025-08-27 22:01:39] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -86.730768+0.002373j
[2025-08-27 22:02:00] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -86.801743-0.008016j
[2025-08-27 22:02:20] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -86.873313-0.008147j
[2025-08-27 22:02:41] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -86.882424+0.003897j
[2025-08-27 22:03:01] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -86.987234-0.003746j
[2025-08-27 22:03:22] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -86.984200-0.004827j
[2025-08-27 22:03:43] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -87.091012-0.002029j
[2025-08-27 22:04:03] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -86.958164-0.001325j
[2025-08-27 22:04:24] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -86.971920+0.003327j
[2025-08-27 22:04:45] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -86.867205+0.002643j
[2025-08-27 22:05:05] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -86.862364-0.000613j
[2025-08-27 22:05:26] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -86.819430-0.002401j
[2025-08-27 22:05:47] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -86.682422+0.003521j
[2025-08-27 22:06:07] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -86.781617+0.001792j
[2025-08-27 22:06:28] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -86.723851+0.010954j
[2025-08-27 22:06:48] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -86.663182-0.003869j
[2025-08-27 22:07:09] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -86.684332-0.004053j
[2025-08-27 22:07:30] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -86.723445+0.005105j
[2025-08-27 22:07:50] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -86.545528+0.008690j
[2025-08-27 22:08:11] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -86.763540-0.003626j
[2025-08-27 22:08:32] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -86.832780-0.004842j
[2025-08-27 22:08:52] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -86.855556+0.006349j
[2025-08-27 22:09:13] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -86.708933+0.000432j
[2025-08-27 22:09:34] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -86.754088+0.011182j
[2025-08-27 22:09:54] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -86.770506+0.002941j
[2025-08-27 22:10:15] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -86.766440-0.007949j
[2025-08-27 22:10:36] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -86.847247+0.004623j
[2025-08-27 22:10:56] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -86.759206-0.002479j
[2025-08-27 22:11:17] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -86.698129-0.002114j
[2025-08-27 22:11:38] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -86.818873-0.003735j
[2025-08-27 22:11:58] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -86.773229-0.000042j
[2025-08-27 22:12:19] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -86.718899-0.005949j
[2025-08-27 22:12:32] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -86.564358-0.007364j
[2025-08-27 22:12:42] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -86.814552-0.001812j
[2025-08-27 22:12:51] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -86.691497-0.003933j
[2025-08-27 22:13:00] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -86.692187+0.002484j
[2025-08-27 22:13:00] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 22:13:00] ✅ Training completed | Restarts: 1
[2025-08-27 22:13:00] ============================================================
[2025-08-27 22:13:00] Training completed | Runtime: 9297.8s
[2025-08-27 22:13:04] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 22:13:04] ============================================================
