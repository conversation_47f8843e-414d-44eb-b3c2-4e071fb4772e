[2025-08-27 17:02:14] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-08-27 17:02:14]   - 迭代次数: final
[2025-08-27 17:02:14]   - 能量: -85.400138-0.006478j ± 0.109090
[2025-08-27 17:02:14]   - 时间戳: 2025-08-27T17:01:51.046171+08:00
[2025-08-27 17:02:24] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 17:02:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 17:02:24] ==================================================
[2025-08-27 17:02:24] GCNN for Shastry-Sutherland Model
[2025-08-27 17:02:24] ==================================================
[2025-08-27 17:02:24] System parameters:
[2025-08-27 17:02:24]   - System size: L=5, N=100
[2025-08-27 17:02:24]   - System parameters: J1=0.06, J2=0.0, Q=1.0
[2025-08-27 17:02:24] --------------------------------------------------
[2025-08-27 17:02:24] Model parameters:
[2025-08-27 17:02:24]   - Number of layers = 4
[2025-08-27 17:02:24]   - Number of features = 4
[2025-08-27 17:02:24]   - Total parameters = 19628
[2025-08-27 17:02:24] --------------------------------------------------
[2025-08-27 17:02:24] Training parameters:
[2025-08-27 17:02:24]   - Learning rate: 0.015
[2025-08-27 17:02:24]   - Total iterations: 450
[2025-08-27 17:02:24]   - Annealing cycles: 2
[2025-08-27 17:02:24]   - Initial period: 150
[2025-08-27 17:02:24]   - Period multiplier: 2.0
[2025-08-27 17:02:24]   - Temperature range: 0.0-1.0
[2025-08-27 17:02:24]   - Samples: 4096
[2025-08-27 17:02:24]   - Discarded samples: 0
[2025-08-27 17:02:24]   - Chunk size: 2048
[2025-08-27 17:02:24]   - Diagonal shift: 0.2
[2025-08-27 17:02:24]   - Gradient clipping: 1.0
[2025-08-27 17:02:24]   - Checkpoint enabled: interval=50
[2025-08-27 17:02:24]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.06/training/checkpoints
[2025-08-27 17:02:24] --------------------------------------------------
[2025-08-27 17:02:24] Device status:
[2025-08-27 17:02:24]   - Devices model: NVIDIA H200 NVL
[2025-08-27 17:02:24]   - Number of devices: 1
[2025-08-27 17:02:25]   - Sharding: True
[2025-08-27 17:02:25] ============================================================
[2025-08-27 17:03:16] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -86.543719-0.002902j
[2025-08-27 17:03:54] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -86.319046-0.010349j
[2025-08-27 17:04:15] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -86.294976-0.007471j
[2025-08-27 17:04:35] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -86.132698-0.007246j
[2025-08-27 17:04:56] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -86.323597-0.004209j
[2025-08-27 17:05:17] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -86.377686+0.003426j
[2025-08-27 17:05:37] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -86.343301+0.000905j
[2025-08-27 17:05:58] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -86.346173+0.005151j
[2025-08-27 17:06:19] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -86.321125+0.010506j
[2025-08-27 17:06:39] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -86.244320+0.008533j
[2025-08-27 17:07:00] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -86.226996+0.001238j
[2025-08-27 17:07:21] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -86.307444+0.005764j
[2025-08-27 17:07:42] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -86.287940+0.009920j
[2025-08-27 17:08:02] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -86.212342+0.002701j
[2025-08-27 17:08:23] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -86.177034+0.000800j
[2025-08-27 17:08:44] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -86.162827+0.000961j
[2025-08-27 17:09:04] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -86.388007+0.000885j
[2025-08-27 17:09:25] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -86.293355-0.010614j
[2025-08-27 17:09:46] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -86.230266+0.004673j
[2025-08-27 17:10:06] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -86.299427+0.004791j
[2025-08-27 17:10:27] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -86.252821-0.000542j
[2025-08-27 17:10:47] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -86.158412-0.004376j
[2025-08-27 17:11:08] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -86.099218+0.009793j
[2025-08-27 17:11:29] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -86.231884-0.003338j
[2025-08-27 17:11:49] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -86.092554-0.007090j
[2025-08-27 17:12:10] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -86.175290-0.010829j
[2025-08-27 17:12:31] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -86.199073+0.000308j
[2025-08-27 17:12:51] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -86.265869-0.002544j
[2025-08-27 17:13:12] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -86.153500-0.000589j
[2025-08-27 17:13:33] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -86.160527-0.004064j
[2025-08-27 17:13:53] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -86.000499-0.006901j
[2025-08-27 17:14:14] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -85.963850-0.000286j
[2025-08-27 17:14:35] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -86.055981-0.001427j
[2025-08-27 17:14:56] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -86.066454-0.004432j
[2025-08-27 17:15:16] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -86.054311-0.008129j
[2025-08-27 17:15:37] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -86.172116+0.001892j
[2025-08-27 17:15:58] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -86.024876-0.009407j
[2025-08-27 17:16:18] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -86.120340-0.007976j
[2025-08-27 17:16:39] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -86.202734+0.000394j
[2025-08-27 17:17:00] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -86.140804+0.000962j
[2025-08-27 17:17:20] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -86.168359-0.001167j
[2025-08-27 17:17:41] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -86.204285-0.010880j
[2025-08-27 17:18:02] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -86.148572-0.007426j
[2025-08-27 17:18:22] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -86.271083-0.001137j
[2025-08-27 17:18:43] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -86.126705-0.000073j
[2025-08-27 17:19:04] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -86.165808+0.000963j
[2025-08-27 17:19:24] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -86.302064-0.001330j
[2025-08-27 17:19:45] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -86.097317+0.006656j
[2025-08-27 17:20:06] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -86.229600-0.003011j
[2025-08-27 17:20:26] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -86.229516-0.000653j
[2025-08-27 17:20:26] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 17:20:47] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -86.271873+0.004351j
[2025-08-27 17:21:08] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -86.320584-0.002721j
[2025-08-27 17:21:29] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -86.184517+0.002312j
[2025-08-27 17:21:49] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -86.069603-0.001368j
[2025-08-27 17:22:10] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -86.117884-0.004138j
[2025-08-27 17:22:31] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -86.093704-0.005174j
[2025-08-27 17:22:51] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -85.932890-0.004397j
[2025-08-27 17:23:12] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -85.990975-0.012559j
[2025-08-27 17:23:33] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -86.140762-0.001881j
[2025-08-27 17:23:53] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -85.875077+0.004720j
[2025-08-27 17:24:14] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -86.113461-0.003662j
[2025-08-27 17:24:35] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -86.364278-0.002401j
[2025-08-27 17:24:56] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -86.413776-0.002569j
[2025-08-27 17:25:16] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -86.369364+0.000671j
[2025-08-27 17:25:37] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -86.315354+0.010084j
[2025-08-27 17:25:58] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -86.218803+0.007385j
[2025-08-27 17:26:18] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -86.077695-0.008341j
[2025-08-27 17:26:39] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -86.344874+0.001217j
[2025-08-27 17:27:00] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -86.197339+0.000184j
[2025-08-27 17:27:20] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -86.257727+0.001841j
[2025-08-27 17:27:41] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -86.199645+0.004482j
[2025-08-27 17:28:02] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -86.292821+0.005429j
[2025-08-27 17:28:22] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -86.169776-0.000598j
[2025-08-27 17:28:43] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -86.260557-0.003196j
[2025-08-27 17:29:04] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -86.135405-0.005598j
[2025-08-27 17:29:24] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -86.146834-0.000868j
[2025-08-27 17:29:45] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -86.330057-0.003656j
[2025-08-27 17:30:06] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -86.166588+0.000179j
[2025-08-27 17:30:26] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -86.168550-0.001244j
[2025-08-27 17:30:47] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -86.122739-0.007397j
[2025-08-27 17:31:08] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -86.194413-0.000099j
[2025-08-27 17:31:28] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -86.218096+0.003190j
[2025-08-27 17:31:49] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -86.163818-0.001362j
[2025-08-27 17:32:10] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -86.214612+0.002043j
[2025-08-27 17:32:30] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -86.290764+0.000758j
[2025-08-27 17:32:51] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -86.284856-0.007620j
[2025-08-27 17:33:12] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -86.217541-0.006471j
[2025-08-27 17:33:32] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -86.103700+0.001493j
[2025-08-27 17:33:53] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -86.268706+0.003646j
[2025-08-27 17:34:14] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -86.390427-0.002377j
[2025-08-27 17:34:34] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -86.417263+0.004558j
[2025-08-27 17:34:55] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -86.223310-0.004321j
[2025-08-27 17:35:16] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -86.354704+0.001641j
[2025-08-27 17:35:37] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -86.209537-0.006490j
[2025-08-27 17:35:57] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -86.263153-0.001856j
[2025-08-27 17:36:18] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -86.292129-0.002744j
[2025-08-27 17:36:39] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -86.326813-0.005109j
[2025-08-27 17:36:59] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -86.311197-0.001562j
[2025-08-27 17:37:20] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -86.277498-0.004496j
[2025-08-27 17:37:41] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -86.260506-0.002389j
[2025-08-27 17:37:41] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 17:38:02] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -86.237580-0.005922j
[2025-08-27 17:38:23] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -86.193463-0.005299j
[2025-08-27 17:38:43] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -86.173279+0.004055j
[2025-08-27 17:39:04] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -86.143031-0.011754j
[2025-08-27 17:39:25] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -86.157546-0.001119j
[2025-08-27 17:39:45] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -86.359605-0.002640j
[2025-08-27 17:40:06] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -86.365996-0.005900j
[2025-08-27 17:40:27] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -86.475953-0.006331j
[2025-08-27 17:40:47] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -86.419344-0.005401j
[2025-08-27 17:41:08] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -86.381078-0.008765j
[2025-08-27 17:41:29] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -86.504397+0.000952j
[2025-08-27 17:41:50] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -86.435243-0.004265j
[2025-08-27 17:42:10] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -86.271565+0.004792j
[2025-08-27 17:42:31] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -86.280287-0.005209j
[2025-08-27 17:42:52] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -86.194035-0.007944j
[2025-08-27 17:43:12] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -86.090543-0.008432j
[2025-08-27 17:43:33] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -86.181600-0.001125j
[2025-08-27 17:43:54] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -86.084265-0.001619j
[2025-08-27 17:44:14] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -86.054427-0.009961j
[2025-08-27 17:44:35] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -86.039328+0.006974j
[2025-08-27 17:44:56] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -86.064387-0.003989j
[2025-08-27 17:45:16] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -85.885376+0.000058j
[2025-08-27 17:45:37] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -85.981166+0.001127j
[2025-08-27 17:45:58] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -86.018713-0.003722j
[2025-08-27 17:46:18] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -86.068284-0.001717j
[2025-08-27 17:46:39] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -86.075742+0.003315j
[2025-08-27 17:47:00] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -86.261356-0.000284j
[2025-08-27 17:47:20] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -86.281836+0.000591j
[2025-08-27 17:47:41] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -86.305963-0.005929j
[2025-08-27 17:48:02] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -86.069786+0.007096j
[2025-08-27 17:48:22] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -86.147263-0.005399j
[2025-08-27 17:48:43] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -86.142691-0.007065j
[2025-08-27 17:49:04] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -86.261089+0.002166j
[2025-08-27 17:49:24] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -85.964007+0.000134j
[2025-08-27 17:49:45] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -86.245108-0.004424j
[2025-08-27 17:50:06] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -85.990254-0.001498j
[2025-08-27 17:50:26] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -85.999182-0.005359j
[2025-08-27 17:50:47] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -86.113351+0.009314j
[2025-08-27 17:51:08] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -86.180193-0.003956j
[2025-08-27 17:51:27] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -86.071772-0.000392j
[2025-08-27 17:51:48] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -86.111103+0.000216j
[2025-08-27 17:52:09] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -86.258194+0.000296j
[2025-08-27 17:52:29] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -86.323379-0.002768j
[2025-08-27 17:52:50] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -86.543922-0.005282j
[2025-08-27 17:53:11] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -86.277812-0.008369j
[2025-08-27 17:53:31] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -86.490739-0.004003j
[2025-08-27 17:53:52] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -86.299147-0.006938j
[2025-08-27 17:54:13] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -86.139492+0.004753j
[2025-08-27 17:54:33] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -86.199684-0.001432j
[2025-08-27 17:54:54] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -86.296549+0.002364j
[2025-08-27 17:54:54] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 17:54:54] RESTART #1 | Period: 300
[2025-08-27 17:55:15] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -86.245849+0.000253j
[2025-08-27 17:55:35] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -86.229493+0.005474j
[2025-08-27 17:55:56] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -86.365484+0.004626j
[2025-08-27 17:56:17] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -86.215311+0.005527j
[2025-08-27 17:56:37] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -86.115630+0.003127j
[2025-08-27 17:56:58] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -86.121279-0.000114j
[2025-08-27 17:57:19] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -86.333273+0.001425j
[2025-08-27 17:57:39] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -86.344416-0.000813j
[2025-08-27 17:58:00] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -86.142377-0.002543j
[2025-08-27 17:58:21] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -86.276963+0.003994j
[2025-08-27 17:58:42] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -86.143660-0.002697j
[2025-08-27 17:59:02] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -86.170164-0.000282j
[2025-08-27 17:59:23] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -86.279530-0.004001j
[2025-08-27 17:59:44] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -86.337797+0.001314j
[2025-08-27 18:00:04] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -86.395211+0.000281j
[2025-08-27 18:00:25] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -86.246053-0.002890j
[2025-08-27 18:00:46] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -86.205973+0.005063j
[2025-08-27 18:01:06] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -86.081671-0.003414j
[2025-08-27 18:01:27] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -86.127014-0.003894j
[2025-08-27 18:01:48] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -86.194606+0.009181j
[2025-08-27 18:02:08] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -86.295540-0.007561j
[2025-08-27 18:02:29] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -86.311937-0.009821j
[2025-08-27 18:02:50] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -86.186812+0.002591j
[2025-08-27 18:03:10] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -86.310426-0.000186j
[2025-08-27 18:03:31] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -86.164217-0.008234j
[2025-08-27 18:03:52] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -86.234929-0.005631j
[2025-08-27 18:04:12] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -86.170650+0.000834j
[2025-08-27 18:04:33] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -86.063603-0.000269j
[2025-08-27 18:04:54] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -86.213542-0.002657j
[2025-08-27 18:05:14] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -86.107440+0.002459j
[2025-08-27 18:05:35] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -86.131491+0.000742j
[2025-08-27 18:05:56] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -86.119479+0.002670j
[2025-08-27 18:06:16] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -86.060947+0.001532j
[2025-08-27 18:06:37] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -86.126033-0.003011j
[2025-08-27 18:06:58] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -86.146039+0.006430j
[2025-08-27 18:07:18] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -86.106507+0.002900j
[2025-08-27 18:07:39] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -86.127550-0.004255j
[2025-08-27 18:08:00] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -86.138731+0.006000j
[2025-08-27 18:08:20] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -86.183787-0.005993j
[2025-08-27 18:08:41] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -86.267306+0.003501j
[2025-08-27 18:09:02] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -86.270101+0.001737j
[2025-08-27 18:09:22] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -86.086129-0.001543j
[2025-08-27 18:09:43] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -86.240020-0.002480j
[2025-08-27 18:10:04] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -86.110683-0.013610j
[2025-08-27 18:10:25] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -86.118066+0.002881j
[2025-08-27 18:10:45] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -86.010020-0.011036j
[2025-08-27 18:11:06] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -86.175437-0.003725j
[2025-08-27 18:11:27] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -86.076636-0.001068j
[2025-08-27 18:11:47] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -86.037323+0.001806j
[2025-08-27 18:12:08] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -86.133563+0.003038j
[2025-08-27 18:12:08] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 18:12:29] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -86.057748+0.002129j
[2025-08-27 18:12:49] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -86.043160+0.004976j
[2025-08-27 18:13:10] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -86.113812-0.002737j
[2025-08-27 18:13:31] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -86.139644+0.008852j
[2025-08-27 18:13:51] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -86.175457+0.001325j
[2025-08-27 18:14:12] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -86.192452+0.009168j
[2025-08-27 18:14:33] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -86.134144+0.002096j
[2025-08-27 18:14:53] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -86.102097+0.002280j
[2025-08-27 18:15:14] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -86.217467-0.004898j
[2025-08-27 18:15:35] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -86.158089+0.000739j
[2025-08-27 18:15:55] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -86.142611-0.003221j
[2025-08-27 18:16:16] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -86.176570-0.008103j
[2025-08-27 18:16:37] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -86.396608-0.003611j
[2025-08-27 18:16:57] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -86.277527+0.007173j
[2025-08-27 18:17:18] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -86.323677-0.002851j
[2025-08-27 18:17:39] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -86.204032+0.001808j
[2025-08-27 18:17:59] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -86.291287+0.003296j
[2025-08-27 18:18:20] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -86.224050-0.000279j
[2025-08-27 18:18:41] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -86.206053+0.000448j
[2025-08-27 18:19:01] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -86.201334-0.006725j
[2025-08-27 18:19:22] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -86.271359-0.004462j
[2025-08-27 18:19:43] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -86.136125+0.002881j
[2025-08-27 18:20:03] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -86.224578+0.000439j
[2025-08-27 18:20:24] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -86.192867-0.002282j
[2025-08-27 18:20:45] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -86.382742-0.004677j
[2025-08-27 18:21:05] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -86.489305+0.002168j
[2025-08-27 18:21:26] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -86.443505+0.000846j
[2025-08-27 18:21:47] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -86.350980-0.009633j
[2025-08-27 18:22:07] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -86.426515-0.001809j
[2025-08-27 18:22:28] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -86.359456+0.003023j
[2025-08-27 18:22:49] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -86.477162+0.001203j
[2025-08-27 18:23:09] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -86.453855+0.000466j
[2025-08-27 18:23:30] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -86.349223+0.002941j
[2025-08-27 18:23:51] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -86.229292+0.002817j
[2025-08-27 18:24:11] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -86.114154-0.004628j
[2025-08-27 18:24:32] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -86.046665-0.001126j
[2025-08-27 18:24:53] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -86.222741+0.010946j
[2025-08-27 18:25:13] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -86.199801+0.003625j
[2025-08-27 18:25:34] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -86.107820-0.002221j
[2025-08-27 18:25:55] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -86.138012+0.001799j
[2025-08-27 18:26:15] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -86.097367-0.005991j
[2025-08-27 18:26:36] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -86.147447+0.005714j
[2025-08-27 18:26:57] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -86.152671-0.002801j
[2025-08-27 18:27:17] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -86.322788+0.007550j
[2025-08-27 18:27:38] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -86.314196+0.006166j
[2025-08-27 18:27:59] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -86.266807+0.003955j
[2025-08-27 18:28:20] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -86.199873-0.001850j
[2025-08-27 18:28:40] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -86.230800+0.000516j
[2025-08-27 18:29:01] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -86.241835+0.002176j
[2025-08-27 18:29:21] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -86.108289-0.008679j
[2025-08-27 18:29:22] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 18:29:42] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -86.250345-0.003011j
[2025-08-27 18:30:03] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -86.305241+0.001969j
[2025-08-27 18:30:24] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -86.267176-0.000984j
[2025-08-27 18:30:44] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -86.264394-0.003058j
[2025-08-27 18:31:05] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -86.287802+0.006494j
[2025-08-27 18:31:26] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -86.185162-0.004237j
[2025-08-27 18:31:46] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -86.277267+0.006398j
[2025-08-27 18:32:07] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -86.211643+0.004322j
[2025-08-27 18:32:28] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -86.216862-0.002484j
[2025-08-27 18:32:49] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -86.201781+0.004094j
[2025-08-27 18:33:09] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -86.200041-0.000846j
[2025-08-27 18:33:30] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -86.285864-0.005482j
[2025-08-27 18:33:51] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -86.320917+0.006893j
[2025-08-27 18:34:11] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -86.369405+0.006706j
[2025-08-27 18:34:32] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -86.314334+0.001980j
[2025-08-27 18:34:53] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -86.200885-0.001214j
[2025-08-27 18:35:13] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -86.134669+0.006702j
[2025-08-27 18:35:34] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -86.205764+0.000820j
[2025-08-27 18:35:55] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -86.153886+0.005483j
[2025-08-27 18:36:15] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -86.151565-0.001181j
[2025-08-27 18:36:36] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -86.047307+0.009279j
[2025-08-27 18:36:57] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -86.081483+0.004216j
[2025-08-27 18:37:17] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -86.136705+0.002018j
[2025-08-27 18:37:38] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -86.163092+0.010098j
[2025-08-27 18:37:59] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -86.192221+0.000259j
[2025-08-27 18:38:19] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -86.185633+0.012735j
[2025-08-27 18:38:40] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -86.124699+0.002444j
[2025-08-27 18:39:01] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -86.091359+0.004697j
[2025-08-27 18:39:21] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -86.003842-0.003794j
[2025-08-27 18:39:42] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -86.050380+0.002270j
[2025-08-27 18:40:03] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -85.986671+0.000840j
[2025-08-27 18:40:23] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -86.200547-0.001785j
[2025-08-27 18:40:44] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -86.193994+0.001020j
[2025-08-27 18:41:05] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -86.199603-0.006002j
[2025-08-27 18:41:25] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -86.121028+0.003149j
[2025-08-27 18:41:46] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -86.143882+0.002778j
[2025-08-27 18:42:07] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -86.153217+0.000755j
[2025-08-27 18:42:27] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -86.142635-0.001587j
[2025-08-27 18:42:48] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -86.357399-0.002027j
[2025-08-27 18:43:09] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -86.398351+0.005199j
[2025-08-27 18:43:29] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -86.293064-0.000964j
[2025-08-27 18:43:50] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -86.264064-0.006060j
[2025-08-27 18:44:11] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -86.327580-0.005644j
[2025-08-27 18:44:31] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -86.369476+0.008198j
[2025-08-27 18:44:52] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -86.253276-0.008527j
[2025-08-27 18:45:13] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -86.378223-0.002298j
[2025-08-27 18:45:33] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -86.161928-0.001804j
[2025-08-27 18:45:54] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -86.286828+0.001634j
[2025-08-27 18:46:15] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -86.228354+0.005870j
[2025-08-27 18:46:35] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -86.202758-0.001606j
[2025-08-27 18:46:35] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 18:46:57] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -86.190684-0.001415j
[2025-08-27 18:47:17] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -86.205742-0.001323j
[2025-08-27 18:47:38] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -86.240500-0.012743j
[2025-08-27 18:47:59] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -86.074927-0.011024j
[2025-08-27 18:48:19] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -86.004938-0.011400j
[2025-08-27 18:48:40] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -86.026983-0.001589j
[2025-08-27 18:49:01] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -86.059279-0.002387j
[2025-08-27 18:49:21] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -86.309288-0.001335j
[2025-08-27 18:49:42] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -86.113610+0.002642j
[2025-08-27 18:50:03] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -86.056273-0.002695j
[2025-08-27 18:50:23] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -86.063486-0.006498j
[2025-08-27 18:50:44] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -86.086244-0.005499j
[2025-08-27 18:51:05] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -86.206110+0.000011j
[2025-08-27 18:51:25] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -86.192986-0.004150j
[2025-08-27 18:51:46] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -86.201575+0.001458j
[2025-08-27 18:52:07] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -86.215707-0.003233j
[2025-08-27 18:52:28] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -86.267772-0.005005j
[2025-08-27 18:52:48] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -86.275108-0.000737j
[2025-08-27 18:53:09] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -86.302815-0.005511j
[2025-08-27 18:53:30] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -86.453490+0.000851j
[2025-08-27 18:53:50] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -86.375645-0.011155j
[2025-08-27 18:54:11] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -86.276411-0.003229j
[2025-08-27 18:54:32] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -86.249996-0.005900j
[2025-08-27 18:54:52] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -86.341826-0.004143j
[2025-08-27 18:55:13] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -86.074194-0.009197j
[2025-08-27 18:55:34] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -86.182229+0.005343j
[2025-08-27 18:55:54] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -86.280031-0.000401j
[2025-08-27 18:56:15] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -86.220819+0.008263j
[2025-08-27 18:56:36] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -86.179103-0.007022j
[2025-08-27 18:56:56] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -86.195787-0.005908j
[2025-08-27 18:57:17] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -86.187684-0.000584j
[2025-08-27 18:57:38] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -86.283093-0.008172j
[2025-08-27 18:57:58] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -86.218296+0.002717j
[2025-08-27 18:58:19] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -86.234159-0.002456j
[2025-08-27 18:58:40] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -86.270359+0.002354j
[2025-08-27 18:59:00] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -86.333578-0.002276j
[2025-08-27 18:59:21] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -86.219842+0.004688j
[2025-08-27 18:59:42] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -85.948727+0.003110j
[2025-08-27 19:00:02] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -86.120162-0.003636j
[2025-08-27 19:00:23] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -86.137194-0.002878j
[2025-08-27 19:00:43] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -86.158172+0.002034j
[2025-08-27 19:01:04] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -86.148484+0.002699j
[2025-08-27 19:01:25] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -86.214154-0.000350j
[2025-08-27 19:01:45] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -86.263905+0.005461j
[2025-08-27 19:02:06] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -86.256354+0.007052j
[2025-08-27 19:02:27] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -86.187525-0.001090j
[2025-08-27 19:02:47] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -86.272376+0.000841j
[2025-08-27 19:03:08] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -86.197765+0.002503j
[2025-08-27 19:03:29] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -86.229555+0.004907j
[2025-08-27 19:03:49] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -86.339412-0.002045j
[2025-08-27 19:03:49] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 19:04:10] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -86.244947-0.005585j
[2025-08-27 19:04:31] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -86.156594+0.003023j
[2025-08-27 19:04:51] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -86.178361+0.002789j
[2025-08-27 19:05:12] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -86.150126+0.012162j
[2025-08-27 19:05:33] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -86.207659+0.002202j
[2025-08-27 19:05:53] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -86.365855+0.005206j
[2025-08-27 19:06:14] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -86.187487+0.004682j
[2025-08-27 19:06:35] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -86.274711-0.004566j
[2025-08-27 19:06:55] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -86.217469-0.001021j
[2025-08-27 19:07:16] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -86.244131-0.000537j
[2025-08-27 19:07:37] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -86.197759+0.003371j
[2025-08-27 19:07:57] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -86.273645+0.008504j
[2025-08-27 19:08:18] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -86.122485+0.001000j
[2025-08-27 19:08:39] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -86.000663-0.006714j
[2025-08-27 19:08:59] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -85.896575+0.001694j
[2025-08-27 19:09:20] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -85.965311-0.001949j
[2025-08-27 19:09:41] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -85.922905-0.002985j
[2025-08-27 19:10:02] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -85.974610+0.003732j
[2025-08-27 19:10:22] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -86.003640+0.000605j
[2025-08-27 19:10:43] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -86.213340+0.006180j
[2025-08-27 19:11:04] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -86.109570-0.001702j
[2025-08-27 19:11:24] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -86.301447+0.008023j
[2025-08-27 19:11:45] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -86.167795-0.001537j
[2025-08-27 19:12:06] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -86.170062+0.002430j
[2025-08-27 19:12:26] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -86.151358-0.007950j
[2025-08-27 19:12:47] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -86.146906+0.007068j
[2025-08-27 19:13:08] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -86.144191-0.000363j
[2025-08-27 19:13:28] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -86.135885+0.006210j
[2025-08-27 19:13:49] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -86.026457-0.001131j
[2025-08-27 19:14:10] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -85.982293+0.010567j
[2025-08-27 19:14:30] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -86.200752-0.008611j
[2025-08-27 19:14:51] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -86.284529-0.001046j
[2025-08-27 19:15:12] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -86.198899-0.003834j
[2025-08-27 19:15:32] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -86.278565-0.007265j
[2025-08-27 19:15:53] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -86.258575-0.005865j
[2025-08-27 19:16:14] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -86.176494-0.000114j
[2025-08-27 19:16:34] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -86.287551+0.005239j
[2025-08-27 19:16:55] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -86.231577-0.000989j
[2025-08-27 19:17:16] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -86.255775-0.006671j
[2025-08-27 19:17:36] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -86.155177+0.002290j
[2025-08-27 19:17:57] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -86.320446-0.002171j
[2025-08-27 19:18:18] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -86.263515+0.011614j
[2025-08-27 19:18:39] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -86.422912+0.007245j
[2025-08-27 19:18:59] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -86.322045-0.004480j
[2025-08-27 19:19:20] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -86.159796+0.002747j
[2025-08-27 19:19:41] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -86.127402-0.014692j
[2025-08-27 19:20:01] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -86.237740-0.004881j
[2025-08-27 19:20:22] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -86.035833+0.004542j
[2025-08-27 19:20:42] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -86.139369-0.000406j
[2025-08-27 19:21:03] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -86.044979+0.004011j
[2025-08-27 19:21:03] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 19:21:24] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -86.125922+0.002949j
[2025-08-27 19:21:45] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -86.067260+0.000537j
[2025-08-27 19:22:05] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -86.093064+0.007494j
[2025-08-27 19:22:26] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -86.309635-0.005361j
[2025-08-27 19:22:47] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -86.245488-0.002151j
[2025-08-27 19:23:07] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -86.209892+0.001727j
[2025-08-27 19:23:28] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -85.972626+0.000304j
[2025-08-27 19:23:49] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -86.113044-0.005137j
[2025-08-27 19:24:09] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -86.159414+0.001356j
[2025-08-27 19:24:30] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -86.072667-0.003713j
[2025-08-27 19:24:50] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -86.082304-0.004625j
[2025-08-27 19:25:11] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -86.048356-0.007328j
[2025-08-27 19:25:32] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -86.110324+0.003619j
[2025-08-27 19:25:53] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -86.237905-0.005860j
[2025-08-27 19:26:13] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -86.022159-0.004920j
[2025-08-27 19:26:34] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -86.091367+0.006211j
[2025-08-27 19:26:55] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -86.265283-0.001112j
[2025-08-27 19:27:15] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -86.144089+0.000307j
[2025-08-27 19:27:36] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -86.447315+0.006571j
[2025-08-27 19:27:57] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -86.433642+0.003792j
[2025-08-27 19:28:17] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -86.093010+0.008013j
[2025-08-27 19:28:38] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -86.166958-0.000418j
[2025-08-27 19:28:59] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -86.085540-0.004261j
[2025-08-27 19:29:19] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -86.141100-0.002788j
[2025-08-27 19:29:40] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -85.996708+0.001945j
[2025-08-27 19:30:01] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -85.991742+0.004669j
[2025-08-27 19:30:21] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -86.082285+0.000808j
[2025-08-27 19:30:42] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -86.300242+0.000281j
[2025-08-27 19:31:03] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -86.238713+0.005116j
[2025-08-27 19:31:23] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -86.257845+0.009518j
[2025-08-27 19:31:44] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -86.249727+0.001813j
[2025-08-27 19:32:05] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -86.202869-0.002768j
[2025-08-27 19:32:25] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -86.212593+0.000288j
[2025-08-27 19:32:46] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -86.148391+0.001768j
[2025-08-27 19:33:07] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -86.273440+0.005998j
[2025-08-27 19:33:28] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -86.276559-0.001349j
[2025-08-27 19:33:48] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -86.199591+0.005115j
[2025-08-27 19:34:09] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -86.140764-0.001410j
[2025-08-27 19:34:30] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -86.252612-0.009882j
[2025-08-27 19:34:50] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -86.095343-0.010176j
[2025-08-27 19:35:11] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -86.168711+0.008053j
[2025-08-27 19:35:32] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -86.123446+0.000235j
[2025-08-27 19:35:52] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -86.089229-0.001286j
[2025-08-27 19:36:13] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -86.021918-0.006605j
[2025-08-27 19:36:34] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -86.130173-0.003376j
[2025-08-27 19:36:54] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -85.986921+0.004889j
[2025-08-27 19:37:05] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -86.049638-0.009923j
[2025-08-27 19:37:14] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -85.973034+0.005576j
[2025-08-27 19:37:24] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -86.194032+0.002907j
[2025-08-27 19:37:33] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -86.085151-0.002514j
[2025-08-27 19:37:33] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 19:37:33] ✅ Training completed | Restarts: 1
[2025-08-27 19:37:33] ============================================================
[2025-08-27 19:37:33] Training completed | Runtime: 9308.7s
[2025-08-27 19:37:37] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 19:37:37] ============================================================
