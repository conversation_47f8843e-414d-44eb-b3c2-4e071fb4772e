[2025-08-27 17:02:14] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-08-27 17:02:14]   - 迭代次数: final
[2025-08-27 17:02:14]   - 能量: -84.113107+0.006542j ± 0.107994
[2025-08-27 17:02:14]   - 时间戳: 2025-08-27T17:02:01.313908+08:00
[2025-08-27 17:02:24] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 17:02:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 17:02:24] ==================================================
[2025-08-27 17:02:24] GCNN for Shastry-Sutherland Model
[2025-08-27 17:02:24] ==================================================
[2025-08-27 17:02:24] System parameters:
[2025-08-27 17:02:24]   - System size: L=5, N=100
[2025-08-27 17:02:24]   - System parameters: J1=0.02, J2=0.0, Q=1.0
[2025-08-27 17:02:24] --------------------------------------------------
[2025-08-27 17:02:24] Model parameters:
[2025-08-27 17:02:24]   - Number of layers = 4
[2025-08-27 17:02:24]   - Number of features = 4
[2025-08-27 17:02:24]   - Total parameters = 19628
[2025-08-27 17:02:24] --------------------------------------------------
[2025-08-27 17:02:24] Training parameters:
[2025-08-27 17:02:24]   - Learning rate: 0.015
[2025-08-27 17:02:24]   - Total iterations: 450
[2025-08-27 17:02:24]   - Annealing cycles: 2
[2025-08-27 17:02:24]   - Initial period: 150
[2025-08-27 17:02:24]   - Period multiplier: 2.0
[2025-08-27 17:02:24]   - Temperature range: 0.0-1.0
[2025-08-27 17:02:24]   - Samples: 4096
[2025-08-27 17:02:24]   - Discarded samples: 0
[2025-08-27 17:02:24]   - Chunk size: 2048
[2025-08-27 17:02:24]   - Diagonal shift: 0.2
[2025-08-27 17:02:24]   - Gradient clipping: 1.0
[2025-08-27 17:02:24]   - Checkpoint enabled: interval=50
[2025-08-27 17:02:24]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.02/training/checkpoints
[2025-08-27 17:02:24] --------------------------------------------------
[2025-08-27 17:02:24] Device status:
[2025-08-27 17:02:24]   - Devices model: NVIDIA H200 NVL
[2025-08-27 17:02:24]   - Number of devices: 1
[2025-08-27 17:02:24]   - Sharding: True
[2025-08-27 17:02:24] ============================================================
[2025-08-27 17:03:16] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -83.891659-0.005433j
[2025-08-27 17:03:54] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -83.804175+0.022811j
[2025-08-27 17:04:14] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -83.788398+0.012793j
[2025-08-27 17:04:35] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -84.086862-0.001699j
[2025-08-27 17:04:55] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -83.955507+0.004600j
[2025-08-27 17:05:16] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -83.889443+0.004440j
[2025-08-27 17:05:36] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -83.820599+0.003985j
[2025-08-27 17:05:57] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -83.832677+0.006574j
[2025-08-27 17:06:17] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -83.880737+0.005847j
[2025-08-27 17:06:38] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -83.760649-0.000999j
[2025-08-27 17:06:58] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -83.817960+0.003458j
[2025-08-27 17:07:19] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -83.793198-0.002559j
[2025-08-27 17:07:39] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -83.925426+0.002948j
[2025-08-27 17:07:59] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -83.880121+0.002392j
[2025-08-27 17:08:20] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -83.562420-0.003497j
[2025-08-27 17:08:40] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -83.776742-0.003965j
[2025-08-27 17:09:01] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -83.668475-0.005062j
[2025-08-27 17:09:21] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -83.638888+0.007235j
[2025-08-27 17:09:42] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -83.594414+0.004217j
[2025-08-27 17:10:03] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -83.668276-0.003593j
[2025-08-27 17:10:23] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -83.698731-0.002897j
[2025-08-27 17:10:44] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -83.760385-0.013715j
[2025-08-27 17:11:04] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -83.789877+0.000377j
[2025-08-27 17:11:25] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -83.852858-0.004587j
[2025-08-27 17:11:45] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -83.940630+0.000630j
[2025-08-27 17:12:06] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -83.796237-0.004904j
[2025-08-27 17:12:26] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -83.826598-0.002895j
[2025-08-27 17:12:46] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -83.716411-0.000579j
[2025-08-27 17:13:07] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -83.720537-0.004182j
[2025-08-27 17:13:27] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -83.755863-0.003528j
[2025-08-27 17:13:48] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -83.764728-0.001638j
[2025-08-27 17:14:08] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -83.678782-0.006127j
[2025-08-27 17:14:29] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -83.772098-0.005944j
[2025-08-27 17:14:49] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -83.674449+0.007675j
[2025-08-27 17:15:10] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -83.829835+0.001856j
[2025-08-27 17:15:30] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -83.863036+0.010661j
[2025-08-27 17:15:51] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -83.703235+0.000642j
[2025-08-27 17:16:11] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -83.727223-0.009022j
[2025-08-27 17:16:32] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -83.775286+0.002184j
[2025-08-27 17:16:52] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -83.727020-0.000736j
[2025-08-27 17:17:13] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -83.672737-0.002235j
[2025-08-27 17:17:33] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -83.596998-0.004902j
[2025-08-27 17:17:53] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -83.701822-0.003035j
[2025-08-27 17:18:14] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -83.751011+0.007042j
[2025-08-27 17:18:34] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -83.791286-0.011441j
[2025-08-27 17:18:55] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -83.653648-0.008841j
[2025-08-27 17:19:15] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -83.640779+0.000735j
[2025-08-27 17:19:36] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -83.729537-0.009168j
[2025-08-27 17:19:56] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -83.630599+0.010415j
[2025-08-27 17:20:17] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -83.718406+0.004650j
[2025-08-27 17:20:17] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 17:20:37] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -83.690019+0.000270j
[2025-08-27 17:20:58] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -83.884590+0.002316j
[2025-08-27 17:21:18] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -83.719529-0.012534j
[2025-08-27 17:21:39] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -83.844473+0.002810j
[2025-08-27 17:21:59] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -83.636709-0.008919j
[2025-08-27 17:22:20] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -83.865508-0.000923j
[2025-08-27 17:22:40] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -83.843178-0.006530j
[2025-08-27 17:23:01] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -83.816834-0.004828j
[2025-08-27 17:23:21] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -83.862340-0.003282j
[2025-08-27 17:23:42] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -83.916508+0.001271j
[2025-08-27 17:24:02] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -83.934008+0.001160j
[2025-08-27 17:24:23] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -83.846897+0.000790j
[2025-08-27 17:24:43] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -83.884914+0.001714j
[2025-08-27 17:25:04] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -83.975102+0.004696j
[2025-08-27 17:25:24] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -83.860130+0.001382j
[2025-08-27 17:25:44] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -83.779655+0.001273j
[2025-08-27 17:26:05] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -83.786875+0.001723j
[2025-08-27 17:26:25] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -83.818441+0.002582j
[2025-08-27 17:26:46] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -83.752268-0.005667j
[2025-08-27 17:27:06] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -83.754567+0.001693j
[2025-08-27 17:27:27] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -83.927206+0.001101j
[2025-08-27 17:27:47] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -83.899994-0.000137j
[2025-08-27 17:28:08] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -83.938889+0.007766j
[2025-08-27 17:28:28] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -84.015895+0.007464j
[2025-08-27 17:28:49] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -83.923968-0.003270j
[2025-08-27 17:29:09] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -83.955615-0.006144j
[2025-08-27 17:29:30] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -83.868434-0.011059j
[2025-08-27 17:29:50] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -83.820961+0.000993j
[2025-08-27 17:30:11] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -83.815622+0.010985j
[2025-08-27 17:30:31] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -83.844311-0.001472j
[2025-08-27 17:30:52] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -83.764657+0.002822j
[2025-08-27 17:31:12] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -83.787459+0.005398j
[2025-08-27 17:31:32] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -83.815146-0.005394j
[2025-08-27 17:31:53] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -83.642298+0.000841j
[2025-08-27 17:32:13] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -83.697232+0.011899j
[2025-08-27 17:32:34] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -83.725502-0.008335j
[2025-08-27 17:32:54] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -83.819702-0.009029j
[2025-08-27 17:33:15] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -83.752322+0.006028j
[2025-08-27 17:33:35] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -83.753842+0.004489j
[2025-08-27 17:33:56] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -83.775541+0.000260j
[2025-08-27 17:34:16] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -83.664148-0.005865j
[2025-08-27 17:34:37] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -83.732780+0.004874j
[2025-08-27 17:34:57] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -83.566311+0.003529j
[2025-08-27 17:35:18] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -83.589467+0.001874j
[2025-08-27 17:35:38] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -83.711587+0.003745j
[2025-08-27 17:35:59] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -83.535380+0.004128j
[2025-08-27 17:36:19] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -83.638283-0.004211j
[2025-08-27 17:36:40] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -83.717083-0.005037j
[2025-08-27 17:37:00] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -83.542573+0.003378j
[2025-08-27 17:37:21] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -83.746141-0.000394j
[2025-08-27 17:37:21] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 17:37:41] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -83.748911-0.001115j
[2025-08-27 17:38:02] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -83.716364-0.010308j
[2025-08-27 17:38:22] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -83.651759+0.003826j
[2025-08-27 17:38:43] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -83.532375-0.005860j
[2025-08-27 17:39:03] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -83.614077+0.000662j
[2025-08-27 17:39:24] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -83.603416-0.006023j
[2025-08-27 17:39:44] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -83.674374-0.005155j
[2025-08-27 17:40:05] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -83.744988-0.006017j
[2025-08-27 17:40:25] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -83.625172-0.006347j
[2025-08-27 17:40:46] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -83.623870+0.003383j
[2025-08-27 17:41:06] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -83.569395-0.003571j
[2025-08-27 17:41:27] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -83.554424+0.012781j
[2025-08-27 17:41:47] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -83.667195+0.001279j
[2025-08-27 17:42:08] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -83.638804+0.006067j
[2025-08-27 17:42:28] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -83.550622+0.004124j
[2025-08-27 17:42:49] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -83.626304+0.001140j
[2025-08-27 17:43:09] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -83.756020+0.000293j
[2025-08-27 17:43:30] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -83.485436+0.003282j
[2025-08-27 17:43:50] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -83.676530+0.002475j
[2025-08-27 17:44:11] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -83.700036+0.009517j
[2025-08-27 17:44:31] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -83.712420-0.003387j
[2025-08-27 17:44:51] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -83.746400-0.002221j
[2025-08-27 17:45:12] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -83.713250-0.008729j
[2025-08-27 17:45:32] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -83.585037+0.014555j
[2025-08-27 17:45:53] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -83.590291-0.000436j
[2025-08-27 17:46:13] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -83.703392+0.003855j
[2025-08-27 17:46:34] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -83.664861-0.010234j
[2025-08-27 17:46:54] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -83.738202+0.010953j
[2025-08-27 17:47:15] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -83.553881-0.001360j
[2025-08-27 17:47:35] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -83.689529-0.004637j
[2025-08-27 17:47:56] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -83.660630+0.005837j
[2025-08-27 17:48:16] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -83.700708-0.000195j
[2025-08-27 17:48:37] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -83.632264-0.005987j
[2025-08-27 17:48:57] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -83.623756+0.000210j
[2025-08-27 17:49:18] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -83.643309-0.010359j
[2025-08-27 17:49:38] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -83.726248-0.008781j
[2025-08-27 17:49:59] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -83.742664+0.000593j
[2025-08-27 17:50:19] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -83.755188-0.005292j
[2025-08-27 17:50:40] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -83.656237-0.002004j
[2025-08-27 17:51:00] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -83.643929+0.005269j
[2025-08-27 17:51:21] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -83.756913-0.002440j
[2025-08-27 17:51:42] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -83.862301-0.007937j
[2025-08-27 17:52:02] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -83.927508+0.002102j
[2025-08-27 17:52:23] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -83.796666+0.003202j
[2025-08-27 17:52:43] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -83.885928-0.005020j
[2025-08-27 17:53:04] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -83.813800-0.006915j
[2025-08-27 17:53:24] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -83.844168-0.001050j
[2025-08-27 17:53:45] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -83.660914+0.000921j
[2025-08-27 17:54:05] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -83.518790-0.004477j
[2025-08-27 17:54:26] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -83.683945-0.001156j
[2025-08-27 17:54:26] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 17:54:26] RESTART #1 | Period: 300
[2025-08-27 17:54:46] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -83.701809+0.010457j
[2025-08-27 17:55:07] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -83.796389+0.008994j
[2025-08-27 17:55:27] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -83.715933-0.000634j
[2025-08-27 17:55:48] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -83.721629-0.000034j
[2025-08-27 17:56:08] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -83.663768-0.004686j
[2025-08-27 17:56:29] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -83.622487-0.002244j
[2025-08-27 17:56:49] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -83.714564+0.000810j
[2025-08-27 17:57:10] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -83.636485+0.001484j
[2025-08-27 17:57:30] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -83.518134-0.008954j
[2025-08-27 17:57:51] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -83.570681-0.001731j
[2025-08-27 17:58:11] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -83.593811-0.005426j
[2025-08-27 17:58:32] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -83.450724+0.003951j
[2025-08-27 17:58:52] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -83.684095+0.001932j
[2025-08-27 17:59:13] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -83.590862+0.002887j
[2025-08-27 17:59:33] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -83.774388-0.007058j
[2025-08-27 17:59:53] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -83.949227+0.000023j
[2025-08-27 18:00:14] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -83.914818+0.003125j
[2025-08-27 18:00:34] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -83.841264+0.001500j
[2025-08-27 18:00:55] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -83.818042+0.000814j
[2025-08-27 18:01:15] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -83.775969-0.012608j
[2025-08-27 18:01:36] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -83.720492-0.004582j
[2025-08-27 18:01:56] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -83.713876+0.002844j
[2025-08-27 18:02:17] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -83.728594-0.001916j
[2025-08-27 18:02:37] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -83.879178+0.003643j
[2025-08-27 18:02:58] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -83.754894-0.006461j
[2025-08-27 18:03:18] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -83.785636+0.008746j
[2025-08-27 18:03:39] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -83.908593+0.000356j
[2025-08-27 18:03:59] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -83.728777-0.000438j
[2025-08-27 18:04:20] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -83.696344+0.001836j
[2025-08-27 18:04:40] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -83.693326-0.004038j
[2025-08-27 18:05:01] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -83.830875+0.002746j
[2025-08-27 18:05:21] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -83.759951+0.000691j
[2025-08-27 18:05:42] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -83.823123-0.001112j
[2025-08-27 18:06:02] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -83.834608+0.001296j
[2025-08-27 18:06:22] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -83.751225+0.000774j
[2025-08-27 18:06:43] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -83.763389-0.007038j
[2025-08-27 18:07:03] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -83.791033-0.003167j
[2025-08-27 18:07:24] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -83.852593+0.007082j
[2025-08-27 18:07:44] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -83.823905+0.002632j
[2025-08-27 18:08:05] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -83.776859+0.005692j
[2025-08-27 18:08:25] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -83.824225-0.007612j
[2025-08-27 18:08:46] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -83.815727-0.005539j
[2025-08-27 18:09:06] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -83.819999+0.004161j
[2025-08-27 18:09:27] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -83.853668-0.001631j
[2025-08-27 18:09:47] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -83.849803-0.000289j
[2025-08-27 18:10:08] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -83.806198+0.001944j
[2025-08-27 18:10:28] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -83.695901+0.000627j
[2025-08-27 18:10:49] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -83.785750+0.002042j
[2025-08-27 18:11:09] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -83.711708+0.003137j
[2025-08-27 18:11:30] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -83.930222-0.000823j
[2025-08-27 18:11:30] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 18:11:50] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -83.893528+0.006558j
[2025-08-27 18:12:10] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -83.839440-0.004279j
[2025-08-27 18:12:31] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -83.807001-0.002494j
[2025-08-27 18:12:51] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -83.933825+0.003459j
[2025-08-27 18:13:12] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -83.877320+0.006292j
[2025-08-27 18:13:32] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -83.854798+0.008359j
[2025-08-27 18:13:53] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -83.664454+0.002426j
[2025-08-27 18:14:13] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -83.721564+0.006305j
[2025-08-27 18:14:34] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -83.683333-0.002681j
[2025-08-27 18:14:54] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -83.596992-0.005729j
[2025-08-27 18:15:15] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -83.628109+0.002915j
[2025-08-27 18:15:35] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -83.717338-0.001442j
[2025-08-27 18:15:56] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -83.686626+0.001194j
[2025-08-27 18:16:16] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -83.755722-0.002534j
[2025-08-27 18:16:37] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -83.747254-0.007664j
[2025-08-27 18:16:57] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -83.668846+0.010682j
[2025-08-27 18:17:18] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -83.681586-0.004238j
[2025-08-27 18:17:38] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -83.789668-0.018684j
[2025-08-27 18:17:59] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -83.653443+0.000085j
[2025-08-27 18:18:19] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -83.741362+0.006109j
[2025-08-27 18:18:40] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -83.877264-0.002866j
[2025-08-27 18:19:00] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -83.953832+0.000789j
[2025-08-27 18:19:21] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -83.855434-0.012404j
[2025-08-27 18:19:41] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -83.801984+0.001524j
[2025-08-27 18:20:02] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -83.727049-0.016337j
[2025-08-27 18:20:22] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -83.875676-0.004584j
[2025-08-27 18:20:43] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -83.912595+0.012495j
[2025-08-27 18:21:03] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -83.937732+0.003461j
[2025-08-27 18:21:23] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -83.887602-0.003329j
[2025-08-27 18:21:44] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -83.740652+0.000387j
[2025-08-27 18:22:04] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -83.726930-0.003339j
[2025-08-27 18:22:25] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -83.834573-0.000460j
[2025-08-27 18:22:45] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -83.593722-0.000524j
[2025-08-27 18:23:06] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -83.722340-0.008409j
[2025-08-27 18:23:26] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -83.542989-0.003992j
[2025-08-27 18:23:47] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -83.627153+0.009519j
[2025-08-27 18:24:07] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -83.655503+0.005524j
[2025-08-27 18:24:28] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -83.643398-0.004242j
[2025-08-27 18:24:48] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -83.570987+0.003256j
[2025-08-27 18:25:09] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -83.698199+0.003084j
[2025-08-27 18:25:29] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -83.793673-0.003997j
[2025-08-27 18:25:50] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -83.804674+0.009288j
[2025-08-27 18:26:10] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -83.841094-0.005390j
[2025-08-27 18:26:31] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -83.845133+0.000920j
[2025-08-27 18:26:51] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -83.966024+0.003257j
[2025-08-27 18:27:12] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -83.846040-0.009561j
[2025-08-27 18:27:32] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -83.762282+0.001874j
[2025-08-27 18:27:52] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -83.674706-0.008681j
[2025-08-27 18:28:13] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -83.682690+0.001289j
[2025-08-27 18:28:33] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -83.616322+0.003673j
[2025-08-27 18:28:34] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 18:28:54] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -83.784580+0.005265j
[2025-08-27 18:29:15] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -83.830497-0.012193j
[2025-08-27 18:29:35] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -83.736511-0.000437j
[2025-08-27 18:29:55] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -83.676205+0.004660j
[2025-08-27 18:30:16] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -83.801068+0.010574j
[2025-08-27 18:30:36] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -83.753691+0.001915j
[2025-08-27 18:30:57] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -83.633545-0.000559j
[2025-08-27 18:31:17] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -83.643028-0.011130j
[2025-08-27 18:31:38] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -83.477279+0.006099j
[2025-08-27 18:31:58] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -83.696940-0.001268j
[2025-08-27 18:32:19] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -83.786458+0.004816j
[2025-08-27 18:32:39] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -83.711332-0.009890j
[2025-08-27 18:33:00] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -83.728789-0.011180j
[2025-08-27 18:33:20] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -83.692442-0.000415j
[2025-08-27 18:33:41] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -83.621053-0.000493j
[2025-08-27 18:34:01] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -83.559755-0.003076j
[2025-08-27 18:34:22] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -83.622065+0.001932j
[2025-08-27 18:34:42] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -83.830027+0.001416j
[2025-08-27 18:35:03] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -83.698333+0.000725j
[2025-08-27 18:35:23] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -83.719818+0.004268j
[2025-08-27 18:35:44] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -83.757935+0.002002j
[2025-08-27 18:36:04] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -83.748078+0.008240j
[2025-08-27 18:36:24] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -83.678401+0.003023j
[2025-08-27 18:36:45] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -83.665264-0.002817j
[2025-08-27 18:37:05] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -83.690311-0.000114j
[2025-08-27 18:37:26] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -83.679794-0.007251j
[2025-08-27 18:37:46] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -83.723997-0.003515j
[2025-08-27 18:38:07] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -83.815276-0.000114j
[2025-08-27 18:38:27] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -83.654978-0.006601j
[2025-08-27 18:38:48] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -83.853076+0.005767j
[2025-08-27 18:39:08] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -83.727360+0.003486j
[2025-08-27 18:39:29] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -83.591723+0.004981j
[2025-08-27 18:39:49] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -83.573699+0.000411j
[2025-08-27 18:40:10] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -83.596502+0.008217j
[2025-08-27 18:40:30] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -83.645993-0.004349j
[2025-08-27 18:40:51] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -83.918513-0.003898j
[2025-08-27 18:41:11] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -83.735658+0.003957j
[2025-08-27 18:41:32] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -83.878950+0.000631j
[2025-08-27 18:41:52] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -84.066911+0.000029j
[2025-08-27 18:42:13] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -83.958602-0.002278j
[2025-08-27 18:42:33] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -83.821551+0.002629j
[2025-08-27 18:42:54] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -83.586969-0.004541j
[2025-08-27 18:43:14] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -83.681678-0.002281j
[2025-08-27 18:43:34] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -83.824547+0.007837j
[2025-08-27 18:43:55] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -83.817447-0.000991j
[2025-08-27 18:44:16] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -83.840338+0.003166j
[2025-08-27 18:44:36] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -83.764409-0.002333j
[2025-08-27 18:44:56] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -83.785580+0.004255j
[2025-08-27 18:45:17] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -83.627273-0.003616j
[2025-08-27 18:45:37] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -83.746341-0.010872j
[2025-08-27 18:45:38] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 18:45:58] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -83.698662-0.004297j
[2025-08-27 18:46:18] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -83.760731-0.001079j
[2025-08-27 18:46:38] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -83.826123-0.001650j
[2025-08-27 18:46:58] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -83.791774+0.001050j
[2025-08-27 18:47:19] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -83.876940-0.004121j
[2025-08-27 18:47:39] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -83.715135+0.010250j
[2025-08-27 18:48:00] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -83.755607+0.006106j
[2025-08-27 18:48:20] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -83.715825-0.009441j
[2025-08-27 18:48:41] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -83.731789-0.001726j
[2025-08-27 18:49:01] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -83.849953-0.008625j
[2025-08-27 18:49:22] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -83.717480-0.001813j
[2025-08-27 18:49:42] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -83.748666+0.007773j
[2025-08-27 18:50:03] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -83.965558+0.003713j
[2025-08-27 18:50:23] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -83.929500+0.004595j
[2025-08-27 18:50:44] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -83.949595+0.003309j
[2025-08-27 18:51:04] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -83.812819-0.005203j
[2025-08-27 18:51:25] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -83.779757+0.003292j
[2025-08-27 18:51:45] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -83.883132-0.003287j
[2025-08-27 18:52:06] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -83.663946-0.001024j
[2025-08-27 18:52:26] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -83.791233+0.005042j
[2025-08-27 18:52:47] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -83.698794-0.003329j
[2025-08-27 18:53:07] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -83.741284+0.001638j
[2025-08-27 18:53:28] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -83.690626-0.009648j
[2025-08-27 18:53:48] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -83.673163-0.012595j
[2025-08-27 18:54:08] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -83.797297-0.006174j
[2025-08-27 18:54:29] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -83.794047-0.008681j
[2025-08-27 18:54:49] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -83.851640-0.000007j
[2025-08-27 18:55:10] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -83.836186-0.006172j
[2025-08-27 18:55:30] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -83.752495+0.005359j
[2025-08-27 18:55:51] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -83.688954+0.006594j
[2025-08-27 18:56:11] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -83.673365-0.009553j
[2025-08-27 18:56:32] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -83.610473-0.000500j
[2025-08-27 18:56:52] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -83.761715+0.002110j
[2025-08-27 18:57:13] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -83.576023+0.006603j
[2025-08-27 18:57:33] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -83.552992+0.002458j
[2025-08-27 18:57:54] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -83.446964-0.002538j
[2025-08-27 18:58:14] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -83.488615-0.004440j
[2025-08-27 18:58:35] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -83.597186+0.006483j
[2025-08-27 18:58:55] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -83.604375+0.003778j
[2025-08-27 18:59:16] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -83.591020+0.005955j
[2025-08-27 18:59:36] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -83.697601-0.000123j
[2025-08-27 18:59:57] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -83.725248-0.001118j
[2025-08-27 19:00:17] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -83.565377+0.002242j
[2025-08-27 19:00:38] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -83.474237-0.002856j
[2025-08-27 19:00:59] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -83.793592+0.006435j
[2025-08-27 19:01:19] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -83.579121-0.004432j
[2025-08-27 19:01:39] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -83.675473-0.001265j
[2025-08-27 19:02:00] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -83.675511+0.003704j
[2025-08-27 19:02:20] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -83.507692-0.007400j
[2025-08-27 19:02:41] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -83.539800+0.001828j
[2025-08-27 19:02:41] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 19:03:02] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -83.547433-0.003951j
[2025-08-27 19:03:22] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -83.731991+0.011041j
[2025-08-27 19:03:42] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -83.668464-0.001822j
[2025-08-27 19:04:03] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -83.726241-0.002971j
[2025-08-27 19:04:23] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -83.834039+0.004714j
[2025-08-27 19:04:44] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -83.856212+0.001334j
[2025-08-27 19:05:04] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -83.740467-0.006758j
[2025-08-27 19:05:25] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -83.736065+0.002105j
[2025-08-27 19:05:45] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -83.673036-0.001552j
[2025-08-27 19:06:06] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -83.740760-0.005945j
[2025-08-27 19:06:26] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -83.808940+0.008089j
[2025-08-27 19:06:47] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -83.778248+0.012071j
[2025-08-27 19:07:07] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -83.708949+0.011315j
[2025-08-27 19:07:28] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -83.705768-0.005878j
[2025-08-27 19:07:48] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -83.737773+0.004157j
[2025-08-27 19:08:09] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -83.784111+0.009415j
[2025-08-27 19:08:29] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -83.684187+0.006675j
[2025-08-27 19:08:50] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -83.722032+0.006702j
[2025-08-27 19:09:10] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -83.923853+0.001170j
[2025-08-27 19:09:31] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -83.756425+0.005519j
[2025-08-27 19:09:51] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -83.757227-0.004473j
[2025-08-27 19:10:11] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -83.874197+0.009429j
[2025-08-27 19:10:32] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -83.754905-0.001828j
[2025-08-27 19:10:52] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -83.877472-0.001087j
[2025-08-27 19:11:13] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -83.801584+0.006080j
[2025-08-27 19:11:33] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -83.964701+0.000571j
[2025-08-27 19:11:54] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -83.800311-0.007151j
[2025-08-27 19:12:14] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -83.830915+0.007359j
[2025-08-27 19:12:35] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -83.713009+0.003797j
[2025-08-27 19:12:55] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -83.806805-0.001605j
[2025-08-27 19:13:16] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -83.837950-0.002155j
[2025-08-27 19:13:36] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -83.665227+0.003444j
[2025-08-27 19:13:57] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -83.604185-0.007169j
[2025-08-27 19:14:17] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -83.652756-0.002394j
[2025-08-27 19:14:38] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -83.664480-0.002911j
[2025-08-27 19:14:58] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -83.700430+0.003406j
[2025-08-27 19:15:19] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -83.803765+0.001591j
[2025-08-27 19:15:39] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -83.743279-0.000782j
[2025-08-27 19:16:00] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -83.566834+0.000778j
[2025-08-27 19:16:20] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -83.547689-0.005013j
[2025-08-27 19:16:41] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -83.760915+0.001435j
[2025-08-27 19:17:01] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -83.782007-0.002585j
[2025-08-27 19:17:21] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -83.909811+0.007637j
[2025-08-27 19:17:42] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -83.947804-0.000909j
[2025-08-27 19:18:02] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -83.828723-0.005559j
[2025-08-27 19:18:23] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -83.893851-0.002625j
[2025-08-27 19:18:43] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -83.825700+0.001055j
[2025-08-27 19:19:04] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -83.768942+0.007082j
[2025-08-27 19:19:24] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -83.698948-0.004426j
[2025-08-27 19:19:45] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -83.590257-0.001726j
[2025-08-27 19:19:45] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 19:20:05] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -83.720433+0.000441j
[2025-08-27 19:20:26] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -83.643571-0.007873j
[2025-08-27 19:20:46] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -83.626064-0.003742j
[2025-08-27 19:21:07] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -83.737667-0.006153j
[2025-08-27 19:21:27] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -83.835501-0.007965j
[2025-08-27 19:21:48] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -83.684128-0.000469j
[2025-08-27 19:22:08] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -83.616314+0.000709j
[2025-08-27 19:22:29] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -83.719625+0.006589j
[2025-08-27 19:22:49] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -83.458678+0.008427j
[2025-08-27 19:23:10] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -83.765048-0.000116j
[2025-08-27 19:23:30] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -83.727257-0.003189j
[2025-08-27 19:23:50] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -83.658987-0.002879j
[2025-08-27 19:24:11] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -83.729943-0.002444j
[2025-08-27 19:24:31] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -83.771423-0.010873j
[2025-08-27 19:24:52] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -83.808507+0.002270j
[2025-08-27 19:25:12] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -83.589400+0.000709j
[2025-08-27 19:25:33] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -83.742488-0.001432j
[2025-08-27 19:25:53] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -83.550247+0.002250j
[2025-08-27 19:26:14] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -83.615325+0.002966j
[2025-08-27 19:26:34] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -83.563122-0.000183j
[2025-08-27 19:26:55] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -83.732396+0.010150j
[2025-08-27 19:27:15] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -83.910916+0.000858j
[2025-08-27 19:27:36] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -83.894444+0.004015j
[2025-08-27 19:27:56] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -83.897297+0.006010j
[2025-08-27 19:28:17] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -83.889407-0.001620j
[2025-08-27 19:28:37] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -83.791875-0.003364j
[2025-08-27 19:28:58] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -83.645481+0.006356j
[2025-08-27 19:29:18] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -83.575725-0.006103j
[2025-08-27 19:29:39] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -83.503338-0.005949j
[2025-08-27 19:29:59] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -83.617604+0.004147j
[2025-08-27 19:30:20] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -83.773795-0.001763j
[2025-08-27 19:30:40] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -83.668376-0.001432j
[2025-08-27 19:31:01] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -83.613938-0.007373j
[2025-08-27 19:31:21] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -83.652597-0.004684j
[2025-08-27 19:31:42] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -83.798559+0.000133j
[2025-08-27 19:32:02] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -83.682067+0.000342j
[2025-08-27 19:32:23] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -83.526442+0.000718j
[2025-08-27 19:32:43] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -83.607817+0.007804j
[2025-08-27 19:33:03] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -83.510054-0.003748j
[2025-08-27 19:33:24] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -83.586359-0.003996j
[2025-08-27 19:33:44] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -83.660159-0.001240j
[2025-08-27 19:34:05] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -83.669951-0.001213j
[2025-08-27 19:34:25] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -83.600379-0.003349j
[2025-08-27 19:34:46] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -83.719071+0.000948j
[2025-08-27 19:35:06] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -83.705916+0.006596j
[2025-08-27 19:35:27] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -83.722559+0.002130j
[2025-08-27 19:35:47] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -83.706373-0.001112j
[2025-08-27 19:36:08] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -83.789713-0.001290j
[2025-08-27 19:36:28] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -83.725872+0.001070j
[2025-08-27 19:36:49] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -83.709665+0.005496j
[2025-08-27 19:36:49] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 19:36:49] ✅ Training completed | Restarts: 1
[2025-08-27 19:36:49] ============================================================
[2025-08-27 19:36:49] Training completed | Runtime: 9264.6s
[2025-08-27 19:36:57] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 19:36:57] ============================================================
