[2025-08-27 14:27:08] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-08-27 14:27:08]   - 迭代次数: final
[2025-08-27 14:27:08]   - 能量: -85.026625+0.000719j ± 0.056647
[2025-08-27 14:27:08]   - 时间戳: 2025-08-27T00:17:06.526370+08:00
[2025-08-27 14:27:19] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 14:27:19] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 14:27:19] ==================================================
[2025-08-27 14:27:19] GCNN for Shastry-Sutherland Model
[2025-08-27 14:27:19] ==================================================
[2025-08-27 14:27:19] System parameters:
[2025-08-27 14:27:19]   - System size: L=5, N=100
[2025-08-27 14:27:19]   - System parameters: J1=0.03, J2=0.0, Q=1.0
[2025-08-27 14:27:19] --------------------------------------------------
[2025-08-27 14:27:19] Model parameters:
[2025-08-27 14:27:19]   - Number of layers = 4
[2025-08-27 14:27:19]   - Number of features = 4
[2025-08-27 14:27:19]   - Total parameters = 19628
[2025-08-27 14:27:19] --------------------------------------------------
[2025-08-27 14:27:19] Training parameters:
[2025-08-27 14:27:19]   - Learning rate: 0.015
[2025-08-27 14:27:19]   - Total iterations: 450
[2025-08-27 14:27:19]   - Annealing cycles: 2
[2025-08-27 14:27:19]   - Initial period: 150
[2025-08-27 14:27:19]   - Period multiplier: 2.0
[2025-08-27 14:27:19]   - Temperature range: 0.0-1.0
[2025-08-27 14:27:19]   - Samples: 4096
[2025-08-27 14:27:19]   - Discarded samples: 0
[2025-08-27 14:27:19]   - Chunk size: 2048
[2025-08-27 14:27:19]   - Diagonal shift: 0.2
[2025-08-27 14:27:19]   - Gradient clipping: 1.0
[2025-08-27 14:27:19]   - Checkpoint enabled: interval=50
[2025-08-27 14:27:19]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.03/training/checkpoints
[2025-08-27 14:27:19] --------------------------------------------------
[2025-08-27 14:27:19] Device status:
[2025-08-27 14:27:19]   - Devices model: NVIDIA H200 NVL
[2025-08-27 14:27:19]   - Number of devices: 1
[2025-08-27 14:27:19]   - Sharding: True
[2025-08-27 14:27:19] ============================================================
[2025-08-27 14:28:10] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -84.458449+0.030090j
[2025-08-27 14:28:48] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -84.677315+0.031832j
[2025-08-27 14:29:08] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -84.696954+0.005403j
[2025-08-27 14:29:29] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -84.490938+0.004266j
[2025-08-27 14:29:49] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -84.500474+0.006044j
[2025-08-27 14:30:10] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -84.447415+0.011090j
[2025-08-27 14:30:30] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -84.375894+0.011483j
[2025-08-27 14:30:51] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -84.500869+0.009135j
[2025-08-27 14:31:11] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -84.546360+0.002727j
[2025-08-27 14:31:32] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -84.469650-0.005526j
[2025-08-27 14:31:52] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -84.425776+0.016812j
[2025-08-27 14:32:13] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -84.538816+0.005556j
[2025-08-27 14:32:34] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -84.527509+0.005447j
[2025-08-27 14:32:54] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -84.566845-0.008668j
[2025-08-27 14:33:15] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -84.625375-0.006102j
[2025-08-27 14:33:35] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -84.762623+0.001372j
[2025-08-27 14:33:56] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -84.428501+0.003134j
[2025-08-27 14:34:16] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -84.417328+0.001796j
[2025-08-27 14:34:37] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -84.477752+0.005167j
[2025-08-27 14:34:57] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -84.342141-0.002844j
[2025-08-27 14:35:18] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -84.443542+0.004899j
[2025-08-27 14:35:38] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -84.386333+0.016049j
[2025-08-27 14:35:59] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -84.381837+0.004980j
[2025-08-27 14:36:19] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -84.176174-0.000576j
[2025-08-27 14:36:41] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -84.148982+0.010075j
[2025-08-27 14:37:01] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -84.015701+0.015490j
[2025-08-27 14:37:22] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -84.038229+0.003996j
[2025-08-27 14:37:42] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -84.066016+0.001363j
[2025-08-27 14:38:03] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -84.171450+0.000050j
[2025-08-27 14:38:23] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -84.225248+0.002668j
[2025-08-27 14:38:44] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -84.324898+0.005181j
[2025-08-27 14:39:04] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -84.286248-0.007490j
[2025-08-27 14:39:25] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -84.342522-0.002952j
[2025-08-27 14:39:45] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -84.338967-0.004001j
[2025-08-27 14:40:06] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -84.386510+0.002884j
[2025-08-27 14:40:26] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -84.279737-0.005399j
[2025-08-27 14:40:47] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -84.330801+0.012785j
[2025-08-27 14:41:07] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -84.322441+0.005021j
[2025-08-27 14:41:28] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -84.270189+0.002815j
[2025-08-27 14:41:48] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -84.312937+0.000617j
[2025-08-27 14:42:09] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -84.508603-0.001750j
[2025-08-27 14:42:30] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -84.409959+0.006017j
[2025-08-27 14:42:50] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -84.380162-0.003831j
[2025-08-27 14:43:11] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -84.439120+0.004024j
[2025-08-27 14:43:32] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -84.218347+0.015355j
[2025-08-27 14:43:52] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -84.269614+0.004861j
[2025-08-27 14:44:13] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -84.195226-0.003202j
[2025-08-27 14:44:33] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -84.106742+0.005307j
[2025-08-27 14:44:54] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -84.272791+0.011418j
[2025-08-27 14:45:14] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -84.227708+0.004036j
[2025-08-27 14:45:14] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 14:45:35] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -84.177555+0.002833j
[2025-08-27 14:45:55] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -84.339251-0.006138j
[2025-08-27 14:46:16] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -84.513088-0.002087j
[2025-08-27 14:46:36] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -84.474904-0.010714j
[2025-08-27 14:46:57] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -84.399906-0.002533j
[2025-08-27 14:47:18] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -84.194943-0.001591j
[2025-08-27 14:47:38] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -84.199290+0.001131j
[2025-08-27 14:47:59] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -84.315059+0.001544j
[2025-08-27 14:48:19] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -84.334425+0.002360j
[2025-08-27 14:48:40] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -84.466804-0.007624j
[2025-08-27 14:49:00] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -84.419074-0.001388j
[2025-08-27 14:49:21] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -84.512508+0.006650j
[2025-08-27 14:49:41] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -84.386529-0.003346j
[2025-08-27 14:50:02] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -84.350832+0.010746j
[2025-08-27 14:50:22] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -84.353125-0.006475j
[2025-08-27 14:50:43] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -84.311198-0.003422j
[2025-08-27 14:51:03] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -84.200151+0.006441j
[2025-08-27 14:51:24] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -84.380495-0.001370j
[2025-08-27 14:51:44] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -84.373035-0.010485j
[2025-08-27 14:52:05] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -84.450924+0.000049j
[2025-08-27 14:52:25] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -84.455303+0.001492j
[2025-08-27 14:52:46] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -84.443059+0.004077j
[2025-08-27 14:53:06] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -84.380236+0.000283j
[2025-08-27 14:53:27] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -84.424410+0.001533j
[2025-08-27 14:53:47] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -84.334197+0.006930j
[2025-08-27 14:54:08] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -84.342637+0.000313j
[2025-08-27 14:54:28] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -84.434124-0.005540j
[2025-08-27 14:54:49] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -84.333114-0.002990j
[2025-08-27 14:55:09] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -84.443580+0.002752j
[2025-08-27 14:55:30] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -84.372283+0.010934j
[2025-08-27 14:55:51] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -84.406520+0.009845j
[2025-08-27 14:56:11] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -84.330155+0.015369j
[2025-08-27 14:56:32] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -84.164834-0.004654j
[2025-08-27 14:56:52] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -84.207403-0.001494j
[2025-08-27 14:57:13] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -84.535451-0.001479j
[2025-08-27 14:57:33] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -84.330071+0.012995j
[2025-08-27 14:57:54] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -84.383574-0.001270j
[2025-08-27 14:58:15] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -84.406371+0.005616j
[2025-08-27 14:58:35] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -84.444316-0.008287j
[2025-08-27 14:58:56] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -84.246017+0.006566j
[2025-08-27 14:59:16] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -84.326981+0.002162j
[2025-08-27 14:59:37] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -84.415346-0.006839j
[2025-08-27 14:59:57] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -84.545013+0.003544j
[2025-08-27 15:00:18] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -84.420809-0.002945j
[2025-08-27 15:00:38] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -84.483505-0.000171j
[2025-08-27 15:00:59] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -84.493148-0.003921j
[2025-08-27 15:01:19] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -84.426627-0.003151j
[2025-08-27 15:01:40] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -84.473020+0.001201j
[2025-08-27 15:02:00] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -84.457532-0.008611j
[2025-08-27 15:02:21] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -84.351918+0.005693j
[2025-08-27 15:02:21] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 15:02:41] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -84.387123+0.007141j
[2025-08-27 15:03:02] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -84.355921-0.002222j
[2025-08-27 15:03:22] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -84.348558+0.006160j
[2025-08-27 15:03:43] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -84.383240+0.005949j
[2025-08-27 15:04:03] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -84.398910+0.003833j
[2025-08-27 15:04:24] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -84.273046-0.005957j
[2025-08-27 15:04:44] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -84.345068+0.008846j
[2025-08-27 15:05:05] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -84.390171-0.002404j
[2025-08-27 15:05:25] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -84.359289+0.009384j
[2025-08-27 15:05:46] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -84.259269+0.008742j
[2025-08-27 15:06:06] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -84.217041+0.004729j
[2025-08-27 15:06:26] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -84.100494-0.002421j
[2025-08-27 15:06:47] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -84.309001+0.003720j
[2025-08-27 15:07:07] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -84.244985-0.001948j
[2025-08-27 15:07:28] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -84.630677-0.003928j
[2025-08-27 15:07:48] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -84.400289+0.013665j
[2025-08-27 15:08:09] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -84.402213-0.003693j
[2025-08-27 15:08:29] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -84.223091+0.012536j
[2025-08-27 15:08:50] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -84.349818-0.000779j
[2025-08-27 15:09:10] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -84.248297+0.006937j
[2025-08-27 15:09:31] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -84.353087-0.007331j
[2025-08-27 15:09:51] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -84.306236-0.000376j
[2025-08-27 15:10:12] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -84.213843-0.002424j
[2025-08-27 15:10:32] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -84.251387-0.004763j
[2025-08-27 15:10:53] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -84.448653-0.003707j
[2025-08-27 15:11:13] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -84.424134-0.000292j
[2025-08-27 15:11:34] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -84.572041-0.008425j
[2025-08-27 15:11:54] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -84.600440-0.001063j
[2025-08-27 15:12:15] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -84.623616+0.005293j
[2025-08-27 15:12:35] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -84.610390+0.004511j
[2025-08-27 15:12:56] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -84.502548+0.003451j
[2025-08-27 15:13:16] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -84.567910+0.005073j
[2025-08-27 15:13:37] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -84.451719-0.002574j
[2025-08-27 15:13:58] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -84.297940+0.003471j
[2025-08-27 15:14:18] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -84.484538+0.003887j
[2025-08-27 15:14:39] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -84.408062-0.008883j
[2025-08-27 15:14:59] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -84.345359+0.000383j
[2025-08-27 15:15:20] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -84.368734+0.012357j
[2025-08-27 15:15:40] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -84.272317-0.002170j
[2025-08-27 15:16:01] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -84.307759+0.002136j
[2025-08-27 15:16:21] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -84.312927+0.002398j
[2025-08-27 15:16:42] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -84.447726+0.004233j
[2025-08-27 15:17:02] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -84.552014-0.002419j
[2025-08-27 15:17:23] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -84.529110-0.008067j
[2025-08-27 15:17:43] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -84.514468-0.003528j
[2025-08-27 15:18:04] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -84.346187-0.005754j
[2025-08-27 15:18:24] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -84.195208-0.001499j
[2025-08-27 15:18:45] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -84.196523-0.001081j
[2025-08-27 15:19:05] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -84.227141-0.007137j
[2025-08-27 15:19:26] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -84.198429+0.005355j
[2025-08-27 15:19:26] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 15:19:26] RESTART #1 | Period: 300
[2025-08-27 15:19:46] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -84.389364-0.001167j
[2025-08-27 15:20:07] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -84.311343-0.006869j
[2025-08-27 15:20:28] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -84.368638+0.007369j
[2025-08-27 15:20:48] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -84.393756-0.016320j
[2025-08-27 15:21:09] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -84.299951+0.006100j
[2025-08-27 15:21:30] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -84.460491-0.001263j
[2025-08-27 15:21:50] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -84.529654-0.002301j
[2025-08-27 15:22:11] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -84.549193+0.002488j
[2025-08-27 15:22:31] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -84.278244-0.008152j
[2025-08-27 15:22:52] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -84.128977+0.001509j
[2025-08-27 15:23:12] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -84.300375-0.007145j
[2025-08-27 15:23:33] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -84.365508+0.004591j
[2025-08-27 15:23:53] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -84.267770+0.000164j
[2025-08-27 15:24:14] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -84.263547-0.002920j
[2025-08-27 15:24:34] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -84.242959+0.004766j
[2025-08-27 15:24:55] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -84.195459-0.011558j
[2025-08-27 15:25:16] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -84.398946-0.009988j
[2025-08-27 15:25:36] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -84.159120-0.000573j
[2025-08-27 15:25:57] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -84.159657-0.011051j
[2025-08-27 15:26:17] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -84.279417-0.004123j
[2025-08-27 15:26:38] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -84.202666+0.004619j
[2025-08-27 15:26:58] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -84.186164-0.008743j
[2025-08-27 15:27:19] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -84.252479-0.001655j
[2025-08-27 15:27:39] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -84.225729+0.004628j
[2025-08-27 15:28:00] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -84.316695+0.001100j
[2025-08-27 15:28:21] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -84.337811+0.001971j
[2025-08-27 15:28:41] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -84.315693-0.008340j
[2025-08-27 15:29:02] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -84.226705-0.001449j
[2025-08-27 15:29:22] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -84.337391-0.004924j
[2025-08-27 15:29:43] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -84.385856-0.000195j
[2025-08-27 15:30:03] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -84.354212-0.004537j
[2025-08-27 15:30:24] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -84.144695-0.005014j
[2025-08-27 15:30:45] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -84.151807+0.002633j
[2025-08-27 15:31:05] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -84.262976-0.008887j
[2025-08-27 15:31:26] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -84.552139-0.003312j
[2025-08-27 15:31:46] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -84.456602+0.000835j
[2025-08-27 15:32:07] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -84.316327-0.009404j
[2025-08-27 15:32:27] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -84.498604+0.000376j
[2025-08-27 15:32:48] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -84.433393-0.004334j
[2025-08-27 15:33:08] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -84.466335+0.004938j
[2025-08-27 15:33:29] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -84.371721+0.001137j
[2025-08-27 15:33:49] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -84.370976+0.013068j
[2025-08-27 15:34:10] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -84.377796+0.003453j
[2025-08-27 15:34:30] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -84.538769+0.001868j
[2025-08-27 15:34:51] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -84.433999-0.001575j
[2025-08-27 15:35:11] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -84.210666-0.004155j
[2025-08-27 15:35:32] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -84.125251+0.005536j
[2025-08-27 15:35:53] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -84.147671-0.004044j
[2025-08-27 15:36:13] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -84.227784-0.007456j
[2025-08-27 15:36:34] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -84.380246+0.001981j
[2025-08-27 15:36:34] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 15:36:54] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -84.421660+0.002602j
[2025-08-27 15:37:15] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -84.431611-0.006872j
[2025-08-27 15:37:35] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -84.280557-0.005582j
[2025-08-27 15:37:56] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -84.257061-0.007292j
[2025-08-27 15:38:16] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -84.379912+0.000316j
[2025-08-27 15:38:37] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -84.355554-0.012624j
[2025-08-27 15:38:57] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -84.212726+0.002299j
[2025-08-27 15:39:18] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -84.258296-0.001808j
[2025-08-27 15:39:38] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -84.261218+0.000704j
[2025-08-27 15:39:59] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -84.263870+0.003351j
[2025-08-27 15:40:19] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -84.195296-0.004217j
[2025-08-27 15:40:40] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -84.125583+0.005492j
[2025-08-27 15:41:00] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -84.127029-0.006655j
[2025-08-27 15:41:21] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -84.212444+0.010110j
[2025-08-27 15:41:41] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -84.231519+0.003553j
[2025-08-27 15:42:02] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -84.302960-0.011648j
[2025-08-27 15:42:23] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -84.220657+0.002271j
[2025-08-27 15:42:43] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -84.219558-0.003623j
[2025-08-27 15:43:04] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -84.145214-0.005007j
[2025-08-27 15:43:24] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -84.372774-0.003343j
[2025-08-27 15:43:45] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -84.368807+0.010492j
[2025-08-27 15:44:05] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -84.406495+0.005487j
[2025-08-27 15:44:26] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -84.321974+0.000014j
[2025-08-27 15:44:46] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -84.498705+0.003602j
[2025-08-27 15:45:07] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -84.404915-0.005137j
[2025-08-27 15:45:27] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -84.287944+0.007404j
[2025-08-27 15:45:48] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -84.371874+0.009947j
[2025-08-27 15:46:08] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -84.311288-0.009297j
[2025-08-27 15:46:29] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -84.105951-0.005412j
[2025-08-27 15:46:49] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -84.187861-0.003260j
[2025-08-27 15:47:10] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -84.175734+0.003119j
[2025-08-27 15:47:31] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -84.408657-0.002145j
[2025-08-27 15:47:51] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -84.243690+0.004655j
[2025-08-27 15:48:11] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -84.002819+0.008518j
[2025-08-27 15:48:32] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -84.198679+0.001112j
[2025-08-27 15:48:52] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -84.130946-0.001086j
[2025-08-27 15:49:13] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -84.043790+0.003382j
[2025-08-27 15:49:33] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -84.089194-0.006613j
[2025-08-27 15:49:54] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -84.035016-0.000110j
[2025-08-27 15:50:14] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -84.210068-0.001990j
[2025-08-27 15:50:35] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -84.103218+0.001556j
[2025-08-27 15:50:56] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -84.222538-0.006295j
[2025-08-27 15:51:16] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -84.234831-0.006600j
[2025-08-27 15:51:37] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -84.169787+0.007922j
[2025-08-27 15:51:57] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -84.263443-0.000178j
[2025-08-27 15:52:18] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -84.221547+0.002873j
[2025-08-27 15:52:38] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -84.303551-0.001687j
[2025-08-27 15:52:59] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -84.383647+0.001234j
[2025-08-27 15:53:19] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -84.356002+0.000584j
[2025-08-27 15:53:40] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -84.429027+0.008663j
[2025-08-27 15:53:40] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 15:54:00] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -84.556771+0.002408j
[2025-08-27 15:54:21] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -84.606121-0.005264j
[2025-08-27 15:54:41] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -84.315474-0.006214j
[2025-08-27 15:55:02] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -84.576689+0.004188j
[2025-08-27 15:55:23] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -84.552848-0.000531j
[2025-08-27 15:55:43] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -84.538956-0.001019j
[2025-08-27 15:56:04] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -84.413968-0.001388j
[2025-08-27 15:56:24] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -84.411582+0.007764j
[2025-08-27 15:56:45] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -84.284760+0.007412j
[2025-08-27 15:57:05] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -84.211162+0.001185j
[2025-08-27 15:57:26] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -84.423534+0.003220j
[2025-08-27 15:57:46] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -84.469957-0.003694j
[2025-08-27 15:58:07] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -84.485747+0.002506j
[2025-08-27 15:58:27] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -84.498499+0.007958j
[2025-08-27 15:58:48] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -84.406014-0.001587j
[2025-08-27 15:59:08] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -84.485853+0.008214j
[2025-08-27 15:59:29] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -84.410900+0.001656j
[2025-08-27 15:59:49] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -84.430231-0.000246j
[2025-08-27 16:00:10] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -84.410781-0.006150j
[2025-08-27 16:00:30] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -84.381776-0.004501j
[2025-08-27 16:00:51] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -84.397034+0.010736j
[2025-08-27 16:01:11] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -84.370222-0.001431j
[2025-08-27 16:01:32] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -84.295430+0.000729j
[2025-08-27 16:01:52] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -84.311167-0.002434j
[2025-08-27 16:02:13] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -84.405559-0.003694j
[2025-08-27 16:02:34] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -84.216251+0.004971j
[2025-08-27 16:02:54] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -84.276108+0.000476j
[2025-08-27 16:03:15] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -84.331191+0.000248j
[2025-08-27 16:03:35] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -84.332712+0.003768j
[2025-08-27 16:03:56] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -84.371787-0.004878j
[2025-08-27 16:04:16] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -84.478469+0.002999j
[2025-08-27 16:04:37] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -84.521796+0.003795j
[2025-08-27 16:04:57] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -84.406093-0.000824j
[2025-08-27 16:05:18] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -84.419536+0.001476j
[2025-08-27 16:05:38] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -84.372134-0.005147j
[2025-08-27 16:05:59] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -84.512094+0.006301j
[2025-08-27 16:06:19] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -84.492700+0.005141j
[2025-08-27 16:06:40] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -84.417890+0.004661j
[2025-08-27 16:07:00] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -84.475878-0.006447j
[2025-08-27 16:07:21] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -84.274082-0.006325j
[2025-08-27 16:07:41] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -84.325358-0.006504j
[2025-08-27 16:08:02] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -84.341368+0.001896j
[2025-08-27 16:08:22] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -84.280408-0.001725j
[2025-08-27 16:08:43] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -84.270787+0.004516j
[2025-08-27 16:09:03] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -84.234229+0.000202j
[2025-08-27 16:09:24] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -84.319519-0.003095j
[2025-08-27 16:09:44] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -84.318286+0.002067j
[2025-08-27 16:10:05] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -84.380497-0.003585j
[2025-08-27 16:10:25] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -84.434664-0.000147j
[2025-08-27 16:10:46] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -84.424294-0.004348j
[2025-08-27 16:10:46] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 16:11:06] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -84.506171-0.000106j
[2025-08-27 16:11:27] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -84.408223+0.004862j
[2025-08-27 16:11:47] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -84.360418+0.004203j
[2025-08-27 16:12:08] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -84.387907-0.003943j
[2025-08-27 16:12:29] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -84.385338-0.002809j
[2025-08-27 16:12:49] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -84.453292-0.006457j
[2025-08-27 16:13:10] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -84.391158+0.007867j
[2025-08-27 16:13:30] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -84.487294+0.005424j
[2025-08-27 16:13:51] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -84.437586+0.002664j
[2025-08-27 16:14:11] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -84.389381+0.002730j
[2025-08-27 16:14:32] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -84.258168+0.005128j
[2025-08-27 16:14:52] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -84.355673-0.000539j
[2025-08-27 16:15:12] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -84.364401-0.002861j
[2025-08-27 16:15:33] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -84.475732-0.002970j
[2025-08-27 16:15:53] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -84.355638-0.000106j
[2025-08-27 16:16:14] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -84.386689-0.002191j
[2025-08-27 16:16:34] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -84.405803-0.006514j
[2025-08-27 16:16:55] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -84.237029+0.011413j
[2025-08-27 16:17:15] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -84.177328+0.004851j
[2025-08-27 16:17:36] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -84.248125+0.003170j
[2025-08-27 16:17:56] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -84.248775+0.012107j
[2025-08-27 16:18:17] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -84.290270+0.003363j
[2025-08-27 16:18:37] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -84.223514+0.001706j
[2025-08-27 16:18:58] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -84.260157+0.003523j
[2025-08-27 16:19:18] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -84.249575+0.006539j
[2025-08-27 16:19:39] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -84.328617+0.009990j
[2025-08-27 16:19:59] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -84.265988+0.002436j
[2025-08-27 16:20:20] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -84.291079+0.008496j
[2025-08-27 16:20:41] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -84.248651+0.000659j
[2025-08-27 16:21:01] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -84.431057-0.007291j
[2025-08-27 16:21:22] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -84.461041+0.007782j
[2025-08-27 16:21:42] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -84.337689-0.003132j
[2025-08-27 16:22:03] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -84.441442-0.000482j
[2025-08-27 16:22:23] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -84.341912+0.005167j
[2025-08-27 16:22:44] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -84.338097+0.002811j
[2025-08-27 16:23:04] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -84.386033+0.001332j
[2025-08-27 16:23:25] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -84.329099-0.002611j
[2025-08-27 16:23:45] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -84.248925-0.007251j
[2025-08-27 16:24:06] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -84.318633-0.002601j
[2025-08-27 16:24:26] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -84.324285-0.004906j
[2025-08-27 16:24:47] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -84.268131-0.002330j
[2025-08-27 16:25:07] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -84.338909-0.002931j
[2025-08-27 16:25:28] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -84.328888+0.013382j
[2025-08-27 16:25:48] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -84.289640+0.001324j
[2025-08-27 16:26:09] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -84.285210-0.004058j
[2025-08-27 16:26:30] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -84.141195+0.006008j
[2025-08-27 16:26:50] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -84.407731+0.002404j
[2025-08-27 16:27:11] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -84.178309+0.000429j
[2025-08-27 16:27:31] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -84.247414+0.004565j
[2025-08-27 16:27:52] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -84.267787+0.003208j
[2025-08-27 16:27:52] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 16:28:13] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -84.135030+0.000317j
[2025-08-27 16:28:33] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -84.233576+0.000547j
[2025-08-27 16:28:54] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -84.233872+0.002387j
[2025-08-27 16:29:14] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -84.154386+0.006956j
[2025-08-27 16:29:35] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -84.060643-0.002127j
[2025-08-27 16:29:55] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -84.259824-0.008514j
[2025-08-27 16:30:16] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -84.249627-0.004384j
[2025-08-27 16:30:36] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -84.324710+0.002649j
[2025-08-27 16:30:57] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -84.196925-0.000254j
[2025-08-27 16:31:17] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -84.298240+0.001099j
[2025-08-27 16:31:38] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -84.277645-0.005157j
[2025-08-27 16:31:58] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -84.478107-0.006474j
[2025-08-27 16:32:19] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -84.406756-0.006019j
[2025-08-27 16:32:39] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -84.421745+0.001469j
[2025-08-27 16:33:00] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -84.276756+0.003747j
[2025-08-27 16:33:20] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -84.263129+0.007385j
[2025-08-27 16:33:41] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -84.441709+0.000387j
[2025-08-27 16:34:01] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -84.513202+0.003784j
[2025-08-27 16:34:22] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -84.537589+0.000569j
[2025-08-27 16:34:42] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -84.481497-0.009043j
[2025-08-27 16:35:03] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -84.306291-0.001282j
[2025-08-27 16:35:23] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -84.356181+0.003047j
[2025-08-27 16:35:44] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -84.349106+0.002038j
[2025-08-27 16:36:04] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -84.262263+0.003364j
[2025-08-27 16:36:25] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -84.327771-0.008790j
[2025-08-27 16:36:45] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -84.138277-0.004565j
[2025-08-27 16:37:06] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -84.123893-0.000105j
[2025-08-27 16:37:26] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -84.282513-0.003350j
[2025-08-27 16:37:47] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -84.288677+0.003141j
[2025-08-27 16:38:07] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -84.377205-0.002871j
[2025-08-27 16:38:28] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -84.424814-0.002702j
[2025-08-27 16:38:49] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -84.301873-0.006257j
[2025-08-27 16:39:09] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -84.487002-0.005125j
[2025-08-27 16:39:30] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -84.506359-0.002212j
[2025-08-27 16:39:50] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -84.230663+0.006807j
[2025-08-27 16:40:11] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -84.274580-0.008111j
[2025-08-27 16:40:31] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -84.420805-0.002601j
[2025-08-27 16:40:52] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -84.319429-0.004965j
[2025-08-27 16:41:12] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -84.489925+0.003789j
[2025-08-27 16:41:33] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -84.462727-0.001144j
[2025-08-27 16:41:53] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -84.391550-0.003186j
[2025-08-27 16:42:14] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -84.301910+0.005975j
[2025-08-27 16:42:35] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -84.271590-0.001879j
[2025-08-27 16:42:56] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -84.297038-0.003848j
[2025-08-27 16:43:16] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -84.279569-0.000147j
[2025-08-27 16:43:37] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -84.272133+0.001415j
[2025-08-27 16:43:57] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -84.307155-0.003708j
[2025-08-27 16:44:18] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -84.181087-0.002252j
[2025-08-27 16:44:38] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -84.283178-0.002769j
[2025-08-27 16:44:59] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -84.363876+0.009607j
[2025-08-27 16:44:59] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 16:45:19] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -84.306596-0.012099j
[2025-08-27 16:45:40] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -84.229498-0.002188j
[2025-08-27 16:46:00] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -84.294559+0.000508j
[2025-08-27 16:46:21] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -84.349072+0.001887j
[2025-08-27 16:46:41] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -84.340182-0.008533j
[2025-08-27 16:47:02] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -84.362332+0.004079j
[2025-08-27 16:47:23] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -84.360059+0.009401j
[2025-08-27 16:47:43] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -84.406522+0.004529j
[2025-08-27 16:48:04] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -84.463469+0.007645j
[2025-08-27 16:48:24] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -84.573069-0.002694j
[2025-08-27 16:48:45] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -84.399775+0.003165j
[2025-08-27 16:49:05] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -84.405862-0.008692j
[2025-08-27 16:49:26] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -84.514967-0.004974j
[2025-08-27 16:49:46] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -84.450496-0.005592j
[2025-08-27 16:50:07] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -84.426656-0.000241j
[2025-08-27 16:50:27] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -84.403084-0.001345j
[2025-08-27 16:50:48] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -84.269240+0.005461j
[2025-08-27 16:51:08] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -84.243703-0.002580j
[2025-08-27 16:51:29] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -84.450684+0.000346j
[2025-08-27 16:51:49] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -84.336191-0.002462j
[2025-08-27 16:52:10] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -84.411834-0.007097j
[2025-08-27 16:52:30] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -84.399729+0.006437j
[2025-08-27 16:52:51] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -84.398237+0.002622j
[2025-08-27 16:53:11] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -84.389245+0.010241j
[2025-08-27 16:53:32] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -84.303590+0.003800j
[2025-08-27 16:53:52] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -84.369807+0.008733j
[2025-08-27 16:54:13] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -84.375496-0.000098j
[2025-08-27 16:54:33] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -84.442251+0.005098j
[2025-08-27 16:54:54] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -84.438816+0.011037j
[2025-08-27 16:55:14] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -84.299408-0.006185j
[2025-08-27 16:55:35] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -84.461721-0.000145j
[2025-08-27 16:55:55] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -84.391824-0.006596j
[2025-08-27 16:56:16] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -84.355672+0.004855j
[2025-08-27 16:56:36] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -84.338433-0.009280j
[2025-08-27 16:56:57] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -84.313965+0.000443j
[2025-08-27 16:57:17] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -84.203564-0.004481j
[2025-08-27 16:57:38] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -84.343062-0.006597j
[2025-08-27 16:57:58] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -84.337639-0.008059j
[2025-08-27 16:58:19] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -84.360337-0.000849j
[2025-08-27 16:58:39] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -84.328109-0.002706j
[2025-08-27 16:59:00] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -84.467197+0.000476j
[2025-08-27 16:59:20] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -84.273094-0.004969j
[2025-08-27 16:59:41] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -84.376018+0.002211j
[2025-08-27 17:00:01] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -84.199736+0.004496j
[2025-08-27 17:00:22] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -84.178742-0.005921j
[2025-08-27 17:00:43] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -84.315205-0.003659j
[2025-08-27 17:01:03] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -84.238010-0.002551j
[2025-08-27 17:01:24] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -84.263353+0.012190j
[2025-08-27 17:01:44] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -84.156545+0.006669j
[2025-08-27 17:01:57] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -84.113107+0.006542j
[2025-08-27 17:01:57] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 17:01:57] ✅ Training completed | Restarts: 1
[2025-08-27 17:01:57] ============================================================
[2025-08-27 17:01:57] Training completed | Runtime: 9278.4s
[2025-08-27 17:02:01] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 17:02:01] ============================================================
