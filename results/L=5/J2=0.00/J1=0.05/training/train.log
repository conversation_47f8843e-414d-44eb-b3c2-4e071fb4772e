[2025-08-27 14:27:08] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-08-27 14:27:08]   - 迭代次数: final
[2025-08-27 14:27:08]   - 能量: -85.026625+0.000719j ± 0.056647
[2025-08-27 14:27:08]   - 时间戳: 2025-08-27T00:17:06.526370+08:00
[2025-08-27 14:27:19] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 14:27:19] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 14:27:19] ==================================================
[2025-08-27 14:27:19] GCNN for Shastry-Sutherland Model
[2025-08-27 14:27:19] ==================================================
[2025-08-27 14:27:19] System parameters:
[2025-08-27 14:27:19]   - System size: L=5, N=100
[2025-08-27 14:27:19]   - System parameters: J1=0.05, J2=0.0, Q=1.0
[2025-08-27 14:27:19] --------------------------------------------------
[2025-08-27 14:27:19] Model parameters:
[2025-08-27 14:27:19]   - Number of layers = 4
[2025-08-27 14:27:19]   - Number of features = 4
[2025-08-27 14:27:19]   - Total parameters = 19628
[2025-08-27 14:27:19] --------------------------------------------------
[2025-08-27 14:27:19] Training parameters:
[2025-08-27 14:27:19]   - Learning rate: 0.015
[2025-08-27 14:27:19]   - Total iterations: 450
[2025-08-27 14:27:19]   - Annealing cycles: 2
[2025-08-27 14:27:19]   - Initial period: 150
[2025-08-27 14:27:19]   - Period multiplier: 2.0
[2025-08-27 14:27:19]   - Temperature range: 0.0-1.0
[2025-08-27 14:27:19]   - Samples: 4096
[2025-08-27 14:27:19]   - Discarded samples: 0
[2025-08-27 14:27:19]   - Chunk size: 2048
[2025-08-27 14:27:19]   - Diagonal shift: 0.2
[2025-08-27 14:27:19]   - Gradient clipping: 1.0
[2025-08-27 14:27:19]   - Checkpoint enabled: interval=50
[2025-08-27 14:27:19]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.05/training/checkpoints
[2025-08-27 14:27:19] --------------------------------------------------
[2025-08-27 14:27:19] Device status:
[2025-08-27 14:27:19]   - Devices model: NVIDIA H200 NVL
[2025-08-27 14:27:19]   - Number of devices: 1
[2025-08-27 14:27:19]   - Sharding: True
[2025-08-27 14:27:19] ============================================================
[2025-08-27 14:28:10] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -85.885077+0.013972j
[2025-08-27 14:28:48] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -85.896218+0.019807j
[2025-08-27 14:29:08] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -85.776967+0.014855j
[2025-08-27 14:29:29] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -85.799009+0.015276j
[2025-08-27 14:29:49] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -85.547156+0.005794j
[2025-08-27 14:30:10] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -85.618663+0.009344j
[2025-08-27 14:30:30] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -85.707365+0.007102j
[2025-08-27 14:30:51] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -85.626257+0.010865j
[2025-08-27 14:31:11] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -85.705659+0.001335j
[2025-08-27 14:31:32] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -85.626911-0.001882j
[2025-08-27 14:31:52] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -85.700115-0.000799j
[2025-08-27 14:32:13] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -85.683148+0.003294j
[2025-08-27 14:32:33] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -85.789724+0.003489j
[2025-08-27 14:32:54] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -85.580131+0.003065j
[2025-08-27 14:33:14] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -85.654368-0.009474j
[2025-08-27 14:33:35] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -85.630092-0.002256j
[2025-08-27 14:33:55] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -85.740234+0.009990j
[2025-08-27 14:34:16] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -85.699410+0.007186j
[2025-08-27 14:34:36] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -85.552852+0.000306j
[2025-08-27 14:34:57] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -85.774646-0.006162j
[2025-08-27 14:35:17] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -85.648610+0.000412j
[2025-08-27 14:35:38] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -85.781719+0.004403j
[2025-08-27 14:35:58] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -85.788529+0.011607j
[2025-08-27 14:36:19] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -85.811189+0.004933j
[2025-08-27 14:36:39] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -85.790472+0.002080j
[2025-08-27 14:36:59] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -85.619429-0.003487j
[2025-08-27 14:37:20] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -85.687828-0.004706j
[2025-08-27 14:37:40] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -85.717710-0.004976j
[2025-08-27 14:38:01] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -85.627086-0.006847j
[2025-08-27 14:38:21] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -85.668175+0.001226j
[2025-08-27 14:38:42] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -85.723624+0.006293j
[2025-08-27 14:39:02] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -85.576401-0.000495j
[2025-08-27 14:39:23] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -85.461290+0.010243j
[2025-08-27 14:39:43] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -85.459793-0.006629j
[2025-08-27 14:40:04] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -85.549353-0.004989j
[2025-08-27 14:40:24] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -85.479242+0.007048j
[2025-08-27 14:40:45] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -85.448943-0.008015j
[2025-08-27 14:41:05] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -85.449852+0.019268j
[2025-08-27 14:41:26] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -85.509697-0.003409j
[2025-08-27 14:41:46] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -85.474109+0.010596j
[2025-08-27 14:42:07] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -85.422950-0.002725j
[2025-08-27 14:42:27] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -85.557453+0.000793j
[2025-08-27 14:42:47] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -85.648469+0.005775j
[2025-08-27 14:43:07] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -85.429447+0.004541j
[2025-08-27 14:43:28] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -85.442810+0.007507j
[2025-08-27 14:43:48] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -85.411061+0.005217j
[2025-08-27 14:44:09] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -85.267485-0.014441j
[2025-08-27 14:44:29] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -85.498062-0.006563j
[2025-08-27 14:44:50] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -85.571105-0.003237j
[2025-08-27 14:45:10] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -85.568541-0.005091j
[2025-08-27 14:45:10] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 14:45:31] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -85.554644+0.004141j
[2025-08-27 14:45:51] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -85.651126+0.003490j
[2025-08-27 14:46:12] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -85.618583-0.002159j
[2025-08-27 14:46:32] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -85.514226+0.000459j
[2025-08-27 14:46:53] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -85.487069+0.010366j
[2025-08-27 14:47:13] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -85.628918+0.000939j
[2025-08-27 14:47:34] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -85.543810+0.006407j
[2025-08-27 14:47:54] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -85.451203+0.002883j
[2025-08-27 14:48:15] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -85.560527+0.001411j
[2025-08-27 14:48:35] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -85.596232-0.007233j
[2025-08-27 14:48:55] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -85.698734-0.001405j
[2025-08-27 14:49:16] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -85.645326-0.000566j
[2025-08-27 14:49:36] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -85.621430-0.000788j
[2025-08-27 14:49:57] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -85.635700+0.002686j
[2025-08-27 14:50:17] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -85.731557+0.000410j
[2025-08-27 14:50:38] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -85.591144-0.002050j
[2025-08-27 14:50:58] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -85.558505+0.002067j
[2025-08-27 14:51:19] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -85.597043+0.001370j
[2025-08-27 14:51:39] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -85.476856+0.005940j
[2025-08-27 14:52:00] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -85.445795-0.002867j
[2025-08-27 14:52:20] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -85.383925+0.004673j
[2025-08-27 14:52:41] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -85.457482+0.001529j
[2025-08-27 14:53:01] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -85.410337+0.002363j
[2025-08-27 14:53:22] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -85.640182-0.003881j
[2025-08-27 14:53:42] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -85.590168-0.002063j
[2025-08-27 14:54:03] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -85.419276-0.009298j
[2025-08-27 14:54:23] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -85.512722+0.000258j
[2025-08-27 14:54:44] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -85.528004+0.015841j
[2025-08-27 14:55:04] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -85.619244+0.003584j
[2025-08-27 14:55:24] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -85.403977+0.007201j
[2025-08-27 14:55:44] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -85.410351+0.004749j
[2025-08-27 14:56:05] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -85.440225-0.000601j
[2025-08-27 14:56:25] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -85.433163-0.000368j
[2025-08-27 14:56:46] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -85.385655+0.003771j
[2025-08-27 14:57:06] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -85.445675+0.009600j
[2025-08-27 14:57:27] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -85.427039-0.001600j
[2025-08-27 14:57:47] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -85.438673-0.005428j
[2025-08-27 14:58:08] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -85.515014+0.009221j
[2025-08-27 14:58:28] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -85.593616+0.006358j
[2025-08-27 14:58:49] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -85.623685-0.003735j
[2025-08-27 14:59:09] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -85.809501-0.001556j
[2025-08-27 14:59:30] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -85.744959-0.008721j
[2025-08-27 14:59:50] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -85.688408+0.001718j
[2025-08-27 15:00:11] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -85.734850+0.002741j
[2025-08-27 15:00:31] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -85.720366+0.001757j
[2025-08-27 15:00:52] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -85.710600-0.011015j
[2025-08-27 15:01:12] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -85.700001-0.010592j
[2025-08-27 15:01:33] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -85.590511-0.006580j
[2025-08-27 15:01:53] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -85.549748+0.005823j
[2025-08-27 15:02:14] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -85.591463-0.006116j
[2025-08-27 15:02:14] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 15:02:34] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -85.614311-0.006888j
[2025-08-27 15:02:54] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -85.644167+0.008640j
[2025-08-27 15:03:15] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -85.832585-0.007858j
[2025-08-27 15:03:35] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -85.609076-0.002378j
[2025-08-27 15:03:56] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -85.704572-0.000939j
[2025-08-27 15:04:16] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -85.746894-0.006961j
[2025-08-27 15:04:37] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -85.640970-0.002842j
[2025-08-27 15:04:57] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -85.614304-0.001917j
[2025-08-27 15:05:18] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -85.583412-0.003803j
[2025-08-27 15:05:38] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -85.820571-0.006102j
[2025-08-27 15:06:00] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -85.816288-0.006492j
[2025-08-27 15:06:20] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -85.686679+0.002831j
[2025-08-27 15:06:41] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -85.573779+0.008929j
[2025-08-27 15:07:01] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -85.669395-0.007510j
[2025-08-27 15:07:21] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -85.579821-0.000759j
[2025-08-27 15:07:42] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -85.559287-0.009755j
[2025-08-27 15:08:02] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -85.579044-0.002111j
[2025-08-27 15:08:23] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -85.758784-0.008592j
[2025-08-27 15:08:43] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -85.781632-0.001247j
[2025-08-27 15:09:04] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -85.716533-0.001522j
[2025-08-27 15:09:24] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -85.763533-0.001662j
[2025-08-27 15:09:45] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -85.692657-0.001063j
[2025-08-27 15:10:05] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -85.735236+0.006846j
[2025-08-27 15:10:26] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -85.577812-0.000617j
[2025-08-27 15:10:46] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -85.547128+0.000757j
[2025-08-27 15:11:07] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -85.506063-0.004506j
[2025-08-27 15:11:27] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -85.651459+0.002708j
[2025-08-27 15:11:48] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -85.752095+0.005914j
[2025-08-27 15:12:08] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -85.867721+0.007769j
[2025-08-27 15:12:29] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -85.692557-0.002016j
[2025-08-27 15:12:49] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -85.670610+0.008380j
[2025-08-27 15:13:10] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -85.734768-0.007450j
[2025-08-27 15:13:30] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -85.738970+0.001888j
[2025-08-27 15:13:50] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -85.719035+0.002273j
[2025-08-27 15:14:11] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -85.793743-0.001151j
[2025-08-27 15:14:31] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -85.672748+0.003358j
[2025-08-27 15:14:52] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -85.766096-0.002862j
[2025-08-27 15:15:12] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -85.769738+0.015907j
[2025-08-27 15:15:33] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -85.807375+0.004815j
[2025-08-27 15:15:53] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -85.761272+0.000618j
[2025-08-27 15:16:14] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -85.760019-0.001577j
[2025-08-27 15:16:34] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -85.724085-0.009900j
[2025-08-27 15:16:55] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -85.788738-0.001322j
[2025-08-27 15:17:15] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -85.848469-0.000829j
[2025-08-27 15:17:36] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -85.604120-0.006410j
[2025-08-27 15:17:56] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -85.442046-0.003047j
[2025-08-27 15:18:17] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -85.487217-0.004222j
[2025-08-27 15:18:37] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -85.554411+0.003071j
[2025-08-27 15:18:58] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -85.430170+0.001931j
[2025-08-27 15:19:18] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -85.496363-0.005032j
[2025-08-27 15:19:18] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 15:19:18] RESTART #1 | Period: 300
[2025-08-27 15:19:38] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -85.553958+0.008051j
[2025-08-27 15:19:59] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -85.544823-0.001810j
[2025-08-27 15:20:19] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -85.519695-0.001467j
[2025-08-27 15:20:40] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -85.430600-0.000223j
[2025-08-27 15:21:00] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -85.516718-0.004867j
[2025-08-27 15:21:21] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -85.592561-0.003784j
[2025-08-27 15:21:42] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -85.638855-0.001788j
[2025-08-27 15:22:02] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -85.780034+0.006123j
[2025-08-27 15:22:23] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -85.464711+0.005236j
[2025-08-27 15:22:43] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -85.514429+0.002274j
[2025-08-27 15:23:04] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -85.447720+0.000812j
[2025-08-27 15:23:24] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -85.743880-0.006344j
[2025-08-27 15:23:45] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -85.803220-0.001122j
[2025-08-27 15:24:05] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -85.544688-0.004862j
[2025-08-27 15:24:26] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -85.597637-0.005524j
[2025-08-27 15:24:46] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -85.760539+0.008680j
[2025-08-27 15:25:07] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -85.888795+0.002444j
[2025-08-27 15:25:27] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -85.878117-0.006717j
[2025-08-27 15:25:48] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -85.755928+0.003588j
[2025-08-27 15:26:08] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -85.631724-0.002480j
[2025-08-27 15:26:29] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -85.538359+0.001324j
[2025-08-27 15:26:49] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -85.563536+0.002484j
[2025-08-27 15:27:10] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -85.581595+0.005303j
[2025-08-27 15:27:30] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -85.640782-0.001208j
[2025-08-27 15:27:50] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -85.571839-0.002818j
[2025-08-27 15:28:11] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -85.454767+0.001516j
[2025-08-27 15:28:31] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -85.481275-0.001104j
[2025-08-27 15:28:52] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -85.570743+0.006988j
[2025-08-27 15:29:12] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -85.519694-0.006752j
[2025-08-27 15:29:33] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -85.699699-0.004818j
[2025-08-27 15:29:53] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -85.528493-0.006492j
[2025-08-27 15:30:14] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -85.428955-0.009298j
[2025-08-27 15:30:34] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -85.308196-0.008945j
[2025-08-27 15:30:55] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -85.414029-0.008273j
[2025-08-27 15:31:15] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -85.400541+0.002821j
[2025-08-27 15:31:36] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -85.496090-0.001173j
[2025-08-27 15:31:56] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -85.463799-0.002326j
[2025-08-27 15:32:17] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -85.460187-0.004394j
[2025-08-27 15:32:37] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -85.532640-0.001604j
[2025-08-27 15:32:58] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -85.627579-0.000114j
[2025-08-27 15:33:18] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -85.521675-0.002850j
[2025-08-27 15:33:39] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -85.581103+0.000212j
[2025-08-27 15:33:59] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -85.655988+0.008775j
[2025-08-27 15:34:20] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -85.479959+0.000615j
[2025-08-27 15:34:40] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -85.517226+0.003588j
[2025-08-27 15:35:01] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -85.433881-0.006585j
[2025-08-27 15:35:21] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -85.494024+0.007393j
[2025-08-27 15:35:42] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -85.545877+0.001692j
[2025-08-27 15:36:02] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -85.561230-0.004684j
[2025-08-27 15:36:23] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -85.502165-0.006808j
[2025-08-27 15:36:23] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 15:36:43] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -85.461674-0.003538j
[2025-08-27 15:37:04] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -85.367437+0.006414j
[2025-08-27 15:37:24] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -85.499956+0.003509j
[2025-08-27 15:37:45] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -85.381349-0.011473j
[2025-08-27 15:38:05] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -85.565404-0.001029j
[2025-08-27 15:38:25] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -85.411434+0.003648j
[2025-08-27 15:38:46] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -85.448328-0.006787j
[2025-08-27 15:39:06] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -85.622760+0.003697j
[2025-08-27 15:39:27] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -85.631495-0.002727j
[2025-08-27 15:39:47] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -85.626341+0.005534j
[2025-08-27 15:40:08] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -85.372742-0.002321j
[2025-08-27 15:40:28] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -85.401109-0.005156j
[2025-08-27 15:40:49] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -85.376712-0.004339j
[2025-08-27 15:41:09] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -85.525771+0.004155j
[2025-08-27 15:41:30] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -85.529215-0.010437j
[2025-08-27 15:41:50] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -85.627862-0.005084j
[2025-08-27 15:42:11] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -85.458949+0.006657j
[2025-08-27 15:42:31] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -85.582467-0.004647j
[2025-08-27 15:42:52] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -85.416931-0.004173j
[2025-08-27 15:43:12] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -85.534849+0.000474j
[2025-08-27 15:43:33] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -85.394973+0.011975j
[2025-08-27 15:43:53] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -85.508991-0.002876j
[2025-08-27 15:44:14] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -85.463095-0.000064j
[2025-08-27 15:44:34] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -85.516022-0.005377j
[2025-08-27 15:44:55] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -85.704140-0.005825j
[2025-08-27 15:45:15] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -85.522329-0.000618j
[2025-08-27 15:45:35] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -85.453143+0.002579j
[2025-08-27 15:45:56] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -85.591317+0.004794j
[2025-08-27 15:46:16] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -85.463539-0.001513j
[2025-08-27 15:46:37] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -85.405315+0.004884j
[2025-08-27 15:46:57] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -85.291255-0.007057j
[2025-08-27 15:47:18] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -85.523335-0.005194j
[2025-08-27 15:47:38] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -85.396531+0.000247j
[2025-08-27 15:47:59] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -85.632684-0.003372j
[2025-08-27 15:48:19] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -85.561977+0.000488j
[2025-08-27 15:48:40] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -85.577557-0.000463j
[2025-08-27 15:49:00] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -85.637403-0.014075j
[2025-08-27 15:49:21] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -85.553661+0.003810j
[2025-08-27 15:49:41] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -85.706632+0.004466j
[2025-08-27 15:50:02] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -85.579495-0.004296j
[2025-08-27 15:50:22] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -85.556172-0.009179j
[2025-08-27 15:50:42] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -85.464911-0.002230j
[2025-08-27 15:51:03] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -85.611178-0.001532j
[2025-08-27 15:51:23] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -85.591734-0.002623j
[2025-08-27 15:51:44] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -85.511003-0.008516j
[2025-08-27 15:52:04] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -85.543094-0.009609j
[2025-08-27 15:52:25] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -85.487912-0.000703j
[2025-08-27 15:52:45] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -85.553129+0.001349j
[2025-08-27 15:53:06] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -85.560666-0.001293j
[2025-08-27 15:53:26] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -85.612686+0.002912j
[2025-08-27 15:53:26] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 15:53:47] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -85.648528-0.005854j
[2025-08-27 15:54:07] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -85.670934+0.003953j
[2025-08-27 15:54:28] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -85.573709+0.001150j
[2025-08-27 15:54:48] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -85.619508-0.000608j
[2025-08-27 15:55:09] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -85.560219-0.007362j
[2025-08-27 15:55:29] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -85.504446+0.004196j
[2025-08-27 15:55:49] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -85.662509+0.002682j
[2025-08-27 15:56:10] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -85.695692+0.001117j
[2025-08-27 15:56:30] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -85.663879+0.007810j
[2025-08-27 15:56:51] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -85.734365+0.005893j
[2025-08-27 15:57:11] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -85.661442-0.001669j
[2025-08-27 15:57:32] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -85.706090-0.004457j
[2025-08-27 15:57:52] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -85.564398+0.004072j
[2025-08-27 15:58:13] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -85.728292+0.007712j
[2025-08-27 15:58:33] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -85.640490+0.000415j
[2025-08-27 15:58:54] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -85.596660+0.005315j
[2025-08-27 15:59:14] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -85.621453+0.007298j
[2025-08-27 15:59:35] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -85.595262+0.012194j
[2025-08-27 15:59:55] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -85.505360-0.001914j
[2025-08-27 16:00:16] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -85.502547+0.004435j
[2025-08-27 16:00:36] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -85.540129-0.001262j
[2025-08-27 16:00:57] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -85.697661+0.000400j
[2025-08-27 16:01:17] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -85.635189-0.008083j
[2025-08-27 16:01:38] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -85.842362-0.006284j
[2025-08-27 16:01:58] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -85.681238-0.000559j
[2025-08-27 16:02:19] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -85.815625-0.005693j
[2025-08-27 16:02:39] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -85.664777-0.001129j
[2025-08-27 16:03:00] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -85.717632-0.008472j
[2025-08-27 16:03:20] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -85.783145-0.005505j
[2025-08-27 16:03:40] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -85.620805-0.002571j
[2025-08-27 16:04:01] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -85.603692+0.001409j
[2025-08-27 16:04:21] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -85.702846+0.008066j
[2025-08-27 16:04:42] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -85.581627+0.000786j
[2025-08-27 16:05:02] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -85.603656+0.003058j
[2025-08-27 16:05:23] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -85.569028+0.004453j
[2025-08-27 16:05:43] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -85.572261-0.004198j
[2025-08-27 16:06:04] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -85.622408+0.003897j
[2025-08-27 16:06:24] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -85.609857-0.001352j
[2025-08-27 16:06:45] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -85.553938-0.002278j
[2025-08-27 16:07:05] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -85.473719-0.002205j
[2025-08-27 16:07:26] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -85.545892+0.009765j
[2025-08-27 16:07:46] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -85.590626-0.000059j
[2025-08-27 16:08:07] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -85.416966-0.001530j
[2025-08-27 16:08:27] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -85.595455-0.002614j
[2025-08-27 16:08:48] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -85.695984+0.006378j
[2025-08-27 16:09:08] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -85.708297+0.001575j
[2025-08-27 16:09:29] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -85.672831+0.006165j
[2025-08-27 16:09:49] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -85.643248+0.003134j
[2025-08-27 16:10:10] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -85.681456+0.011785j
[2025-08-27 16:10:30] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -85.500666-0.000398j
[2025-08-27 16:10:30] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 16:10:51] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -85.575277-0.009402j
[2025-08-27 16:11:11] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -85.659383+0.010470j
[2025-08-27 16:11:31] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -85.563092-0.005669j
[2025-08-27 16:11:52] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -85.695583-0.001464j
[2025-08-27 16:12:12] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -85.663499+0.009123j
[2025-08-27 16:12:33] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -85.642836+0.001771j
[2025-08-27 16:12:53] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -85.585141-0.011126j
[2025-08-27 16:13:14] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -85.391700-0.001637j
[2025-08-27 16:13:34] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -85.511404+0.004358j
[2025-08-27 16:13:55] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -85.590071+0.008145j
[2025-08-27 16:14:15] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -85.517028-0.000239j
[2025-08-27 16:14:36] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -85.450743-0.009110j
[2025-08-27 16:14:57] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -85.475437+0.004235j
[2025-08-27 16:15:17] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -85.494527+0.011903j
[2025-08-27 16:15:38] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -85.596332-0.000335j
[2025-08-27 16:15:58] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -85.525227+0.002092j
[2025-08-27 16:16:19] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -85.472196+0.001668j
[2025-08-27 16:16:39] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -85.488282-0.002427j
[2025-08-27 16:16:59] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -85.558324+0.007636j
[2025-08-27 16:17:20] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -85.450518+0.001445j
[2025-08-27 16:17:40] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -85.474187+0.002019j
[2025-08-27 16:18:01] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -85.420646-0.000927j
[2025-08-27 16:18:21] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -85.629496+0.002121j
[2025-08-27 16:18:42] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -85.416494+0.007501j
[2025-08-27 16:19:02] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -85.506195+0.002653j
[2025-08-27 16:19:23] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -85.581799-0.003505j
[2025-08-27 16:19:43] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -85.541846-0.010786j
[2025-08-27 16:20:04] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -85.545740+0.008353j
[2025-08-27 16:20:24] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -85.689669-0.000467j
[2025-08-27 16:20:45] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -85.679959-0.007787j
[2025-08-27 16:21:05] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -85.744294-0.011458j
[2025-08-27 16:21:26] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -85.658321-0.005665j
[2025-08-27 16:21:46] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -85.639266+0.008746j
[2025-08-27 16:22:07] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -85.659661-0.000106j
[2025-08-27 16:22:27] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -85.592474-0.008170j
[2025-08-27 16:22:48] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -85.575165+0.008617j
[2025-08-27 16:23:08] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -85.760779+0.007383j
[2025-08-27 16:23:29] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -85.519315-0.004125j
[2025-08-27 16:23:49] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -85.632806+0.000106j
[2025-08-27 16:24:10] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -85.510919+0.000220j
[2025-08-27 16:24:30] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -85.630336-0.001834j
[2025-08-27 16:24:50] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -85.621403-0.002642j
[2025-08-27 16:25:11] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -85.475888+0.006476j
[2025-08-27 16:25:31] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -85.480444-0.006353j
[2025-08-27 16:25:52] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -85.328583+0.007884j
[2025-08-27 16:26:12] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -85.248050+0.003873j
[2025-08-27 16:26:33] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -85.361624+0.003437j
[2025-08-27 16:26:53] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -85.501826+0.002041j
[2025-08-27 16:27:14] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -85.548947-0.007471j
[2025-08-27 16:27:34] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -85.604770-0.004820j
[2025-08-27 16:27:34] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 16:27:54] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -85.538512-0.000936j
[2025-08-27 16:28:15] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -85.684160-0.006153j
[2025-08-27 16:28:35] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -85.738320-0.002549j
[2025-08-27 16:28:56] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -85.578378+0.008417j
[2025-08-27 16:29:16] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -85.461383-0.005665j
[2025-08-27 16:29:37] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -85.643675+0.003855j
[2025-08-27 16:29:57] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -85.703273+0.005936j
[2025-08-27 16:30:18] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -85.607950+0.003194j
[2025-08-27 16:30:38] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -85.679512+0.002397j
[2025-08-27 16:30:59] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -85.702297-0.006193j
[2025-08-27 16:31:19] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -85.542769+0.005982j
[2025-08-27 16:31:40] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -85.720801-0.000577j
[2025-08-27 16:32:00] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -85.594099-0.006326j
[2025-08-27 16:32:21] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -85.566315+0.001584j
[2025-08-27 16:32:41] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -85.527860-0.004265j
[2025-08-27 16:33:02] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -85.489839-0.011114j
[2025-08-27 16:33:22] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -85.673299+0.001071j
[2025-08-27 16:33:43] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -85.726906+0.003032j
[2025-08-27 16:34:03] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -85.707635+0.017920j
[2025-08-27 16:34:24] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -85.643021+0.002915j
[2025-08-27 16:34:44] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -85.617746+0.000809j
[2025-08-27 16:35:05] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -85.674120-0.003293j
[2025-08-27 16:35:25] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -85.691561+0.000132j
[2025-08-27 16:35:46] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -85.694696+0.000100j
[2025-08-27 16:36:06] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -85.612819+0.001424j
[2025-08-27 16:36:27] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -85.693017+0.000474j
[2025-08-27 16:36:47] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -85.710501-0.003746j
[2025-08-27 16:37:08] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -85.536534+0.000873j
[2025-08-27 16:37:28] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -85.602708-0.005855j
[2025-08-27 16:37:49] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -85.571077-0.001547j
[2025-08-27 16:38:09] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -85.470193+0.005519j
[2025-08-27 16:38:30] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -85.537069-0.006350j
[2025-08-27 16:38:50] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -85.466052-0.000144j
[2025-08-27 16:39:11] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -85.441282+0.007210j
[2025-08-27 16:39:31] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -85.552242+0.003668j
[2025-08-27 16:39:52] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -85.609157+0.006332j
[2025-08-27 16:40:12] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -85.648064+0.006945j
[2025-08-27 16:40:33] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -85.654072-0.009018j
[2025-08-27 16:40:53] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -85.563963+0.006034j
[2025-08-27 16:41:14] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -85.660590-0.005124j
[2025-08-27 16:41:34] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -85.643400-0.003614j
[2025-08-27 16:41:55] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -85.786046-0.003953j
[2025-08-27 16:42:15] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -85.613775+0.007293j
[2025-08-27 16:42:35] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -85.715406-0.000803j
[2025-08-27 16:42:56] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -85.699811+0.000350j
[2025-08-27 16:43:16] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -85.621337+0.012995j
[2025-08-27 16:43:37] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -85.408731-0.000759j
[2025-08-27 16:43:57] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -85.442336-0.003239j
[2025-08-27 16:44:18] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -85.596449-0.004736j
[2025-08-27 16:44:38] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -85.660963+0.009680j
[2025-08-27 16:44:38] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 16:44:59] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -85.598430-0.001034j
[2025-08-27 16:45:19] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -85.698729-0.005003j
[2025-08-27 16:45:40] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -85.616475-0.002229j
[2025-08-27 16:46:00] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -85.689863-0.002020j
[2025-08-27 16:46:20] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -85.579171+0.001465j
[2025-08-27 16:46:41] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -85.458092-0.012757j
[2025-08-27 16:47:01] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -85.693791-0.003380j
[2025-08-27 16:47:22] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -85.613378-0.001882j
[2025-08-27 16:47:42] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -85.720017-0.005453j
[2025-08-27 16:48:03] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -85.661223+0.002670j
[2025-08-27 16:48:23] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -85.548293-0.004268j
[2025-08-27 16:48:44] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -85.540708-0.009456j
[2025-08-27 16:49:04] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -85.597904-0.007020j
[2025-08-27 16:49:25] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -85.685849+0.000143j
[2025-08-27 16:49:45] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -85.630013-0.003161j
[2025-08-27 16:50:06] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -85.586968-0.003870j
[2025-08-27 16:50:26] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -85.620290-0.002350j
[2025-08-27 16:50:47] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -85.547772-0.002207j
[2025-08-27 16:51:07] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -85.616195-0.006892j
[2025-08-27 16:51:28] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -85.633966-0.001294j
[2025-08-27 16:51:48] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -85.470042+0.003570j
[2025-08-27 16:52:09] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -85.649061+0.003747j
[2025-08-27 16:52:29] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -85.601775-0.004547j
[2025-08-27 16:52:50] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -85.628234-0.004261j
[2025-08-27 16:53:10] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -85.760716+0.005743j
[2025-08-27 16:53:31] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -85.659356-0.006081j
[2025-08-27 16:53:51] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -85.389119+0.001436j
[2025-08-27 16:54:12] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -85.388014+0.001277j
[2025-08-27 16:54:32] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -85.442591+0.000869j
[2025-08-27 16:54:53] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -85.595727-0.005778j
[2025-08-27 16:55:13] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -85.522436-0.000019j
[2025-08-27 16:55:34] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -85.562611+0.000933j
[2025-08-27 16:55:54] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -85.527452+0.007201j
[2025-08-27 16:56:15] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -85.545589+0.007034j
[2025-08-27 16:56:35] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -85.470637+0.000442j
[2025-08-27 16:56:56] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -85.533038+0.007839j
[2025-08-27 16:57:16] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -85.527988-0.001487j
[2025-08-27 16:57:37] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -85.668019-0.009428j
[2025-08-27 16:57:57] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -85.678451-0.006459j
[2025-08-27 16:58:18] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -85.646532-0.003435j
[2025-08-27 16:58:38] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -85.701711-0.002439j
[2025-08-27 16:58:59] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -85.734363-0.005898j
[2025-08-27 16:59:19] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -85.545546-0.002009j
[2025-08-27 16:59:39] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -85.591051-0.005772j
[2025-08-27 17:00:00] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -85.524145+0.004535j
[2025-08-27 17:00:20] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -85.461207-0.001845j
[2025-08-27 17:00:41] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -85.463509-0.000168j
[2025-08-27 17:01:01] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -85.402411+0.001275j
[2025-08-27 17:01:22] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -85.473485+0.003319j
[2025-08-27 17:01:42] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -85.400138-0.006478j
[2025-08-27 17:01:42] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 17:01:42] ✅ Training completed | Restarts: 1
[2025-08-27 17:01:42] ============================================================
[2025-08-27 17:01:42] Training completed | Runtime: 9263.5s
[2025-08-27 17:01:51] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 17:01:51] ============================================================
