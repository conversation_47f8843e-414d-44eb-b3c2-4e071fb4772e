[2025-08-27 22:13:14] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.01/training/checkpoints/final_GCNN.pkl
[2025-08-27 22:13:14]   - 迭代次数: final
[2025-08-27 22:13:14]   - 能量: -83.106397+0.006627j ± 0.115549
[2025-08-27 22:13:14]   - 时间戳: 2025-08-27T22:12:27.007525+08:00
[2025-08-27 22:13:25] ✓ 变分状态参数已从checkpoint恢复
[2025-08-27 22:13:25] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-27 22:13:25] ==================================================
[2025-08-27 22:13:25] GCNN for Shastry-Sutherland Model
[2025-08-27 22:13:25] ==================================================
[2025-08-27 22:13:25] System parameters:
[2025-08-27 22:13:25]   - System size: L=5, N=100
[2025-08-27 22:13:25]   - System parameters: J1=-0.0, J2=0.0, Q=1.0
[2025-08-27 22:13:25] --------------------------------------------------
[2025-08-27 22:13:25] Model parameters:
[2025-08-27 22:13:25]   - Number of layers = 4
[2025-08-27 22:13:25]   - Number of features = 4
[2025-08-27 22:13:25]   - Total parameters = 19628
[2025-08-27 22:13:25] --------------------------------------------------
[2025-08-27 22:13:25] Training parameters:
[2025-08-27 22:13:25]   - Learning rate: 0.015
[2025-08-27 22:13:25]   - Total iterations: 450
[2025-08-27 22:13:25]   - Annealing cycles: 2
[2025-08-27 22:13:25]   - Initial period: 150
[2025-08-27 22:13:25]   - Period multiplier: 2.0
[2025-08-27 22:13:25]   - Temperature range: 0.0-1.0
[2025-08-27 22:13:25]   - Samples: 4096
[2025-08-27 22:13:25]   - Discarded samples: 0
[2025-08-27 22:13:25]   - Chunk size: 2048
[2025-08-27 22:13:25]   - Diagonal shift: 0.2
[2025-08-27 22:13:25]   - Gradient clipping: 1.0
[2025-08-27 22:13:25]   - Checkpoint enabled: interval=50
[2025-08-27 22:13:25]   - Checkpoint directory: results/L=5/J2=0.00/J1=-0.00/training/checkpoints
[2025-08-27 22:13:25] --------------------------------------------------
[2025-08-27 22:13:25] Device status:
[2025-08-27 22:13:25]   - Devices model: NVIDIA H200 NVL
[2025-08-27 22:13:25]   - Number of devices: 1
[2025-08-27 22:13:25]   - Sharding: True
[2025-08-27 22:13:25] ============================================================
[2025-08-27 22:14:12] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -82.653650-0.005726j
[2025-08-27 22:14:44] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -82.924318+0.004252j
[2025-08-27 22:14:58] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -82.909673+0.002664j
[2025-08-27 22:15:12] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -82.840744+0.012765j
[2025-08-27 22:15:26] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -82.660473+0.017894j
[2025-08-27 22:15:40] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -82.453278+0.006372j
[2025-08-27 22:15:54] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -82.717011+0.011625j
[2025-08-27 22:16:08] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -82.679521+0.005679j
[2025-08-27 22:16:22] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -82.632369+0.011175j
[2025-08-27 22:16:36] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -82.679349+0.007869j
[2025-08-27 22:16:50] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -82.664409+0.008156j
[2025-08-27 22:17:04] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -82.750833-0.000194j
[2025-08-27 22:17:18] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -82.690382+0.001654j
[2025-08-27 22:17:32] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -82.583649-0.008337j
[2025-08-27 22:17:46] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -82.535995-0.003539j
[2025-08-27 22:18:00] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -82.575717+0.000919j
[2025-08-27 22:18:15] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -82.610244+0.004467j
[2025-08-27 22:18:29] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -82.609884+0.002649j
[2025-08-27 22:18:43] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -82.625208-0.003983j
[2025-08-27 22:18:57] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -82.649440+0.001280j
[2025-08-27 22:19:11] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -82.571598-0.004068j
[2025-08-27 22:19:25] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -82.616414-0.003040j
[2025-08-27 22:19:39] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -82.653218-0.006636j
[2025-08-27 22:19:53] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -82.557633+0.004125j
[2025-08-27 22:20:07] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -82.534651-0.003227j
[2025-08-27 22:20:21] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -82.722766+0.004898j
[2025-08-27 22:20:35] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -82.608021+0.000887j
[2025-08-27 22:20:49] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -82.697321+0.002950j
[2025-08-27 22:21:03] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -82.585510+0.000715j
[2025-08-27 22:21:17] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -82.590858+0.005631j
[2025-08-27 22:21:31] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -82.527324+0.004110j
[2025-08-27 22:21:46] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -82.414378-0.002147j
[2025-08-27 22:22:00] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -82.564523+0.003287j
[2025-08-27 22:22:14] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -82.600640-0.000586j
[2025-08-27 22:22:28] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -82.590834-0.000300j
[2025-08-27 22:22:42] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -82.519538+0.001148j
[2025-08-27 22:22:56] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -82.569843+0.004343j
[2025-08-27 22:23:10] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -82.567057-0.000644j
[2025-08-27 22:23:24] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -82.556286+0.005014j
[2025-08-27 22:23:38] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -82.332129+0.006168j
[2025-08-27 22:23:52] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -82.579373-0.002284j
[2025-08-27 22:24:06] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -82.414600+0.012471j
[2025-08-27 22:24:20] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -82.530307-0.009626j
[2025-08-27 22:24:34] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -82.604888-0.003198j
[2025-08-27 22:24:48] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -82.432097-0.001675j
[2025-08-27 22:25:02] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -82.400166+0.006943j
[2025-08-27 22:25:17] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -82.501378+0.004721j
[2025-08-27 22:25:31] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -82.444604+0.000719j
[2025-08-27 22:25:45] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -82.537642-0.000907j
[2025-08-27 22:25:59] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -82.481208+0.002333j
[2025-08-27 22:25:59] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-08-27 22:26:13] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -82.601332-0.000727j
[2025-08-27 22:26:27] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -82.838624+0.006247j
[2025-08-27 22:26:41] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -82.681567+0.004263j
[2025-08-27 22:26:55] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -82.660165+0.003952j
[2025-08-27 22:27:09] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -82.591928-0.007839j
[2025-08-27 22:27:23] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -82.547274-0.005861j
[2025-08-27 22:27:38] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -82.685098-0.000669j
[2025-08-27 22:27:52] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -82.688352-0.007408j
[2025-08-27 22:28:06] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -82.651232+0.004079j
[2025-08-27 22:28:20] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -82.537410-0.005630j
[2025-08-27 22:28:34] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -82.556106-0.005457j
[2025-08-27 22:28:49] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -82.571521+0.004718j
[2025-08-27 22:29:03] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -82.719243+0.000175j
[2025-08-27 22:29:17] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -82.683145+0.002153j
[2025-08-27 22:29:31] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -82.672402-0.005991j
[2025-08-27 22:29:45] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -82.605638-0.000151j
[2025-08-27 22:29:59] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -82.642333-0.000980j
[2025-08-27 22:30:13] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -82.615032-0.000974j
[2025-08-27 22:30:27] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -82.429989-0.000222j
[2025-08-27 22:30:41] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -82.493536+0.001269j
[2025-08-27 22:30:55] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -82.350828+0.004349j
[2025-08-27 22:31:09] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -82.397387-0.008490j
[2025-08-27 22:31:23] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -82.449276-0.004743j
[2025-08-27 22:31:37] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -82.714589-0.003743j
[2025-08-27 22:31:51] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -82.586357-0.000683j
[2025-08-27 22:32:05] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -82.597795+0.006151j
[2025-08-27 22:32:19] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -82.566847+0.000082j
[2025-08-27 22:32:34] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -82.541859-0.002421j
[2025-08-27 22:32:48] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -82.503308+0.008374j
[2025-08-27 22:33:02] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -82.494228-0.001539j
[2025-08-27 22:33:16] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -82.558162-0.006464j
[2025-08-27 22:33:30] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -82.403348+0.007642j
[2025-08-27 22:33:44] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -82.317881-0.000708j
[2025-08-27 22:33:58] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -82.388747-0.001804j
[2025-08-27 22:34:12] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -82.615897-0.005388j
[2025-08-27 22:34:26] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -82.671402-0.003574j
[2025-08-27 22:34:40] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -82.674501+0.008653j
[2025-08-27 22:34:54] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -82.753820+0.007716j
[2025-08-27 22:35:08] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -82.676708+0.007440j
[2025-08-27 22:35:22] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -82.475412-0.003327j
[2025-08-27 22:35:36] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -82.629252+0.001437j
[2025-08-27 22:35:50] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -82.485758+0.001561j
[2025-08-27 22:36:04] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -82.401200-0.001484j
[2025-08-27 22:36:19] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -82.422943+0.005593j
[2025-08-27 22:36:33] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -82.472659+0.005693j
[2025-08-27 22:36:47] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -82.383551-0.008677j
[2025-08-27 22:37:01] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -82.362770-0.006265j
[2025-08-27 22:37:15] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -82.621029+0.002637j
[2025-08-27 22:37:29] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -82.714105+0.000174j
[2025-08-27 22:37:43] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -82.651884-0.003061j
[2025-08-27 22:37:43] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-27 22:37:57] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -82.801515+0.000765j
[2025-08-27 22:38:11] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -82.817001+0.006501j
[2025-08-27 22:38:25] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -82.633444+0.003526j
[2025-08-27 22:38:39] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -82.614295-0.000259j
[2025-08-27 22:38:53] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -82.610090-0.001321j
[2025-08-27 22:39:07] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -82.646875-0.005230j
[2025-08-27 22:39:22] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -82.501171+0.002745j
[2025-08-27 22:39:36] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -82.462733-0.000877j
[2025-08-27 22:39:50] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -82.472701-0.003561j
[2025-08-27 22:40:04] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -82.424937+0.002957j
[2025-08-27 22:40:18] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -82.519896-0.004691j
[2025-08-27 22:40:32] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -82.456287-0.002150j
[2025-08-27 22:40:46] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -82.446465-0.005131j
[2025-08-27 22:41:00] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -82.529948-0.008484j
[2025-08-27 22:41:14] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -82.534954+0.002698j
[2025-08-27 22:41:29] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -82.492347+0.002567j
[2025-08-27 22:41:43] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -82.448810-0.006780j
[2025-08-27 22:41:57] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -82.562864+0.004419j
[2025-08-27 22:42:11] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -82.563898+0.001606j
[2025-08-27 22:42:25] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -82.564030+0.008798j
[2025-08-27 22:42:39] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -82.745298-0.003746j
[2025-08-27 22:42:53] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -82.734543+0.001373j
[2025-08-27 22:43:07] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -82.510929+0.003059j
[2025-08-27 22:43:21] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -82.445800+0.001768j
[2025-08-27 22:43:36] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -82.635458+0.006951j
[2025-08-27 22:43:50] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -82.638701-0.002282j
[2025-08-27 22:44:04] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -82.654970-0.000668j
[2025-08-27 22:44:18] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -82.692491+0.001666j
[2025-08-27 22:44:32] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -82.486901-0.001647j
[2025-08-27 22:44:46] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -82.428097-0.002350j
[2025-08-27 22:45:00] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -82.504524+0.002975j
[2025-08-27 22:45:14] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -82.366725+0.000620j
[2025-08-27 22:45:28] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -82.463386-0.002113j
[2025-08-27 22:45:42] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -82.480633+0.000935j
[2025-08-27 22:45:56] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -82.545920-0.007818j
[2025-08-27 22:46:10] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -82.570644-0.002451j
[2025-08-27 22:46:24] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -82.510751+0.001169j
[2025-08-27 22:46:38] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -82.561155-0.004813j
[2025-08-27 22:46:52] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -82.691826+0.005806j
[2025-08-27 22:47:07] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -82.612247-0.006350j
[2025-08-27 22:47:21] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -82.591926-0.006061j
[2025-08-27 22:47:35] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -82.577502-0.005142j
[2025-08-27 22:47:49] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -82.464959-0.001451j
[2025-08-27 22:48:03] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -82.400856-0.000587j
[2025-08-27 22:48:17] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -82.556118-0.000056j
[2025-08-27 22:48:31] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -82.659767-0.004773j
[2025-08-27 22:48:45] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -82.546775-0.000944j
[2025-08-27 22:48:59] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -82.433361-0.001116j
[2025-08-27 22:49:13] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -82.505852+0.000807j
[2025-08-27 22:49:27] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -82.673196+0.002422j
[2025-08-27 22:49:27] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-08-27 22:49:27] RESTART #1 | Period: 300
[2025-08-27 22:49:41] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -82.432306+0.002678j
[2025-08-27 22:49:55] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -82.371780+0.002977j
[2025-08-27 22:50:09] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -82.522338-0.003184j
[2025-08-27 22:50:23] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -82.423015+0.004887j
[2025-08-27 22:50:37] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -82.467282-0.005704j
[2025-08-27 22:50:52] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -82.516133-0.000757j
[2025-08-27 22:51:06] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -82.657938-0.000898j
[2025-08-27 22:51:20] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -82.746573+0.008177j
[2025-08-27 22:51:34] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -82.489682-0.007324j
[2025-08-27 22:51:48] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -82.439167-0.001969j
[2025-08-27 22:52:02] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -82.499030-0.001229j
[2025-08-27 22:52:16] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -82.538985-0.004313j
[2025-08-27 22:52:30] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -82.490904+0.001299j
[2025-08-27 22:52:44] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -82.430614-0.000869j
[2025-08-27 22:52:58] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -82.474135-0.000810j
[2025-08-27 22:53:12] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -82.485535+0.005082j
[2025-08-27 22:53:26] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -82.416645-0.005416j
[2025-08-27 22:53:40] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -82.407930+0.004442j
[2025-08-27 22:53:54] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -82.463753+0.001006j
[2025-08-27 22:54:09] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -82.546172+0.005129j
[2025-08-27 22:54:23] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -82.552015+0.000002j
[2025-08-27 22:54:37] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -82.568810-0.003901j
[2025-08-27 22:54:51] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -82.414734-0.002172j
[2025-08-27 22:55:05] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -82.553022+0.007173j
[2025-08-27 22:55:19] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -82.517398-0.003146j
[2025-08-27 22:55:33] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -82.672264-0.001082j
[2025-08-27 22:55:47] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -82.815765+0.001375j
[2025-08-27 22:56:01] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -82.757303-0.007129j
[2025-08-27 22:56:15] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -82.675742-0.005645j
[2025-08-27 22:56:29] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -82.716384+0.000663j
[2025-08-27 22:56:43] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -82.793730+0.000941j
[2025-08-27 22:56:57] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -82.585988+0.000488j
[2025-08-27 22:57:11] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -82.658488+0.013197j
[2025-08-27 22:57:25] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -82.594072+0.006881j
[2025-08-27 22:57:40] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -82.368529-0.001444j
[2025-08-27 22:57:54] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -82.479368-0.001043j
[2025-08-27 22:58:08] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -82.447924-0.004069j
[2025-08-27 22:58:22] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -82.495679-0.000417j
[2025-08-27 22:58:36] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -82.380561+0.006092j
[2025-08-27 22:58:50] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -82.416969+0.000483j
[2025-08-27 22:59:04] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -82.534170-0.004268j
[2025-08-27 22:59:18] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -82.400006-0.001995j
[2025-08-27 22:59:32] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -82.462212-0.009601j
[2025-08-27 22:59:46] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -82.464043+0.001005j
[2025-08-27 23:00:00] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -82.570353-0.001043j
[2025-08-27 23:00:14] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -82.466459+0.002941j
[2025-08-27 23:00:28] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -82.416553+0.001165j
[2025-08-27 23:00:42] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -82.472178-0.001797j
[2025-08-27 23:00:56] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -82.438722-0.003320j
[2025-08-27 23:01:11] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -82.441242+0.005146j
[2025-08-27 23:01:11] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-27 23:01:25] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -82.502247-0.004400j
[2025-08-27 23:01:39] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -82.452107-0.000342j
[2025-08-27 23:01:53] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -82.537317+0.001301j
[2025-08-27 23:02:07] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -82.454576+0.001338j
[2025-08-27 23:02:21] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -82.695696-0.006149j
[2025-08-27 23:02:35] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -82.564273-0.007942j
[2025-08-27 23:02:49] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -82.360280+0.000053j
[2025-08-27 23:03:03] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -82.568025-0.007810j
[2025-08-27 23:03:17] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -82.452030+0.001882j
[2025-08-27 23:03:31] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -82.620638+0.001614j
[2025-08-27 23:03:45] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -82.555587+0.001397j
[2025-08-27 23:03:59] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -82.431507+0.003210j
[2025-08-27 23:04:13] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -82.579287-0.002499j
[2025-08-27 23:04:28] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -82.505231+0.005123j
[2025-08-27 23:04:42] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -82.463961+0.001343j
[2025-08-27 23:04:56] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -82.558776-0.002809j
[2025-08-27 23:05:10] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -82.496233+0.002132j
[2025-08-27 23:05:24] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -82.448811-0.001275j
[2025-08-27 23:05:38] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -82.670861+0.003098j
[2025-08-27 23:05:52] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -82.619820+0.009914j
[2025-08-27 23:06:06] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -82.420097-0.002090j
[2025-08-27 23:06:20] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -82.457126-0.009511j
[2025-08-27 23:06:34] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -82.458751-0.006653j
[2025-08-27 23:06:48] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -82.457285-0.005481j
[2025-08-27 23:07:02] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -82.378501-0.004769j
[2025-08-27 23:07:16] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -82.479877+0.001637j
[2025-08-27 23:07:30] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -82.337103+0.002625j
[2025-08-27 23:07:44] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -82.529382-0.005060j
[2025-08-27 23:07:58] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -82.383028+0.003073j
[2025-08-27 23:08:12] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -82.476088+0.001967j
[2025-08-27 23:08:27] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -82.538815-0.000786j
[2025-08-27 23:08:41] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -82.544736+0.002887j
[2025-08-27 23:08:55] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -82.517679+0.000089j
[2025-08-27 23:09:09] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -82.630327-0.007877j
[2025-08-27 23:09:23] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -82.566641-0.002569j
[2025-08-27 23:09:37] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -82.701864+0.007946j
[2025-08-27 23:09:51] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -82.650633+0.001846j
[2025-08-27 23:10:05] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -82.568030-0.007847j
[2025-08-27 23:10:19] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -82.562844+0.003513j
[2025-08-27 23:10:33] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -82.631913+0.001486j
[2025-08-27 23:10:47] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -82.529144-0.003835j
[2025-08-27 23:11:01] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -82.526665+0.004817j
[2025-08-27 23:11:15] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -82.527382-0.001813j
[2025-08-27 23:11:29] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -82.607691-0.000550j
[2025-08-27 23:11:43] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -82.708363-0.001101j
[2025-08-27 23:11:57] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -82.656585-0.001654j
[2025-08-27 23:12:12] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -82.687704+0.006247j
[2025-08-27 23:12:26] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -82.709425-0.001045j
[2025-08-27 23:12:40] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -82.636103+0.001590j
[2025-08-27 23:12:54] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -82.607188-0.001334j
[2025-08-27 23:12:54] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-08-27 23:13:08] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -82.566900-0.000437j
[2025-08-27 23:13:22] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -82.460104+0.004413j
[2025-08-27 23:13:36] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -82.627886+0.005883j
[2025-08-27 23:13:50] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -82.337210-0.004748j
[2025-08-27 23:14:04] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -82.447283-0.002399j
[2025-08-27 23:14:18] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -82.458130+0.001921j
[2025-08-27 23:14:32] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -82.413492-0.007944j
[2025-08-27 23:14:47] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -82.371457-0.000118j
[2025-08-27 23:15:01] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -82.542552+0.008015j
[2025-08-27 23:15:15] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -82.506072+0.001196j
[2025-08-27 23:15:29] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -82.629283+0.003904j
[2025-08-27 23:15:43] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -82.606375-0.001555j
[2025-08-27 23:15:57] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -82.549618+0.002139j
[2025-08-27 23:16:11] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -82.479351+0.005926j
[2025-08-27 23:16:25] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -82.518491-0.000902j
[2025-08-27 23:16:39] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -82.520513-0.003687j
[2025-08-27 23:16:53] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -82.440269+0.002760j
[2025-08-27 23:17:07] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -82.423575-0.002946j
[2025-08-27 23:17:21] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -82.378503-0.002203j
[2025-08-27 23:17:35] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -82.418329+0.003615j
[2025-08-27 23:17:49] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -82.468690+0.008137j
[2025-08-27 23:18:03] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -82.418954+0.004798j
[2025-08-27 23:18:18] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -82.222287+0.004807j
[2025-08-27 23:18:32] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -82.363579+0.008312j
[2025-08-27 23:18:46] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -82.529548+0.000705j
[2025-08-27 23:19:00] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -82.316166+0.002698j
[2025-08-27 23:19:14] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -82.498688+0.005272j
[2025-08-27 23:19:28] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -82.479097+0.002764j
[2025-08-27 23:19:42] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -82.429736-0.001364j
[2025-08-27 23:19:56] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -82.469358+0.003006j
[2025-08-27 23:20:10] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -82.625203-0.003982j
[2025-08-27 23:20:24] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -82.635256-0.007459j
[2025-08-27 23:20:38] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -82.327139+0.003949j
[2025-08-27 23:20:52] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -82.343295+0.000113j
[2025-08-27 23:21:06] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -82.329868-0.004844j
[2025-08-27 23:21:20] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -82.527591-0.003583j
[2025-08-27 23:21:34] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -82.717760-0.005040j
[2025-08-27 23:21:48] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -82.654597-0.003820j
[2025-08-27 23:22:03] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -82.577074-0.006721j
[2025-08-27 23:22:17] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -82.739186-0.006546j
[2025-08-27 23:22:31] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -82.647958-0.005690j
[2025-08-27 23:22:45] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -82.796462+0.001640j
[2025-08-27 23:22:59] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -82.693588+0.001054j
[2025-08-27 23:23:13] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -82.636045-0.008216j
[2025-08-27 23:23:27] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -82.626973-0.000184j
[2025-08-27 23:23:41] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -82.575583-0.002862j
[2025-08-27 23:23:55] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -82.781803-0.002347j
[2025-08-27 23:24:09] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -82.543653+0.002821j
[2025-08-27 23:24:23] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -82.728502-0.003637j
[2025-08-27 23:24:37] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -82.484045+0.000192j
[2025-08-27 23:24:37] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-27 23:24:51] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -82.464579+0.001496j
[2025-08-27 23:25:05] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -82.510637-0.001284j
[2025-08-27 23:25:20] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -82.467737+0.000674j
[2025-08-27 23:25:34] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -82.592277+0.002042j
[2025-08-27 23:25:48] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -82.625662-0.001035j
[2025-08-27 23:26:02] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -82.556721-0.002093j
[2025-08-27 23:26:16] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -82.455599-0.001838j
[2025-08-27 23:26:30] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -82.409018-0.004283j
[2025-08-27 23:26:44] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -82.471867+0.009553j
[2025-08-27 23:26:58] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -82.488382+0.000808j
[2025-08-27 23:27:12] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -82.712698+0.001794j
[2025-08-27 23:27:26] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -82.557506+0.003157j
[2025-08-27 23:27:40] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -82.627131+0.004880j
[2025-08-27 23:27:54] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -82.686310-0.003258j
[2025-08-27 23:28:08] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -82.674119-0.004027j
[2025-08-27 23:28:22] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -82.547574+0.002504j
[2025-08-27 23:28:36] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -82.604318-0.006206j
[2025-08-27 23:28:51] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -82.431966-0.000262j
[2025-08-27 23:29:05] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -82.444333-0.001949j
[2025-08-27 23:29:19] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -82.437132-0.002772j
[2025-08-27 23:29:33] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -82.378846-0.001828j
[2025-08-27 23:29:47] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -82.369600+0.008187j
[2025-08-27 23:30:01] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -82.471700+0.005064j
[2025-08-27 23:30:15] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -82.407724+0.001147j
[2025-08-27 23:30:29] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -82.269976-0.004228j
[2025-08-27 23:30:43] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -82.214802+0.000699j
[2025-08-27 23:30:57] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -82.324999+0.001981j
[2025-08-27 23:31:11] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -82.289053-0.002705j
[2025-08-27 23:31:25] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -82.416244-0.003455j
[2025-08-27 23:31:39] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -82.315405-0.002645j
[2025-08-27 23:31:53] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -82.367621+0.000463j
[2025-08-27 23:32:08] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -82.595870-0.005246j
[2025-08-27 23:32:22] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -82.578535-0.007877j
[2025-08-27 23:32:36] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -82.452991+0.002174j
[2025-08-27 23:32:50] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -82.380579-0.006857j
[2025-08-27 23:33:04] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -82.563843-0.006254j
[2025-08-27 23:33:18] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -82.391339-0.001530j
[2025-08-27 23:33:32] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -82.575892-0.001538j
[2025-08-27 23:33:46] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -82.642685+0.001400j
[2025-08-27 23:34:00] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -82.851482+0.008345j
[2025-08-27 23:34:14] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -82.844311+0.002717j
[2025-08-27 23:34:28] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -82.749734-0.002329j
[2025-08-27 23:34:42] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -82.723356+0.005780j
[2025-08-27 23:34:56] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -82.613325-0.000981j
[2025-08-27 23:35:10] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -82.654885-0.000447j
[2025-08-27 23:35:24] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -82.451178+0.000168j
[2025-08-27 23:35:39] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -82.689934+0.003934j
[2025-08-27 23:35:53] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -82.493934-0.004447j
[2025-08-27 23:36:07] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -82.702306-0.000424j
[2025-08-27 23:36:21] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -82.583550-0.000740j
[2025-08-27 23:36:21] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-08-27 23:36:35] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -82.654988+0.000227j
[2025-08-27 23:36:49] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -82.625595+0.005234j
[2025-08-27 23:37:04] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -82.566746+0.005795j
[2025-08-27 23:37:18] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -82.514500+0.000115j
[2025-08-27 23:37:32] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -82.454048-0.003304j
[2025-08-27 23:37:46] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -82.454658-0.002115j
[2025-08-27 23:38:00] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -82.501138-0.001399j
[2025-08-27 23:38:14] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -82.534022-0.005514j
[2025-08-27 23:38:28] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -82.543639-0.002153j
[2025-08-27 23:38:42] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -82.466734+0.003955j
[2025-08-27 23:38:56] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -82.435334+0.001999j
[2025-08-27 23:39:10] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -82.401203+0.006657j
[2025-08-27 23:39:24] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -82.454735-0.003021j
[2025-08-27 23:39:38] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -82.537082-0.000292j
[2025-08-27 23:39:52] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -82.446353-0.002578j
[2025-08-27 23:40:06] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -82.405577+0.001434j
[2025-08-27 23:40:20] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -82.371310-0.004859j
[2025-08-27 23:40:34] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -82.272281-0.001821j
[2025-08-27 23:40:48] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -82.313085+0.002992j
[2025-08-27 23:41:03] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -82.202403+0.002714j
[2025-08-27 23:41:17] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -82.269492+0.000446j
[2025-08-27 23:41:31] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -82.414206+0.000684j
[2025-08-27 23:41:45] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -82.466515-0.002088j
[2025-08-27 23:41:59] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -82.558093-0.007310j
[2025-08-27 23:42:13] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -82.519903+0.003800j
[2025-08-27 23:42:27] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -82.519264+0.007602j
[2025-08-27 23:42:41] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -82.417793-0.000947j
[2025-08-27 23:42:55] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -82.525987+0.007767j
[2025-08-27 23:43:09] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -82.423581+0.001348j
[2025-08-27 23:43:23] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -82.411198+0.004018j
[2025-08-27 23:43:37] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -82.482394+0.003049j
[2025-08-27 23:43:51] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -82.433699+0.003291j
[2025-08-27 23:44:05] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -82.607147+0.001162j
[2025-08-27 23:44:20] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -82.295844-0.006227j
[2025-08-27 23:44:34] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -82.375058+0.003071j
[2025-08-27 23:44:48] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -82.296199-0.000821j
[2025-08-27 23:45:02] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -82.453802-0.005740j
[2025-08-27 23:45:16] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -82.498167+0.001739j
[2025-08-27 23:45:30] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -82.528467-0.006789j
[2025-08-27 23:45:44] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -82.492838+0.003480j
[2025-08-27 23:45:58] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -82.405003-0.007907j
[2025-08-27 23:46:12] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -82.440893+0.016399j
[2025-08-27 23:46:26] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -82.488100+0.003314j
[2025-08-27 23:46:40] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -82.486800+0.001485j
[2025-08-27 23:46:54] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -82.523611+0.004230j
[2025-08-27 23:47:08] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -82.420812-0.000016j
[2025-08-27 23:47:22] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -82.417887+0.000672j
[2025-08-27 23:47:37] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -82.576978-0.004058j
[2025-08-27 23:47:51] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -82.548705+0.000970j
[2025-08-27 23:48:05] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -82.395801-0.000750j
[2025-08-27 23:48:05] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-27 23:48:19] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -82.479226+0.004067j
[2025-08-27 23:48:33] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -82.360043+0.000681j
[2025-08-27 23:48:47] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -82.396703+0.001794j
[2025-08-27 23:49:01] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -82.522360-0.000061j
[2025-08-27 23:49:15] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -82.415841-0.002677j
[2025-08-27 23:49:29] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -82.503315+0.003072j
[2025-08-27 23:49:43] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -82.426940-0.002654j
[2025-08-27 23:49:58] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -82.379411-0.002728j
[2025-08-27 23:50:12] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -82.498567+0.000524j
[2025-08-27 23:50:26] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -82.333742-0.003596j
[2025-08-27 23:50:40] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -82.195762+0.004580j
[2025-08-27 23:50:54] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -82.383002-0.000361j
[2025-08-27 23:51:08] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -82.318856-0.000866j
[2025-08-27 23:51:22] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -82.487390-0.005085j
[2025-08-27 23:51:36] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -82.511204+0.002646j
[2025-08-27 23:51:51] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -82.383169-0.004443j
[2025-08-27 23:52:05] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -82.389775-0.003161j
[2025-08-27 23:52:19] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -82.542921+0.003059j
[2025-08-27 23:52:33] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -82.493341+0.001095j
[2025-08-27 23:52:47] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -82.574208-0.000269j
[2025-08-27 23:53:01] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -82.608996+0.004233j
[2025-08-27 23:53:15] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -82.498173+0.000689j
[2025-08-27 23:53:29] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -82.458748+0.004585j
[2025-08-27 23:53:43] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -82.531823-0.001292j
[2025-08-27 23:53:57] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -82.575977+0.002689j
[2025-08-27 23:54:11] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -82.617189-0.002592j
[2025-08-27 23:54:25] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -82.555615-0.005777j
[2025-08-27 23:54:39] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -82.485900+0.002945j
[2025-08-27 23:54:53] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -82.690385+0.003454j
[2025-08-27 23:55:07] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -82.479532+0.002401j
[2025-08-27 23:55:22] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -82.415788-0.007966j
[2025-08-27 23:55:36] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -82.433593+0.008018j
[2025-08-27 23:55:50] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -82.506561+0.007025j
[2025-08-27 23:56:04] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -82.364712+0.000243j
[2025-08-27 23:56:18] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -82.435567-0.004541j
[2025-08-27 23:56:32] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -82.369366+0.003257j
[2025-08-27 23:56:46] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -82.631194-0.007246j
[2025-08-27 23:57:00] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -82.526243+0.002569j
[2025-08-27 23:57:14] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -82.592647+0.003061j
[2025-08-27 23:57:28] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -82.557452+0.007302j
[2025-08-27 23:57:42] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -82.417721+0.002444j
[2025-08-27 23:57:56] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -82.555447+0.000618j
[2025-08-27 23:58:10] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -82.510408+0.003326j
[2025-08-27 23:58:24] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -82.601591+0.004605j
[2025-08-27 23:58:38] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -82.403933+0.002501j
[2025-08-27 23:58:52] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -82.571161+0.001938j
[2025-08-27 23:59:06] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -82.523603-0.005672j
[2025-08-27 23:59:20] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -82.493840-0.000767j
[2025-08-27 23:59:34] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -82.416912+0.001284j
[2025-08-27 23:59:49] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -82.667581+0.007210j
[2025-08-27 23:59:49] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-08-27 23:59:49] ✅ Training completed | Restarts: 1
[2025-08-27 23:59:49] ============================================================
[2025-08-27 23:59:49] Training completed | Runtime: 6383.6s
[2025-08-27 23:59:54] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-27 23:59:54] ============================================================
